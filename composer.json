{"name": "awardforce/platform", "description": "Award Force utilities and components.", "authors": [{"name": "kirk<PERSON>ell", "email": "<EMAIL>"}], "require": {"php": "^8.2", "beberlei/assert": "^2.4.0", "erusev/parsedown": "^1.7.1", "ezyang/htmlpurifier": "^4.9", "guzzlehttp/guzzle": "^7.0.1", "kirkbushell/eloquence": "^11.0.4", "laravel/framework": "^11.0", "laravel/helpers": "^1.2", "laravel/socialite": "^5.2", "league/flysystem-aws-s3-v3": "^3.0", "monolog/monolog": "^3.0", "propaganistas/laravel-phone": "^5.0", "spatie/laravel-html": "^3.11.2", "symfony/html-sanitizer": "^7.1", "tectonic/laravel-localisation": "^2.3.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.0", "laravel/pint": "^1.13", "league/flysystem": "^3.0", "mockery/mockery": "^1.4", "nunomaduro/collision": "^8.1", "orchestra/testbench": "^9.0", "phpunit/phpunit": "^10.0"}, "autoload": {"psr-4": {"Platform\\": "src/"}, "files": ["src/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests"}}, "config": {"preferred-install": "dist", "sort-packages": true}, "scripts": {"post-install-cmd": ["bash contrib/setup_hooks.sh"]}, "minimum-stability": "dev", "prefer-stable": true}