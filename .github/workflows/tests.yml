name: Tests

on:
  push:
    branches:
      - master
      - staging
  workflow_dispatch:

jobs:
  unittest:
    name: Tests
    container:
      image: ghcr.io/tectonic/phpunittest:php-8-2
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    runs-on: ubuntu-24.04

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Run composer install
      run: composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose

    - name: Run platform tests
      run: ant build

  slack-success:
    needs: unittest
    runs-on: ubuntu-latest
    if: success()
    container:
      image: ghcr.io/tectonic/github-slack:latest 
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}   

    steps:
    - name: Run slack
      run: python /slack-notify/slack-test.py ${{ github.run_number }} ${{ github.sha }} good ${{ secrets.SLACK_WEBHOOK_URL }} master platformtest

  slack-cancelled:
    needs: unittest
    runs-on: ubuntu-latest
    if: cancelled()
    container:
      image: ghcr.io/tectonic/github-slack:latest 
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}   

    steps:
    - name: Run slack
      run: python /slack-notify/slack-test.py ${{ github.run_number }} ${{ github.sha }} warning ${{ secrets.SLACK_WEBHOOK_URL }} master platformtest  

  slack-failure:
    needs: unittest
    runs-on: ubuntu-latest
    if: failure()
    container:
      image: ghcr.io/tectonic/github-slack:latest 
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}   

    steps:
    - name: Run slack
      run: python /slack-notify/slack-test.py ${{ github.run_number }} ${{ github.sha }} danger ${{ secrets.SLACK_WEBHOOK_URL }} master platformtest  
