<?php

namespace Tests\Strings;

use PHPUnit\Framework\Attributes\TestWith;
use Platform\Strings\HtmlSanitiser;
use Platform\Strings\Sanitiser;
use Tests\TestCase;

final class HtmlSanitiserTest extends TestCase
{
    private HtmlSanitiser $sanitiser;

    public function init(): void
    {
        $this->sanitiser = app(Sanitiser::class);
    }

    #[TestWith(['user <script>alert("hello world!")</script>', 'user '])]
    #[TestWith(['<img src="http://x.com" onerror="">', '<img src="http://x.com" />'])]
    public function testForbiddenTagsAreStripped(string $input, string $expected)
    {
        $this->assertSame($expected, $this->sanitiser->filter($input));
    }

    public function testItCleansHyperlinkTags()
    {
        $this->assertSame('Example', $this->sanitiser->clean('<a href="http://example.com" class="ok">Example</a>'));
    }

    #[TestWith(['1<2 & foo & bar & /\ buzz && 3>2'])]
    #[TestWith(['<k> <dat> </wuz> <ezy>'])]
    public function testNonHtmlTextInputsAreLeftIntact(string $input)
    {
        $this->assertEquals($input, $this->sanitiser->clean($input));
    }

    #[TestWith(['html <b>bolded</b> string', 'html bolded string'])]
    #[TestWith(['<img on>start', 'start'])]
    #[TestWith(['foo  bar', 'foo  bar'])]
    public function testTextInputsContainingHtmlAreStripped(string $input, string $expected)
    {
        $this->assertEquals($expected, $this->sanitiser->clean($input));
    }
}
