<?php

namespace Tests\Strings;

use PHPUnit\Framework\Attributes\TestWith;
use Platform\Strings\HtmlStripTags;
use Tests\TestCase;

final class HtmlStripTagsTest extends TestCase
{
    #[TestWith(['<a > space after', ' space after'])]
    #[TestWith(['<a href="yup">', ''])]
    #[TestWith(['<b>', ''])]
    #[TestWith(['before<i>after', 'beforeafter'])]
    #[TestWith(['<div id="foo">abc</div>', 'abc'])]
    #[TestWith(['<h1> valid', ' valid'])]
    #[TestWith(['<img>', ''])]
    #[TestWith(['<img src onerror>', ''])]
    #[TestWith(['<img src=x>', ''])]
    #[TestWith(['<img src="google.com">', ''])]
    #[TestWith(['< invalid > and <b>', '< invalid > and '])]
    #[TestWith(['<script >', ''])]
    #[TestWith(['<script with attrs>', ''])]
    #[TestWith(['<script>alert(123);</script>', 'alert(123);'])]
    #[TestWith(['</ScRiPt >', ''])]
    #[TestWith([' standard <a>tags</a>', ' standard tags'])]
    #[TestWith(['S<a foo=bar y>P<a foo=bar y>QR', 'SPQR'])] // Invalid tag, but detected attribute
    public function testItStripsTags(string $input, string $expected)
    {
        $this->assertSame($expected, HtmlStripTags::stripValidTags($input));
    }

    #[TestWith([null])]
    #[TestWith([''])]
    #[TestWith(['<> empty'])]
    #[TestWith(['a<b & c'])]
    #[TestWith(['a href="nope">'])]
    #[TestWith(['A&lt;B and C&gt;D'])]
    #[TestWith(['</ a>'])]
    #[TestWith(['<a-b> foo'])]
    #[TestWith(['<ayyylol>'])]
    #[TestWith(['< 19'])]
    #[TestWith(['<20'])]
    #[TestWith(['> 21'])]
    #[TestWith(['>22'])]
    #[TestWith(['<123>'])]
    #[TestWith(['<invalid attr="1">'])]
    #[TestWith(['<invalid with attr-ib=\'foo\'/>'])]
    #[TestWith(['<invalid with attr-ib=foo>'])]
    #[TestWith(['just text'])]
    #[TestWith(['< script>'])]
    #[TestWith(['</ script >'])]
    #[TestWith(['x <=a>'])]
    #[TestWith(['x<y foo=bar'])]
    #[TestWith(['x<y & / foo=bar \\ <>'])]
    #[TestWith(['<z-d> <2> <a-b> < a foo> \' " &'])]
    public function testItDoesNotDetectsInvalidTags(?string $input)
    {
        $this->assertSame($input, HtmlStripTags::stripValidTags($input));
    }

    public function testMultiParagraphStringWithoutTagsIsNotConsideredATag()
    {
        $textWithoutValidTags = '< a> b <7> <c-d> foo';
        $input = "<p>$textWithoutValidTags</p><p>$textWithoutValidTags</p>";
        $expected = $textWithoutValidTags.$textWithoutValidTags;

        $this->assertSame($expected, HtmlStripTags::stripValidTags($input));
    }
}
