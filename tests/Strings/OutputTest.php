<?php

namespace Tests\Strings;

use Facades\Platform\Html\Markdown;
use Facades\Platform\Strings\Output;
use Illuminate\Contracts\Support\Htmlable;
use Tests\TestCase;

class OutputTest extends TestCase
{
    public function test_allowed_html_is_preserved()
    {
        $this->assertSame('<a>foo</a>', Output::html('<a>foo</a>'));
        $this->assertSame('<p>&lt; a &gt;</p>', Output::html('<p>&lt; a &gt;</p>'));
        $this->assertSame('<a>safe</a> ', Output::html('<a>safe</a> <script>unsafe!</script>'));
    }

    public function test_empty_or_null_input()
    {
        $this->assertSame('', Output::text(null));
        $this->assertSame('', Output::html(''));
    }

    public function test_less_than_character_not_being_part_of_valid_tags_is_preserved()
    {
        $this->assertSame('<p>&lt;</p>', Output::html('<p>&lt;</p>'));
        $this->assertSame('<p>1&lt;2 &lt; 3</p>', Output::html('<p>1&lt;2 &lt; 3</p>'));
        $this->assertSame('<p>&lt;3&gt; &lt;4&gt;</p>', Output::html('<p>&lt;3&gt; &lt;4&gt;</p>'));
    }

    public function test_htmlable_arguments_have_escape_logic_deferred_to_them()
    {
        $class = new class implements Htmlable
        {
            public function toHtml(): string
            {
                return 'yeah, buddy!';
            }
        };

        $this->assertSame('yeah, buddy!', Output::html($class));
    }

    public function test_htmlable_arguments_with_null_return_value(): void
    {
        $class = new class implements Htmlable
        {
            public function toHtml()
            {
                return null;
            }
        };

        $this->assertSame('', Output::html($class));
    }

    public function test_from_markdown_detects_html_by_trimming(): void
    {
        $this->assertSame('<div>Raw HTML</div>', Output::html('   <div>Raw HTML</div>   '));
    }

    public function test_passing_stringable_to_html_method_still_gets_sanitised(): void
    {
        $stringable = new class('<img onError="alert(\'xss\')")') extends \Illuminate\Support\Stringable
        {
            public function __construct($value)
            {
                parent::__construct($value);
            }
        };

        $this->assertSame('&lt;img onError&#61;&#34;alert(&#039;xss&#039;)&#34;)', Output::html($stringable));
    }

    public function test_multiple_nested_brackets_are_escaped(): void
    {
        $this->assertSame('{foo}', Output::escapeBrackets('{foo}'));
        $this->assertSame('{ {foo} }', Output::escapeBrackets('{{foo}}'));
        $this->assertSame('{ { {foo} } }', Output::escapeBrackets('{{{foo}}}'));
        $this->assertSame('{ { { {foo} } } }', Output::escapeBrackets('{{{{foo}}}}'));
    }

    public function test_markdown_is_called_if_html_detected(): void
    {
        Markdown::shouldReceive('parse')->never();

        $html = '<p>This is HTML</p>And sometimes it is not end with a tag';
        $this->assertSame($html, Output::html($html));
    }

    public function test_markdown_is_called_if_no_html_detected(): void
    {
        Markdown::shouldReceive('parse')
            ->once()
            ->with('This is **markdown**')
            ->andReturn('<p>This is <strong>markdown</strong></p>');

        $this->assertSame('<p>This is <strong>markdown</strong></p>', Output::html('This is **markdown**'));
    }

    public function test_from_markdown_ignores_html_inside_af_field(): void
    {
        $content = "**Title**\n<af-field><table></table></af-field>\nText outside the af-field";

        Markdown::shouldReceive('parse')->once()->with($content);

        Output::html($content);
    }
}
