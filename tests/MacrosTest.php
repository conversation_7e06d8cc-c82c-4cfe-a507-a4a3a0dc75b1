<?php

namespace Tests;

use Eloquence\Behaviours\Slug;
use Illuminate\Support\Collection;
use Illuminate\Support\Fluent;

final class MacrosTest extends TestCase
{
    public function init()
    {
        require_once __DIR__.'/../src/macros.php';
    }

    public function test_just_returns_arrayed_pluck(): void
    {
        $collection = collect([
            new Fluent(['id' => 1, 'name' => 'aaa']),
            new Fluent(['id' => 3, 'name' => 'ccc']),
            new Fluent(['id' => 3, 'name' => 'eee']),
        ]);

        $this->assertEquals(
            $collection->pluck('name', 'id')->toArray(),
            $collection->just('name', 'id')
        );
    }

    public function test_first_with_slug_matches_string_slug(): void
    {
        $collection = collect([
            new Fluent(['id' => 1, 'slug' => new Slug('slugOne')]),
            new Fluent(['id' => 2, 'slug' => new Slug('slugTwo')]),
            new Fluent(['id' => 3, 'slug' => new Slug('slugThree')]),
        ]);

        $this->assertEquals(2, $collection->firstWithSlug('slugTwo')->id);
    }

    public function test_first_with_slug_matches_slug_object(): void
    {
        $collection = collect([
            new Fluent(['id' => 1, 'slug' => new Slug('slugOne')]),
            new Fluent(['id' => 2, 'slug' => new Slug('slugTwo')]),
            new Fluent(['id' => 3, 'slug' => new Slug('slugThree')]),
        ]);

        $this->assertEquals(2, $collection->firstWithSlug(new Slug('slugTwo'))->id);
    }

    public function test_where_type(): void
    {
        $data = new Collection([
            1,
            '2',
            3.5,
            'four',
            new Fluent,
            null,
        ]);

        $this->assertEquals([1, '2'], $data->whereType('int')->values()->all());
        $this->assertEquals([1, '2'], $data->whereType('integer')->values()->all());
        $this->assertEquals([1, '2', 3.5], $data->whereType('float')->values()->all());
        $this->assertEquals(['2', 'four'], $data->whereType('string')->values()->all());
        $this->assertEquals([new Fluent], $data->whereType(Fluent::class)->values()->all());
    }
}
