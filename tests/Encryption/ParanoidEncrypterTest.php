<?php

namespace Tests\Encryption;

use Platform\Encryption\ParanoidEncrypter;
use Tests\TestCase;

final class ParanoidEncrypterTest extends TestCase
{
    public function test_key_not_retrieved_when_unused(): void
    {
        $key = function () {
            throw new \Exception('Key function should not be called!');
        };

        new ParanoidEncrypter($key);
    }

    public function test_encrypt_decrypt(): void
    {
        $key = function () {
            return 'SomeRandomString';
        };

        $encrypter = new ParanoidEncrypter($key);
        $encrypted = $encrypter->encrypt('encrypt me');
        $decrypted = $encrypter->decrypt($encrypted);

        $this->assertEquals('encrypt me', $decrypted);
        $this->assertNotEquals($encrypted, $decrypted);
    }

    public function test_key_only_called_once(): void
    {
        $counter = 0;
        $key = function () use (&$counter) {
            $counter += 1;

            return 'SomeRandomString';
        };

        $encrypter = new ParanoidEncrypter($key);
        $encrypted = $encrypter->encrypt('encrypt me');
        $encrypter->decrypt($encrypted);
        $encrypter->encrypt('another encrypt');

        $this->assertEquals(1, $counter);
    }
}
