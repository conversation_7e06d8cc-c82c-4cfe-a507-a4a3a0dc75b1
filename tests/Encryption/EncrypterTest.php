<?php

namespace Tests\Encryption;

use Platform\Encryption\Encrypter;
use Tests\TestCase;

final class EncrypterTest extends TestCase
{
    public function test_encrypt_data(): void
    {
        $encrypter = new Encrypter($standard = new FakeEncrypter, $maximum = new FakeEncrypter);

        $response = $encrypter->encrypt('encrypt me');

        $this->assertNotNull($response);
        $this->assertStringNotContainsString('encrypt me', $response);
        $this->assertEquals('encrypt me', $standard->encrypted);
        $this->assertNull($maximum->encrypted);
    }

    public function test_decrypt_data(): void
    {
        $encrypter = new Encrypter($standard = new FakeEncrypter, $maximum = new FakeEncrypter);
        $payload = $encrypter->encrypt('decrypt me');

        $response = $encrypter->decrypt($payload);

        $this->assertEquals('decrypt me', $response);
        $this->assertEquals(base64_encode('decrypt me'), $standard->decrypted);
        $this->assertNull($maximum->decrypted);
    }

    public function test_encrypt_maximum_data(): void
    {
        $encrypter = new Encrypter($standard = new FakeEncrypter, $maximum = new FakeEncrypter);

        $response = $encrypter->maximum('encrypt me');

        $this->assertNotNull($response);
        $this->assertStringNotContainsString('encrypt me', $response);
        $this->assertEquals('encrypt me', $maximum->encrypted);
        $this->assertNull($standard->encrypted);
    }

    public function test_decrypt_maximum_data(): void
    {
        $encrypter = new Encrypter($standard = new FakeEncrypter, $maximum = new FakeEncrypter);
        $payload = $encrypter->maximum('decrypt me');

        $response = $encrypter->decrypt($payload);

        $this->assertEquals('decrypt me', $response);
        $this->assertEquals(base64_encode('decrypt me'), $maximum->decrypted);
        $this->assertNull($standard->decrypted);
    }
}
