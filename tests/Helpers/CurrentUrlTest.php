<?php

namespace Tests\Helpers;

use Tests\TestCase;

final class CurrentUrlTest extends TestCase
{
    public function test_it_returns_the_current_url_with_no_additional_parameters(): void
    {
        $this->assertSame('/', current_url());
    }

    public function test_it_returns_the_current_url_with_additional_parameters(): void
    {
        $this->assertSame('/?var1=value&var2=value', current_url(['var1' => 'value', 'var2' => 'value']));
    }
}
