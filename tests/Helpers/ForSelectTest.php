<?php

namespace Tests\Helpers;

use Illuminate\Support\Collection;
use Tests\TestCase;

final class ForSelectTest extends TestCase
{
    public function test_it_returns_a_collection_in_form_select_format(): void
    {
        $option1 = new \stdClass;
        $option1->id = 2;
        $option1->name = 'This';

        $option2 = new \stdClass;
        $option2->id = 11;
        $option2->name = 'That';

        $collection = new Collection([$option1, $option2]);

        $result = for_select_sorted($collection, null, true);

        $this->assertCount(3, $result);
        $this->assertSame('', array_shift($result));
        $this->assertSame('That', array_shift($result));
        $this->assertSame('This', array_shift($result));
    }

    public function test_it_always_sorts_empty_first(): void
    {
        $option1 = new \stdClass;
        $option1->id = 2;
        $option1->name = 'This';

        $option2 = new \stdClass;
        $option2->id = 11;
        $option2->name = '# That';

        $collection = new Collection([$option1, $option2]);

        $result = for_select_sorted($collection, null, true);

        $this->assertCount(3, $result);
        $this->assertSame('', array_shift($result));
        $this->assertSame('# That', array_shift($result));
        $this->assertSame('This', array_shift($result));
    }

    public function test_it_supports_changing_fields(): void
    {
        $option1 = new \stdClass;
        $option1->key = 2;
        $option1->value = 'This';

        $option2 = new \stdClass;
        $option2->key = 11;
        $option2->value = 'That';

        $collection = new Collection([$option1, $option2]);

        $result = for_select($collection, ['key', 'value']);

        $this->assertEquals([2 => 'This', 11 => 'That'], $result);
    }
}
