<?php

namespace Tests\Helpers;

use Tests\TestCase;

final class PrefixTest extends TestCase
{
    public function test_it_prefixes_a_string(): void
    {
        $this->assertSame('PREFIX, man!', prefix('man!', 'PREFIX, '));
    }

    public function test_it_ignores_a_string_already_prefixed(): void
    {
        $this->assertSame('already prefixed', prefix('already prefixed', 'already'));
    }
}
