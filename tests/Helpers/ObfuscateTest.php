<?php

namespace Tests\Helpers;

use Tests\TestCase;

final class ObfuscateTest extends TestCase
{
    public function test_it_obfuscates_data(): void
    {
        $decoded = $this->decode(obfuscate('raw'));

        $this->assertSame('raw', $decoded);
    }

    public function test_it_can_work_with_numerical_arrays(): void
    {
        $decoded = $this->decode(obfuscate([1, 2, 3]));

        $this->assertSame([1, 2, 3], $decoded);
    }

    private function decode($encoded)
    {
        return json_decode(base64_decode($encoded));
    }
}
