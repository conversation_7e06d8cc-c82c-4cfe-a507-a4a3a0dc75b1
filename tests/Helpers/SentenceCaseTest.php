<?php

namespace Tests\Helpers;

use Tests\TestCase;

final class SentenceCaseTest extends TestCase
{
    public function test_it_can_format_anything_to_sentence_case(): void
    {
        $sentence1 = 'The QUICK BROWN fox.';
        $sentence2 = 'the quick brown fox.';
        $sentence3 = 'the quick brown fox. the quick brown fox.';
        $sentence4 = 'the quick brown fox. the quick brown fox';
        $sentence5 = "the quick brown fox. \r\n the quick brown fox.\r\n";
        $sentence6 = 'über zweite fritzbox telefonieren';
        $sentence7 = 'bbc.com';

        $this->assertSame('The QUICK BROWN fox.', sentence_case($sentence1));
        $this->assertSame('The quick brown fox.', sentence_case($sentence2));
        $this->assertSame('The quick brown fox. The quick brown fox.', sentence_case($sentence3));
        $this->assertSame('The quick brown fox. The quick brown fox', sentence_case($sentence4));
        $this->assertSame("The quick brown fox. \r\n The quick brown fox.\r\n", sentence_case($sentence5));
        $this->assertSame('Über zweite fritzbox telefonieren', sentence_case($sentence6));
        $this->assertSame('Bbc.com', sentence_case($sentence7));
    }

    public function test_forced_sentence_case(): void
    {
        $sentence1 = 'The QUICK BROWN fox.';
        $sentence2 = 'The QUICK BROWN fox. tHe QuIcK BRoWN FoX';
        $sentence3 = 'the quick brown fox. the quick brown fox.';
        $sentence4 = 'the quick brown fox. the quick brown fox';
        $sentence5 = "the quick brown fox. \r\n the quick brown fox.\r\n";
        $sentence6 = 'über zweite fritzbox Telefonieren';
        $sentence7 = 'bbc.cOm';

        $this->assertSame('The quick brown fox.', sentence_case($sentence1, true));
        $this->assertSame('The quick brown fox. The quick brown fox', sentence_case($sentence2, true));
        $this->assertSame('The quick brown fox. The quick brown fox.', sentence_case($sentence3, true));
        $this->assertSame('The quick brown fox. The quick brown fox', sentence_case($sentence4, true));
        $this->assertSame("The quick brown fox. \r\n The quick brown fox.\r\n", sentence_case($sentence5, true));
        $this->assertSame('Über zweite fritzbox telefonieren', sentence_case($sentence6, true));
        $this->assertSame('Bbc.com', sentence_case($sentence7, true));
    }
}
