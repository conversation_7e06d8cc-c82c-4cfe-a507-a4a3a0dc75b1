<?php

namespace Tests\Helpers;

use Tests\TestCase;

final class FormToDotNotationTest extends TestCase
{
    public function test_it_can_convert_form_notation(): void
    {
        $this->assertSame('some.notation', form_to_dot_notation('some[notation]'));
    }

    public function test_it_can_work_with_complex_nested_structures(): void
    {
        $this->assertSame('all.the.things.together', form_to_dot_notation('all[the][things][together]'));
    }
}
