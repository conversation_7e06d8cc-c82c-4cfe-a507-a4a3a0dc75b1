<?php

namespace Tests\Helpers;

use Illuminate\Support\Facades\Request;
use Mockery as m;
use Tests\TestCase;

final class QueryParametersTest extends TestCase
{
    public function test_it_returns_true_if_parameters_are_provided(): void
    {
        $request = $this->mockRequest();
        $request->shouldReceive('only')->with(['param1', 'param2'])->andReturn(['param1']);

        $this->assertTrue(query_parameters(['param1', 'param2']));
    }

    public function test_it_returns_false_with_incorrect_params(): void
    {
        $request = $this->mockRequest();
        $request->shouldReceive('only')->with(['nothing'])->andReturn([]);

        $this->assertFalse(query_parameters(['nothing']));
    }

    private function mockRequest()
    {
        $request = m::mock(\Illuminate\Http\Request::class);
        $request->shouldR<PERSON>eive('setUserResolver');

        Request::swap($request);

        return $request;
    }
}
