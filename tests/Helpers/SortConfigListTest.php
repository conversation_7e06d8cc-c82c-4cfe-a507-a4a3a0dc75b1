<?php

namespace Tests\Helpers;

use Tests\TestCase;

final class SortConfigListTest extends TestCase
{
    public function test_it_can_retrieve_and_sort_configuration_options(): void
    {
        $this->app['config']->set(
            'languages',
            [
                'ar_AR' => 'Arabic',
                'cs_CZ' => 'Czech (Czech Republic)',
                'cy_GB' => 'Welsh (Great Britain)',
                'de_DE' => 'German (Germany)',
                'en_GB' => 'English (Great Britain)']
        );

        $config = sort_config_list('languages', 'languages');

        $this->assertArrayHasKey('Arabic', $config);
        $this->assertSame('languages.welsh (great britain)', array_pop($config));
    }
}
