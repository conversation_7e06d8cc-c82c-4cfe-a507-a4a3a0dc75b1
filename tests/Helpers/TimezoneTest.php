<?php

namespace Tests\Helpers;

use Tests\TestCase;

final class TimezoneTest extends TestCase
{
    public function test_returns_timezone_offset(): void
    {
        $this->assertEquals('+10:00', timezone_offset('Australia/Brisbane'));
    }

    public function test_returns_formatted_timezone(): void
    {
        $this->assertEquals('(GMT +10:00) Australia/Brisbane', full_timezone('Australia/Brisbane'));
    }

    public function test_converts_date_to_given_timezone(): void
    {
        $date = convert_date_to_timezone('2016-11-16 00:00:00', 'Australia/Sydney');

        $this->assertEquals(11, $date->hour);
    }

    public function test_converts_given_date_to_utc(): void
    {
        $date = convert_date_to_utc('2016-11-16 11:00', 'Australia/Sydney');

        $this->assertEquals(0, $date->hour);
    }
}
