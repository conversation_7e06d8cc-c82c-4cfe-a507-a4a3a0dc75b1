<?php

namespace Tests\Authorisation;

use Assert\AssertionFailedException;
use Platform\Authorisation\Signature;
use Tests\TestCase;

final class SignatureTest extends TestCase
{
    public function test_invalid_app_prefix(): void
    {
        $this->expectException(AssertionFailedException::class);

        new Signature('invalid');
    }

    public function test_valid_signature(): void
    {
        $signature = (string) new Signature('falcon', 'consumer', '<EMAIL>');

        $this->assertStringContainsString('falcon', $signature);
        $this->assertStringContainsString('consumer', $signature);
        $this->assertStringContainsString('<EMAIL>', $signature);
    }
}
