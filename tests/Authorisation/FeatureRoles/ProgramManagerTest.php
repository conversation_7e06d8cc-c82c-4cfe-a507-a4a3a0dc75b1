<?php

namespace Tests\Authorisation\FeatureRoles;

use Mockery as m;
use Platform\Authorisation\FeatureRoles\ProgramManager;
use Tests\TestCase;

final class ProgramManagerTest extends TestCase
{
    protected $consumer;

    public function init()
    {
        $this->consumer = m::mock(\stdClass::class);
    }

    public function testRoleCheck(): void
    {
        $this->consumer->shouldReceive('can')->with('update', 'EntriesAll')->once()->andReturn(true);
        $this->consumer->shouldReceive('can')->with('update', 'Chapters')->once()->andReturn(true);
        $this->consumer->shouldReceive('can')->with('update', 'Users')->once()->andReturn(true);

        $this->assertTrue(ProgramManager::appliesTo($this->consumer));
    }

    public function testPermissions(): void
    {
        $permissions = ProgramManager::permissionSet();

        $this->assertCount(3, $permissions);
    }
}
