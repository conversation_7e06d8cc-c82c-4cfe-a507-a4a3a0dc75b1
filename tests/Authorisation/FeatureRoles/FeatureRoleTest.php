<?php

namespace Tests\Authorisation\FeatureRoles;

use Mockery as m;
use Platform\Authorisation\FeatureRoles\FeatureRole;
use Platform\Authorisation\Permission;
use Tests\TestCase;

final class FeatureRoleTest extends TestCase
{
    public function init()
    {
        Permission::unguard();
    }

    public function test_applies_in_account_with_sufficient_permissions(): void
    {
        $this->assertFalse(TestRole::appliesTo(collect()));
        $this->assertTrue(TestRole::appliesTo(collect([
            new Permission(['resource' => 'ResourceA', 'action' => 'update', 'mode' => 'allow']),
            new Permission(['resource' => 'ResourceB', 'action' => 'create', 'mode' => 'allow']),
        ])));
    }

    public function test_does_not_apply_with_partial_permissions(): void
    {
        $this->assertFalse(TestRole::appliesTo(collect([
            new Permission(['resource' => 'ResourceA', 'action' => 'update', 'mode' => 'allow']),
        ])));
    }

    public function testAppliesToConsumer(): void
    {
        $consumer = m::mock(\stdClass::class);
        $consumer->shouldReceive('can')->with('update', 'ResourceA')->once()->andReturn(true);
        $consumer->shouldReceive('can')->with('create', 'ResourceB')->once()->andReturn(true);

        $this->assertTrue(TestRole::appliesTo($consumer));
    }

    public function testPermissionsReturnModelsNotArray(): void
    {
        $permissions = (new TestRole)->permissions();

        $this->assertEquals(2, count($permissions));
        $this->assertEquals('ResourceA', $permissions[0]->resource);
        $this->assertEquals('update', $permissions[0]->action);
        $this->assertEquals('allow', $permissions[0]->mode);
        $this->assertEquals('ResourceB', $permissions[1]->resource);
        $this->assertEquals('create', $permissions[1]->action);
        $this->assertEquals('allow', $permissions[1]->mode);
    }
}

class TestRole extends FeatureRole
{
    /**
     * Permissions required to have this feature role.
     *
     * @return mixed
     */
    public static function permissionSet()
    {
        return [
            ['ResourceA', 'update', 'allow'],
            ['ResourceB', 'create', 'allow'],
        ];
    }
}
