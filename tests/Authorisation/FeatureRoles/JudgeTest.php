<?php

namespace Tests\Authorisation\FeatureRoles;

use Mockery as m;
use Platform\Authorisation\FeatureRoles\Judge;
use Tests\TestCase;

final class JudgeTest extends TestCase
{
    protected $consumer;

    public function init()
    {
        $this->consumer = m::mock(\stdClass::class);
    }

    public function testRoleCheck(): void
    {
        $this->consumer->shouldReceive('can')->with('create', 'ScoresOwner')->once()->andReturn(true);

        $this->assertTrue(Judge::appliesTo($this->consumer));
    }

    public function testPermissions(): void
    {
        $permissions = Judge::permissionSet();

        $this->assertCount(1, $permissions);
    }
}
