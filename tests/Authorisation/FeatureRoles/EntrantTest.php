<?php

namespace Tests\Authorisation\FeatureRoles;

use Mockery as m;
use Platform\Authorisation\FeatureRoles\Entrant;
use Tests\TestCase;

final class EntrantTest extends TestCase
{
    protected $consumer;

    public function init()
    {
        $this->consumer = m::mock(\stdClass::class);
    }

    public function testRoleCheck(): void
    {
        $this->consumer->shouldReceive('can')->with('view', 'EntriesOwner')->once()->andReturn(true);

        $this->assertTrue(Entrant::appliesTo($this->consumer));
    }

    public function testPermissions(): void
    {
        $permissions = Entrant::permissionSet();

        $this->assertCount(1, $permissions);
    }
}
