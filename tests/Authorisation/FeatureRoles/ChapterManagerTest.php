<?php

namespace Tests\Authorisation\FeatureRoles;

use Mockery as m;
use Platform\Authorisation\Consumer;
use Platform\Authorisation\FeatureRoles\ChapterManager;
use Tests\TestCase;

final class ChapterManagerTest extends TestCase
{
    protected $consumer;

    public function init()
    {
        $this->consumer = m::mock(Consumer::class);
    }

    public function testRoleCheck(): void
    {
        $this->consumer->shouldReceive('isChapterLimited')->once()->andReturn(false);

        $this->assertFalse(ChapterManager::appliesTo($this->consumer));

        $this->consumer->shouldReceive('isChapterLimited')->once()->andReturn(true);
        $this->consumer->shouldReceive('cannot')->with('create', 'Chapters')->once()->andReturn(true);
        $this->consumer->shouldReceive('can')->with('view', 'EntriesAll')->once()->andReturn(true);

        $this->assertTrue(ChapterManager::appliesTo($this->consumer));
    }

    private function createRole(array $options)
    {
        $role = m::mock(\stdClass::class);

        foreach ($options as $option => $value) {
            $role->{$option} = $value;
        }

        return $role;
    }

    public function testPermissions(): void
    {
        $permissions = ChapterManager::permissionSet();

        $this->assertCount(2, $permissions);
    }
}
