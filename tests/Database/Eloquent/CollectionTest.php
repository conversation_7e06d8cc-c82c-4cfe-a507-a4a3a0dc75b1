<?php

namespace Tests\Database\Eloquent;

use Platform\Database\Eloquent\Collection;
use Tests\TestCase;

final class CollectionTest extends TestCase
{
    public function test_it_releases_all_model_events(): void
    {
        $model1 = new ModelStub;
        $model1->raise(new class
        {
        });

        $model2 = new ModelStub;
        $model2->raise(new class
        {
        });

        $model3 = new ModelStub;

        $model4 = new ModelStub;
        $model4->raise(new class
        {
        });
        $model4->raise(new class
        {
        });
        $model4->raise(new class
        {
        });

        $collection = new Collection([$model1, $model2, $model3, $model4]);

        $this->assertCount(5, $collection->releaseEvents());
    }
}
