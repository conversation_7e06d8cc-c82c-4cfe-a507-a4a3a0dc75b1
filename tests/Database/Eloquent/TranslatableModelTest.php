<?php

namespace Tests\Database\Eloquent;

use Mockery as m;
use Platform\Authorisation\Consumer;
use Platform\Language\Language;
use Tests\TestCase;

final class TranslatableModelTest extends TestCase
{
    private $model;

    public function init()
    {
        $this->model = new TranslatableModelStub;
    }

    public function test_it_can_retrieve_translations(): void
    {
        $this->model->setTranslated(['en_GB' => ['name' => '<PERSON> Wayne']]);

        $this->assertSame('Master Wayne', $this->model->getTranslation('name', 'en_GB'));
    }

    public function test_it_can_check_if_a_translation_exists(): void
    {
        $this->model->setTranslated(['en_GB' => ['title' => 'The Lord of the Rings']]);

        $this->assertTrue($this->model->hasTranslation('title', 'en_GB'));
    }

    public function test_has_translation_defaults_to_fallback_language(): void
    {
        $this->model->setTranslated(['en_GB' => ['title' => "Ender's Game"]]);

        $this->assertEquals("Ender's Game", $this->model->getTranslation('title', 'en_GB'));
        $this->assertEquals(null, $this->model->getTranslation('title', 'pl_PL'));

        $this->assertTrue($this->model->hasTranslation('title', 'en_GB'));
        $this->assertFalse($this->model->hasTranslation('title', 'pl_PL'));
        $this->assertTrue($this->model->hasTranslation('title', 'pl_PL', 'en_GB'));

        $this->model->setTranslated(['pl_PL' => ['title' => 'Gra Endera']]);

        $this->assertTrue($this->model->hasTranslation('title', 'pl_PL'));
        $this->assertTrue($this->model->hasTranslation('title', 'pl_PL', 'en_GB'));
        $this->assertEquals('Gra Endera', $this->model->getTranslation('title', 'pl_PL'));
    }

    public function test_it_returns_true_when_a_translation_isset(): void
    {
        $consumer = m::mock(Consumer::class);
        Language::setLanguages(['en_GB' => 'English (United Kingdom)']);
        $consumer->shouldReceive('strictLanguage')->once()->andReturn(new Language('en_GB'));
        $consumer->code = 'en_GB';

        $this->model->setConsumer($consumer);
        $this->model->setTranslated(['en_GB' => ['model' => 'Kate Upton']]);

        $this->assertTrue(isset($this->model->model));
        $this->assertFalse(isset($this->model->whatever));
    }
}
