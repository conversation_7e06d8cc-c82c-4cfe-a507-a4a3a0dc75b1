<?php

namespace Tests\Database\Eloquent;

use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Eloquent\Enums\TrashedMode;
use Platform\Database\Eloquent\HasQueryBuilder;
use Platform\Database\Eloquent\InvalidQuery;
use Platform\Database\Eloquent\Model;
use Platform\Database\Eloquent\Repository;
use Platform\Database\NoRecordFound;
use Ramsey\Uuid\Uuid;
use Tests\Acceptance;
use Tests\TestCase;

#[Acceptance]
class HasQueryBuilderTest extends TestCase
{
    private QueryBuilderRepository $repository;

    public function init()
    {
        $this->repository = new QueryBuilderRepository(new BuilderModel);
    }

    public function test_new_query_returns_a_builder_object()
    {
        $this->repository->fields(['id']);

        $this->assertInstanceOf(Builder::class, $this->repository->getBuilderQuery());
    }

    public function test_invalid_query_exception_is_thrown_when_no_select_clause_is_provided()
    {
        $this->expectException(InvalidQuery::class);

        $this->repository->get();
    }

    public function test_queries_can_be_paginated()
    {
        BuilderModel::create();
        BuilderModel::create();

        $results = $this->repository->fields(['id'])->paginate(10);

        $this->assertInstanceOf(Paginator::class, $results);
        $this->assertCount(2, $results->items());
    }

    public function test_all_results_can_be_fetched()
    {
        BuilderModel::create();

        $results = $this->repository->fields(['id'])->get();

        $this->assertInstanceOf(Collection::class, $results);
        $this->assertCount(1, $results);
    }

    public function test_a_count_of_matched_results_is_returned()
    {
        BuilderModel::create();
        BuilderModel::create();
        BuilderModel::create();

        $results = $this->repository->fields(['id'])->count();

        $this->assertSame(3, $results);
    }

    public function test_fields_can_be_retrieved_and_sql_specific_syntax_is_wrapped_inside_db_raw_methods()
    {
        $this->repository->fields(['users.id', 'COUNT(users.id)', 'name AS other_name']);

        $columns = $this->repository->getBuilderQuery()->getQuery()->getColumns();

        $this->assertSame('users.id', $columns[0]);
        $this->assertSame('COUNT(users.id)', $columns[1]);
        $this->assertSame('name AS other_name', $columns[2]);
        // ^ The assertion above might look odd, but I couldn't find a way to test and ensure that the field
        // being selected provided an Expression object - by the time I query for them, they've been converted to
        // string values. In either case, the assertion (and it's associated value) is correct, and is what we
        // expect to see. //- Kirk
    }

    public function test_single_results_can_be_fetched()
    {
        $first = BuilderModel::create(['title' => 'same']);

        BuilderModel::create(['title' => 'same']);
        BuilderModel::create(['title' => 'same']);

        $found = $this->repository->fields(['id'])->first();

        $this->assertSame($first->id, $found->id);
    }

    public function test_object_existence_can_be_checked()
    {
        BuilderModel::create(['title' => 'one']);
        BuilderModel::create(['title' => 'two']);

        $this->assertTrue($this->repository->whereTitle('one')->exists());
        $this->assertTrue($this->repository->whereTitle('two')->exists());
        $this->assertFalse($this->repository->whereTitle('three')->exists());
    }

    public function test_relationships_can_be_eager_loaded()
    {
        $this->repository->with('comments');

        $this->assertArrayHasKey('comments', $this->repository->getBuilderQuery()->getEagerLoads());
    }

    public function test_sort_clause_can_be_added()
    {
        $this->repository->sort('id', 'desc');

        $this->assertSame('id', $this->repository->getBuilderQuery()->getQuery()->orders[0]['column']);
        $this->assertSame('desc', $this->repository->getBuilderQuery()->getQuery()->orders[0]['direction']);
    }

    public function test_group_by_clause_can_be_added()
    {
        $this->repository->groupBy('id');

        $this->assertSame('id', $this->repository->getBuilderQuery()->getQuery()->groups[0]);
    }

    public function test_it_does_not_load_soft_deleted_records_by_default(): void
    {
        BuilderModel::create(['title' => 'same']);
        $softDeleted = BuilderModel::create(['title' => 'other']);
        $softDeleted->delete();
        $results = $this->repository->fields(['title'])->get();

        $this->assertCount(1, $results);
        $this->assertSame('same', $results->first()->title);
    }

    public function test_it_can_load_soft_deleted_records(): void
    {
        BuilderModel::create(['title' => 'same']);
        $softDeleted = BuilderModel::create(['title' => 'other']);
        $softDeleted->delete();
        $results = $this->repository->fields(['title'])->trashed(TrashedMode::All)->get();

        $this->assertCount(2, $results);
    }

    public function test_it_can_load_only_soft_deleted_records(): void
    {
        BuilderModel::create(['title' => 'one']);
        BuilderModel::create(['title' => 'same'])->delete();
        BuilderModel::create(['title' => 'other'])->delete();

        $results = $this->repository->fields(['title'])->trashed(TrashedMode::Only)->get();

        $this->assertCount(2, $results);
    }

    public function test_it_doesnt_load_soft_deleted_records_when_trashed_mode_is_none(): void
    {
        BuilderModel::create(['title' => 'same']);
        $softDeleted = BuilderModel::create(['title' => 'other']);
        $softDeleted->delete();
        $results = $this->repository->fields(['title'])->trashed(TrashedMode::None)->get();

        $this->assertCount(1, $results);
    }

    public function test_it_respects_repository_query_overrides(): void
    {
        $repository = new QueryBuilderRepositoryWithConstraint(new BuilderModel);
        $repository->whereTitle('same');

        $wheres = $repository->getBuilderQuery()->getQuery()->wheres;

        $this->assertCount(2, $wheres);
        $this->assertSame('custom_constraint', $wheres[0]['column']);
        $this->assertSame('custom_value', $wheres[0]['value']);
        $this->assertSame('same', $wheres[1]['value']);
    }

    public function test_pluck_returns_collection_of_values_for_column(): void
    {
        BuilderModel::create(['title' => 'one']);
        BuilderModel::create(['title' => 'two']);
        BuilderModel::create(['title' => 'three']);

        $expected = collect(['one', 'two', 'three']);

        $result = $this->repository->pluck('title');

        $this->assertEquals($expected, $result);
    }

    public function test_pluck_returns_collection_of_values_for_column_with_key(): void
    {
        BuilderModel::create(['title' => 'one']);
        BuilderModel::create(['title' => 'two']);
        BuilderModel::create(['title' => 'three']);

        $expected = collect([1 => 'one', 2 => 'two', 3 => 'three']);

        $result = $this->repository->pluck('title', 'id');

        $this->assertEquals($expected, $result);
    }

    public function test_pluck_returns_empty_collection_when_no_results(): void
    {
        $result = $this->repository->pluck('title');

        $this->assertEquals(collect(), $result);
    }

    public function test_just_returns_array_of_values_for_column(): void
    {
        BuilderModel::create(['title' => 'one']);
        BuilderModel::create(['title' => 'two']);
        BuilderModel::create(['title' => 'three']);

        $expected = ['one', 'two', 'three'];

        $result = $this->repository->just('title');

        $this->assertEquals($expected, $result);
    }

    public function test_just_returns_array_of_values_for_column_with_key(): void
    {
        BuilderModel::create(['title' => 'one']);
        BuilderModel::create(['title' => 'two']);
        BuilderModel::create(['title' => 'three']);

        $expected = [1 => 'one', 2 => 'two', 3 => 'three'];

        $result = $this->repository->just('title', 'id');

        $this->assertEquals($expected, $result);
    }

    public function test_require_throws_exception_when_no_record_is_found()
    {
        BuilderModel::create(['title' => 'one']);

        $this->expectException(NoRecordFound::class);

        $this->assertSame('one', $this->repository->fields(['title'])->whereTitle('one')->require()->title);

        $this->repository->fields(['title'])->whereTitle('does not exist')->require();
    }

    public function test_pluck_resets_the_query_after_execution(): void
    {
        $this->repository->fields(['title']);
        $this->repository->pluck('title');

        $this->repository->fields(['id']);

        $this->assertCount(1, $this->repository->getBuilderQuery()->getQuery()->columns);
        $this->assertEquals('id', $this->repository->getBuilderQuery()->getQuery()->columns[0]);
    }

    public function test_primary_uses_correct_primary_key(): void
    {
        $model = new UuidBuilderModel;
        $repository = new QueryBuilderRepository($model);
        $uuid1 = Uuid::uuid4();
        $uuid2 = Uuid::uuid4();
        $repository->primary($uuid1, $uuid2);

        $wheres = $repository->getBuilderQuery()->getQuery()->wheres;

        $this->assertCount(1, $wheres);
        $this->assertSame('In', $wheres[0]['type']);
        $this->assertSame($model->getQualifiedKeyName(), $wheres[0]['column']);
        $this->assertEqualsCanonicalizing([$uuid1->toString(), $uuid2->toString()], $wheres[0]['values']);
    }

    public function test_primary_can_query_single_value(): void
    {
        BuilderModel::create(['id' => 1, 'title' => 'first']);
        BuilderModel::create(['id' => 2, 'title' => 'second']);

        $result = $this->repository->fields(['id', 'title'])->primary(2)->first();

        $this->assertInstanceOf(BuilderModel::class, $result);
        $this->assertEquals(2, $result->id);
        $this->assertEquals('second', $result->title);
    }

    public function test_primary_can_query_multiple_values(): void
    {
        BuilderModel::create(['id' => 1, 'title' => 'first']);
        BuilderModel::create(['id' => 2, 'title' => 'second']);
        BuilderModel::create(['id' => 3, 'title' => 'third']);

        $result = $this->repository->fields(['id', 'title'])->primary(1, 2)->get();

        $this->assertCount(2, $result);
        $this->assertEquals('first', $result[0]->title);
        $this->assertEquals('second', $result[1]->title);
    }

    public function test_objects_can_be_removed()
    {
        $toKeep = BuilderModel::create(['title' => 'toKeep']);
        $toDelete = BuilderModel::create(['title' => 'toDelete']);

        $deleted = $this->repository->whereTitle('toDelete')->remove();

        $this->assertEquals(1, $deleted);
        $this->assertNull($toKeep->fresh()->deletedAt);
        $this->assertNotNull($toDelete->fresh()->deletedAt);
    }
}

class QueryBuilderRepository extends Repository implements BuilderRepository
{
    use HasQueryBuilder;

    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function whereTitle(string $title): self
    {
        $this->query()->whereTitle($title);

        return $this;
    }

    public function getQuery()
    {
        return $this->model->newInstance()->newQuery();
    }

    public function getBuilderQuery(): Builder
    {
        return $this->query();
    }
}

class BuilderModel extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'title',
    ];
}

class UuidBuilderModel extends Model
{
    protected $primaryKey = 'uuid';
}

class QueryBuilderRepositoryWithConstraint extends QueryBuilderRepository
{
    public function getQuery()
    {
        return $this->model->newInstance()->newQuery()
            ->where('custom_constraint', '=', 'custom_value');
    }
}
