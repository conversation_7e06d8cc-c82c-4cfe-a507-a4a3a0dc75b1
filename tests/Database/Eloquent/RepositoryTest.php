<?php

namespace Tests\Database\Eloquent;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Mockery as m;
use Platform\Database\Eloquent\Collection;
use Platform\Database\Eloquent\Model;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\SearchFilterCollection;
use Platform\Support\Exceptions\MethodNotFoundException;
use Tests\TestCase;

final class RepositoryTest extends TestCase
{
    /**
     * @var RepositoryStub
     */
    private $repository;

    public function init()
    {
        // Use the eloquent representation, as it is an eloquent repository after all that we're testing.
        // We're also using the account repository, because it ticks all required boxes for testing.
        $this->repository = new RepositoryStub;
    }

    public function test_it_can_initiative_new_model_objects(): void
    {
        $model = new ModelStub;

        $this->repository->setModel($model);

        $newModel = $this->repository->getNew();

        $this->assertInstanceOf(ModelStub::class, $newModel);
        $this->assertEquals($model, $newModel);
    }

    public function test_it_can_check_if_any_records_exist(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('exists')->andReturn(true);

        $this->assertTrue($this->repository->hasAny());
    }

    public function test_it_can_search_for_all_model_objects_based_on_ids_and_include_trashed_objects(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('withTrashed')->andReturn($query);
        $query->shouldReceive('whereIn')->with('id', [1, 2, 3])->once()->andReturn($query);
        $query->shouldReceive('get')->andReturn('results');

        $this->assertSame('results', $this->repository->getByIdsWithTrashed([1, 2, 3]));
    }

    public function test_it_throws_exception_when_no_result(): void
    {
        $this->expectException(ModelNotFoundException::class);

        $query = $this->initMockQuery();
        $query->shouldReceive('where')->with('created_at', '=', 'bogus value')->once()->andReturn($query);
        $query->shouldReceive('get')->once()->andReturn(new Collection);

        $this->repository->requireByCreatedAt('bogus value');
    }

    public function test_it_requires_a_single_model(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('where')->with('id', '=', 1)->once()->andReturn($query);
        $query->shouldReceive('get')->once()->andReturn(new Collection([new ModelStub]));

        $this->assertNotNull($this->repository->requireById(1));
    }

    public function test_it_returns_a_single_model(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('where')->with('id', '=', 1)->once()->andReturn($query);
        $query->shouldReceive('first')->once()->andReturn(new ModelStub);

        $this->assertNotNull($this->repository->getOneBy('id', 1));
    }

    public function test_retrieval_of_all_records(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('get')->once()->andReturn(new Collection([new ModelStub]));

        $this->assertCount(1, $this->repository->getAll());
    }

    public function test_save_with_no_arguments(): void
    {
        $this->expectException(\Exception::class);
        $this->repository->saveAll();
    }

    public function test_save_of_all_records(): void
    {
        $model = m::mock(new ModelStub);
        $model->shouldReceive('getDirty')->once()->andReturn(['attribute']);
        $model->shouldReceive('save')->once();

        $this->repository->saveAll($model);
    }

    public function test_it_can_retrieve_models_by_slug(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('whereSlug')->once()->with('slug')->andReturn($query);
        $query->shouldReceive('whereNotNull')->andReturn($query);
        $query->shouldReceive('where')->with('slug', '!=', '')->andReturn($query);
        $query->shouldReceive('first')->andReturn('model');

        $this->assertSame('model', $this->repository->getBySlug('slug'));
    }

    public function test_it_can_search_for_model_objects_from_a_search_filter_collection(): void
    {
        $filter = new class implements SearchFilter
        {
            public function applyToEloquent($query)
            {
                return $query->doStuff();
            }
        };

        $filters = new SearchFilterCollection;
        $filters->add($filter);
        $filters->add($filter);

        $query = $this->initMockQuery();
        $query->shouldReceive('doStuff')->times(4)->andReturn($query);
        $query->shouldReceive('paginate')->once();
        $query->shouldReceive('get')->once();
        $query->shouldReceive('preventLazyLoadingInColumnator')->andReturnSelf();

        $this->repository->getByFilters($filters, true);
        $this->repository->getByFilters($filters, false);
    }

    public function test_it_can_search_for_model_objects_from_a_search_limit_filter_collection(): void
    {
        $filter = new class implements SearchFilter
        {
            public function applyToEloquent($query)
            {
                return $query->doStuff();
            }
        };

        $filters = new SearchFilterCollection;
        $filters->add($filter);
        $filters->add($filter);

        $query = $this->initMockQuery();
        $query->shouldReceive('doStuff')->times(2)->andReturn($query);
        $query->shouldReceive('paginate')->once();
        $query->shouldReceive('preventLazyLoadingInColumnator')->andReturnSelf();

        $this->repository->getByFiltersLimit($filters);
    }

    public function test_it_can_retrieve_ids_based_on_search_filters(): void
    {
        $filter = new class implements SearchFilter
        {
            public function applyToEloquent($query)
            {
                return $query;
            }
        };

        $filters = new SearchFilterCollection;
        $filters->add($filter);

        $query = $this->initMockQuery();
        $query->shouldReceive('pluck')->with('id')->once()->andReturn([1, 2, 3]);

        $this->assertSame([1, 2, 3], $this->repository->getIdsByFilters($filters));
    }

    public function test_it_returns_a_single_model_by_id(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('where')->with('id', '=', 1)->once()->andReturn($query);
        $query->shouldReceive('get')->andReturn(new \Illuminate\Support\Collection(['model']));

        $this->assertSame('model', $this->repository->getById(1));
    }

    public function test_it_can_retrieve_many_models_by_slug(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('whereIn')->once()->with('slug', ['slug1', 'slug2'])->andReturn($query);
        $query->shouldReceive('get')->andReturn('models');

        $this->assertSame('models', $this->repository->getBySlugs(['slug1', 'slug2']));
    }

    public function test_model_retrieval(): void
    {
        $this->repository->setModel(new ModelStub);

        $this->assertInstanceOf(Model::class, $this->repository->getModel());
    }

    public function test_query_of_translatable_fields(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('where')->with('name', '=', 'Account')->once()->andReturn($query);
        $query->shouldReceive('get')->once()->andReturn(new Collection);

        $account = $this->repository->getByName('Account');

        $this->assertNotNull($account);
    }

    public function test_it_deletes_the_provided_objects(): void
    {
        $model = m::spy('model');

        $this->repository->delete($model);
        $this->repository->delete($model, true);

        $model->shouldHaveReceived('delete')->once();
        $model->shouldHaveReceived('forceDelete')->once();
    }

    public function test_it_updates_the_object(): void
    {
        $model = m::mock(new TranslatableModelStub)->makePartial();

        $model->shouldReceive('fill')->with(['attribute' => 'value'])->once();
        $model->shouldReceive('save')->once();

        $this->repository->update($model, ['attribute' => 'value']);
    }

    public function test_it_throws_exception_for_invalid_method(): void
    {
        $this->expectException(MethodNotFoundException::class);

        $this->repository->lksjefljsdf();
    }

    public function test_it_restores_the_models(): void
    {
        $model = m::spy('model');
        $models = [$model];

        $this->repository->restore($models);

        $model->shouldHaveReceived('restore')->once();
    }

    public function test_it_returns_an_existing_local_id_when_calculating(): void
    {
        $model = new ModelStub;
        $model->localId = 2;

        $this->assertSame(2, $this->repository->calculateLocalId($model));
    }

    public function test_it_can_calculate_local_id(): void
    {
        $model = new ModelStub;

        $query = $this->initMockQuery();
        $query->shouldReceive('whereNotNull')->with('local_id')->andReturn($query);
        $query->shouldReceive('max')->with('local_id')->andReturn(1);

        // Increments model by 1
        $this->assertEquals(2, $this->repository->calculateLocalId($model));
    }

    public function test_it_can_retrieve_trashed_objects_by_ids(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('onlyTrashed->whereIn->get')->andReturn('result');

        $this->assertEquals('result', $this->repository->getTrashedByIds([1, 2]));
    }

    public function test_it_returns_only_trashed_ids(): void
    {
        $this->initMockQuery()->shouldReceive('onlyTrashed->pluck->toArray')->andReturn([11, 12]);

        $this->assertSame([11, 12], $this->repository->getTrashedIds());
    }

    public function test_it_can_return_all_objects_that_match_the_provided_ids(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('whereIn')->with('id', [1, 2, 3])->once()->andReturn($query);
        $query->shouldReceive('get')->andReturn('models');

        $this->assertSame('models', $this->repository->getByIds([1, 2, 3]));
    }

    public function test_it_can_count_all_objects_in_database(): void
    {
        $this->initMockQuery()->shouldReceive('count')->andReturn(5);

        $this->assertSame(5, $this->repository->countAll());
    }

    public function test_it_returns_all_ids_in_the_database(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('pluck')->with('id')->andReturn($query);
        $query->shouldReceive('toArray')->andReturn([1, 5]);

        $this->assertSame([1, 5], $this->repository->getIds());
    }

    public function test_it_can_delete_all_objects_from_a_season(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('whereSeasonId')->once()->with(1)->andReturn($query);
        $query->shouldReceive('delete');

        $this->repository->deleteAllFromSeason(1);
    }

    public function test_get_with_lock(): void
    {
        $this->repository->setModel($model = m::mock(new ModelStub));

        $model->shouldReceive('getConnection->transaction')
            ->with(m::type('callable'))
            ->andReturnUsing(function (callable $callback) {
                return $callback();
            });

        $model->shouldReceive('newQuery->lockForUpdate->find')->with(1)
            ->andReturn($lockedModel = new ModelStub);

        $result = $this->repository->getWithLock(1, function (ModelStub $model) use ($lockedModel) {
            $this->assertSame($lockedModel, $model);
        });

        $this->assertSame($lockedModel, $result);
    }

    public function test_it_knows_when_all_ids_are_in_the_database(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('whereIn')->with('id', [1, 2, 3])->once()->andReturn($query);
        $query->shouldReceive('count')->andReturn('3');

        $this->assertTrue($this->repository->hasAllIds([1, 2, 3]));
    }

    public function test_it_knows_when_not_all_ids_are_in_the_database(): void
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('whereIn')->with('id', [1, 2, 3, 4])->once()->andReturn($query);
        $query->shouldReceive('count')->andReturn('3');

        $this->assertFalse($this->repository->hasAllIds([1, 2, 3, 4]));
    }

    /**
     * Initialises the model and setups a new query instance for mocking out clauses and conditions.
     *
     * @return mock
     */
    private function initMockQuery()
    {
        $query = m::mock('query');

        $model = m::mock(new ModelStub);
        $model->shouldReceive('newInstance')->andReturn($model);
        $model->shouldReceive('newQuery')->andReturn($query);
        $model->shouldReceive('getTable')->andReturn('table');

        $this->repository->setModel($model);

        return $query;
    }

    public function test_get_all_with_trashed()
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('withTrashed->get')->once()->andReturn(new Collection([$model = new ModelStub]));

        $this->repository->delete($model);
        $this->assertCount(1, $this->repository->getAllWithTrashed());
    }

    public function test_it_prevents_lazy_loading_during_search()
    {
        $query = $this->initMockQuery();
        $query->shouldReceive('paginate')->andReturn(new Collection([new ModelStub]));
        $query->shouldReceive('preventLazyLoadingInColumnator')->once()->andReturn($query);

        $results = $this->repository->getByFilters(new SearchFilterCollection);
        $this->assertCount(1, $results);
    }
}

class RepositoryStub extends Repository
{
    protected function currentAccountId()
    {
        return 1;
    }

    /**
     * Returns a new query object that can be used.
     *
     * @return mixed
     */
    protected function getQuery()
    {
        return $this->getModel()->newQuery();
    }
}
