<?php

namespace Tests\Database\Eloquent;

use Eloquence\Behaviours\HasSlugs;
use Eloquence\Behaviours\Slug;
use Platform\Database\Eloquent\Collection;
use Platform\Database\Eloquent\Model;
use Platform\Events\ModelWasCopied;
use Platform\Test\EventAssertions;
use Tests\TestCase;

final class ModelTest extends TestCase
{
    use EventAssertions;

    public function test_it_can_convert_objects_to_string_literals(): void
    {
        $entry = new Entry;
        $entry->id = 1;
        $entry->slug = new Slug('entry-slug');

        $attributes = $entry->attributesToArray();

        $this->assertSame('entry-slug', $attributes['slug']);
        $this->assertSame(1, $attributes['id']);
    }

    public function test_it_returns_the_model_cache_key(): void
    {
        $entry = new Entry;
        $entry->id = 7658;
        $entry->exists = true;

        $this->assertSame('Entry-7658-', $entry->cacheKey());
    }

    public function test_it_returns_a_new_cache_key_for_non_existing_model_objects(): void
    {
        $this->assertSame('Entry-new', (new Entry)->cacheKey());
    }

    public function test_it_returns_platform_collection_objects(): void
    {
        $this->assertInstanceOf(Collection::class, (new Entry)->newCollection([]));
    }

    public function test_it_can_restore_itself(): void
    {
        $model = new UnsaveableModel;
        $model->deletedAt = time();
        $model->restore();

        $this->assertNull($model->deletedAt);
    }

    public function test_it_can_copy_itself(): void
    {
        $model = new ModelStub;
        $copy = $model->copy();

        $this->assertRaised($copy, ModelWasCopied::class);
    }

    public function test_models_can_cache_changed_attributes(): void
    {
        $model = new Entry;
        $model->title = 'Nomination';
        $model->category = 765;

        $this->assertChanged(['title', 'category'], $model);

        $model->title = 'New nomination';

        $this->assertChanged(['title'], $model);
    }

    // Helper method to test model attirubte value changes
    private function assertChanged($expected, $model)
    {
        try {
            $model->save();
        } catch (\PDOException $e) {
            foreach ($expected as $field) {
                $this->assertArrayHasKey($field, $model->changed);
            }
        }
    }
}

class Entry extends Model
{
    use HasSlugs;
}

class UnsaveableModel extends Model
{
    public function save(array $options = [])
    {
        return true;
    }
}
