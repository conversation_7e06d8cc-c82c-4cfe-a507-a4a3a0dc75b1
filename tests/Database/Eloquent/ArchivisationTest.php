<?php

namespace Tests\Database\Eloquent;

use Carbon\Carbon;
use Platform\Database\Eloquent\Archivisation;
use Platform\Database\Eloquent\Model;
use Platform\Events\ModelWasArchived;
use Platform\Events\ModelWasUnarchived;
use Platform\Test\EventAssertions;
use Tests\TestCase;

final class ArchivisationTest extends TestCase
{
    use EventAssertions;

    public function test_it_can_be_archived(): void
    {
        $model = new ArchivableModel;

        $result = $model->archive();

        $this->assertTrue($result);
        $this->assertNotNull($model->archivedAt);
        $this->assertRaised($model, ModelWasArchived::class);
    }

    public function test_it_can_be_unarchived(): void
    {
        $model = new ArchivableModel;
        $model->archivedAt = Carbon::now();

        $result = $model->unarchive();

        $this->assertTrue($result);
        $this->assertNull($model->archivedAt);
        $this->assertRaised($model, ModelWasUnarchived::class);
    }
}

class ArchivableModel extends Model
{
    use Archivisation;

    public function save(array $options = [])
    {
        return true;
    }
}
