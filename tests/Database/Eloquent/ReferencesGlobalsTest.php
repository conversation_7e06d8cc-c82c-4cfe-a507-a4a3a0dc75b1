<?php

namespace Tests\Database\Eloquent;

use Platform\Database\Eloquent\Model;
use Platform\Database\Eloquent\ReferencesGlobals;
use Ramsey\Uuid\Uuid;
use Tests\TestCase;

final class ReferencesGlobalsTest extends TestCase
{
    public function testUsingIncorrectValueRaisesException(): void
    {
        $this->expectException(\InvalidArgumentException::class);

        $model = new GlobalModel;
        $model->globalId = 'lkjsdf';
    }

    public function testRetreivingUuids(): void
    {
        $globalId = Uuid::uuid4();
        $ownerId = Uuid::uuid4();

        $model = new GlobalModel;
        $model->globalId = $globalId;
        $model->ownerId = $ownerId;

        $this->assertSame($globalId->toString(), $model->globalId->toString());
        $this->assertSame($ownerId->toString(), $model->ownerId->toString());
    }
}

class GlobalModel extends Model
{
    use ReferencesGlobals;

    /**
     * Return the columns that have a uuid.
     */
    protected function uuids(): array
    {
        return ['global_id', 'owner_id'];
    }
}
