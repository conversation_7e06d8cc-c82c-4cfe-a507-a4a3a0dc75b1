<?php
namespace Tests\Database\Eloquent;


use Platform\Database\Eloquent\Builder;
use Tests\TestCase;
use Mockery as m;

class BuilderTest extends TestCase
{
    public function test_it_sets_prevent_lazy_loading_on_models_during_hydration()
    {
        $query = m::mock(\Illuminate\Database\Query\Builder::class);
        $query->shouldReceive('from')->andReturnSelf();
        $query->shouldReceive('getConnection->getName')->andReturn('test');
        $query->shouldReceive('get')->andReturn(collect([
            ['id' => 1],
            ['id' => 2],
        ]));
        
        $builder = new Builder($query);
        $builder->preventLazyLoadingInColumnator()
            ->setModel(new ModelStub);
        
        $results = $builder->getModels();

        $this->assertTrue($results[0]->preventsLazyLoading);
        $this->assertTrue($results[1]->preventsLazyLoading);
    }
}
