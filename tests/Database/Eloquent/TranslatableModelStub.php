<?php

namespace Tests\Database\Eloquent;

use Platform\Authorisation\Consumer;
use Platform\Database\Eloquent\Model;
use Platform\Database\Eloquent\TranslatableModel;
use Tectonic\Localisation\Contracts\Translatable;

class TranslatableModelStub extends Model implements Translatable
{
    use TranslatableModel;

    private $consumer;

    protected function consumer(): Consumer
    {
        return $this->consumer;
    }

    public function setTranslated(array $translated)
    {
        $this->translated = $translated;
    }

    public function setConsumer(Consumer $consumer)
    {
        $this->consumer = $consumer;
    }

    /**
     * Returns the id for the record implementing the interface.
     *
     * @return int
     */
    public function getId()
    {
        // TODO: Implement getId() method.
    }

    /**
     * Returns the name of the resource that this object represents.
     *
     * @return string
     */
    public function getResourceName()
    {
        // TODO: Implement getResourceName() method.
    }

    /**
     * Add a translation to the model or entity.
     *
     * @param  string  $language
     * @param  string  $key
     * @param  mixed  $value
     */
    public function addTranslation($language, $key, $value)
    {
        // TODO: Implement addTranslation() method.
    }

    /**
     * Returns an array of field names that can be translated.
     *
     * @return array
     */
    public function getTranslatableFields()
    {
        return ['model', 'name', 'title'];
    }
}
