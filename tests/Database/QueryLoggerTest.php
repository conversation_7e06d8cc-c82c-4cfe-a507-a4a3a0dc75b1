<?php

namespace Tests\Database;

use Illuminate\Support\Collection;
use Platform\Database\Facades\QueryLogger;
use Platform\Database\QueryLoggerService;
use Tests\TestCase;

final class QueryLoggerTest extends TestCase
{
    public function init()
    {
        $this->app->singleton('querylogger', FakeQueryLoggerService::class);
    }

    public function testItGroupsQueriesResolvingQueryBindings(): void
    {
        $this->assertEquals(3, QueryLogger::getAllQueries(false)->count());
        $this->assertEquals(
            'select `integrations`.* from `integrations` where `integrations`.`account_id` = ? and `action` in (?, ?) limit 1',
            QueryLogger::getAllQueries(false)->keys()->first()
        );
    }

    public function testItGroupsQueriesWithoutResolvingQueryBindings(): void
    {
        $this->assertEquals(4, QueryLogger::getAllQueries(true)->count());
        $this->assertEquals(
            'select `integrations`.* from `integrations` where `integrations`.`account_id` = 33 and `action` in (666, "param-2") limit 1',
            QueryLogger::getAllQueries(true)->keys()->first()
        );
    }

    public function testItCountsMostOftenExecutedQueriesDontResolveBindings(): void
    {
        $mostOftenExecuted = QueryLogger::getMostOftenExecuted(false);

        $this->assertEquals([
            FakeQueryLoggerService::SQL_2_GENERIC => 2,
            FakeQueryLoggerService::SQL_1_GENERIC => 1,
            FakeQueryLoggerService::SQL_4_GENERIC => 1,
        ], $mostOftenExecuted->toArray());
    }

    public function testItCountsMostOftenExecutedQueriesResolveBindings(): void
    {
        $mostOftenExecuted = QueryLogger::getMostOftenExecuted(true);

        $this->assertEquals([
            FakeQueryLoggerService::SQL_1_WITH_VALUES => 1,
            FakeQueryLoggerService::SQL_2_WITH_VALUES => 1,
            FakeQueryLoggerService::SQL_3_WITH_VALUES => 1,
            FakeQueryLoggerService::SQL_4_WITH_VALUES => 1,
        ], $mostOftenExecuted->toArray());
    }

    public function testItCountsMostTimeConsumingDontResolveBindings(): void
    {
        $mostOftenExecuted = QueryLogger::getMostTimeConsuming(false);

        $this->assertEquals([
            FakeQueryLoggerService::SQL_2_GENERIC => 6.0,
            FakeQueryLoggerService::SQL_1_GENERIC => 4.5,
            FakeQueryLoggerService::SQL_4_GENERIC => 1.0,
            '### TOTAL QUERY EXECUTION TIME IN SECONDS ###' => 11.5,
        ], $mostOftenExecuted->toArray());
    }

    public function testItCountsMostTimeConsumingResolveBindings(): void
    {
        $mostOftenExecuted = QueryLogger::getMostTimeConsuming(true);

        $this->assertEquals([
            FakeQueryLoggerService::SQL_1_WITH_VALUES => 4.5,
            FakeQueryLoggerService::SQL_2_WITH_VALUES => 3.5,
            FakeQueryLoggerService::SQL_3_WITH_VALUES => 2.5,
            FakeQueryLoggerService::SQL_4_WITH_VALUES => 1.0,
            '### TOTAL QUERY EXECUTION TIME IN SECONDS ###' => 11.5,
        ], $mostOftenExecuted->toArray());
    }
}

// Fake class

class FakeQueryLoggerService extends QueryLoggerService
{
    const SQL_1_GENERIC = 'select `integrations`.* from `integrations` where `integrations`.`account_id` = ? and `action` in (?, ?) limit 1';

    const SQL_1_WITH_VALUES = 'select `integrations`.* from `integrations` where `integrations`.`account_id` = 33 and `action` in (666, "param-2") limit 1';

    const SQL_2_GENERIC = 'select * from `translations` where `translations`.`account_id` = ? and `resource` = ?';

    const SQL_2_WITH_VALUES = 'select * from `translations` where `translations`.`account_id` = 33 and `resource` = "ui"';

    const SQL_3_GENERIC = 'select * from `translations` where `translations`.`account_id` = ? and `resource` = ?';

    const SQL_3_WITH_VALUES = 'select * from `translations` where `translations`.`account_id` = 33 and `resource` = "button-text"';

    const SQL_4_GENERIC = 'DELETE FROM `discounts`';

    const SQL_4_WITH_VALUES = 'DELETE FROM `discounts`';

    /**
     * Modified example output of \DB::getQueryLog().
     *
     * @return Collection
     */
    public function getRawQueries()
    {
        return collect([
            [
                'query' => self::SQL_1_GENERIC,
                'bindings' => [
                    33,
                    666,
                    'param-2',
                ],
                'time' => 4500,
            ],
            [
                'query' => self::SQL_2_GENERIC,
                'bindings' => [
                    33,
                    'ui',
                ],
                'time' => 3500,
            ],
            [
                'query' => self::SQL_3_GENERIC,
                'bindings' => [
                    33,
                    'button-text',
                ],
                'time' => 2500,
            ],
            [
                'query' => self::SQL_4_GENERIC,
                'bindings' => [],
                'time' => 1000,
            ],
        ]);
    }
}
