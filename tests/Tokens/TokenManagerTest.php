<?php

namespace Tests\Tokens;

use Carbon\Carbon;
use Illuminate\Contracts\Cache\Repository as Cache;
use Illuminate\Support\Str;
use Mockery as m;
use Platform\Tokens\StringToken;
use Platform\Tokens\Token;
use Platform\Tokens\TokenManager;
use Platform\Tokens\TokenNotFoundException;
use Tests\TestCase;

final class TokenManagerTest extends TestCase
{
    const ACCOUNTID = 13;

    /**
     * @var TokenManager
     */
    protected $manager;

    /**
     * @var m\MockInterface
     */
    protected $cache;

    public function init()
    {
        $this->cache = m::mock(Cache::class);

        $this->manager = new TokenManager($this->cache);
    }

    public function testCreateNewToken(): void
    {
        $type = $this->newTokenWithoutAccount();

        $this->cache->shouldReceive('put')
            ->with(m::type('string'), $type, m::type(Carbon::class));

        $token = $this->manager->create($type);

        $this->assertNotNull($token);
    }

    public function testPullTokenNotFoundRandomToken(): void
    {
        $this->cache->shouldReceive('pull')->with(m::type('string'))->andReturnNull();

        $this->expectException(TokenNotFoundException::class);
        $this->manager->pull(Str::random(), self::class);
    }

    public function testPullTokenNotFoundInvalidType(): void
    {
        $type = $this->newTokenWithoutAccount();
        $this->cache->shouldReceive('pull')->with(m::type('string'))->andReturn($type);

        $this->expectException(TokenNotFoundException::class);
        $this->manager->pull(Str::random(), self::class);
    }

    public function testPullInvalidAccount(): void
    {
        $type = $this->newTokenWithAccount();
        $this->cache->shouldReceive('pull')->with(m::type('string'))->andReturn($type);

        $this->expectException(TokenNotFoundException::class);
        $this->manager->pull(Str::random(), get_class($type), 42);
    }

    public function testPullTokenType(): void
    {
        $type = $this->newTokenWithAccount();
        $this->cache->shouldReceive('pull')->with(m::type('string'))->andReturn($type);

        $result = $this->manager->pull(Str::random(), get_class($type), self::ACCOUNTID);

        $this->assertEquals($type, $result);
    }

    public function testCreateAndRequireUseSameKey(): void
    {
        $type = $this->newTokenWithoutAccount();

        $cacheKey = '';
        $on = function ($key) use (&$cacheKey) {
            $cacheKey = $key;

            return true;
        };

        $this->cache->shouldReceive('put')->with(m::on($on), $type, m::type(Carbon::class));
        $token = $this->manager->create($type);

        $this->cache->shouldReceive('pull')->with($cacheKey)->andReturn($type);
        $this->manager->pull($token, get_class($type));
    }

    public function testHasTokenInvalidToken(): void
    {
        $this->cache->shouldReceive('get')->with(m::type('string'))->andReturnNull();

        $this->assertFalse($this->manager->has(Str::random(), self::class));
    }

    public function testHasToken(): void
    {
        $type = $this->newTokenWithAccount();
        $this->cache->shouldReceive('get')->with(m::type('string'))->andReturn($type);

        $this->assertTrue($this->manager->has(Str::random(), get_class($type), self::ACCOUNTID));
    }

    public function testCustomTokenGeneration(): void
    {
        $type = new class implements Token
        {
            public function expires(): int
            {
                return 5;
            }

            public function accountId()
            {
                return null;
            }

            public function generate()
            {
                return 12345;
            }
        };

        $this->cache->shouldReceive('put');

        $this->assertEquals(12345, $this->manager->create($type));
    }

    protected function newTokenWithoutAccount(): Token
    {
        return new class implements Token
        {
            use StringToken;

            public function expires(): int
            {
                return 5;
            }

            public function accountId()
            {
                return null;
            }
        };
    }

    protected function newTokenWithAccount(): Token
    {
        return new class implements Token
        {
            use StringToken;

            public function expires(): int
            {
                return 5;
            }

            public function accountId()
            {
                return TokenManagerTest::ACCOUNTID;
            }
        };
    }
}
