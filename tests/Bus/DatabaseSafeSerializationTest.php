<?php

namespace Tests\Bus;

use Platform\Bus\SerializesModels;
use Platform\Database\Database;
use Platform\Database\Selector;
use Tests\TestCase;

final class DatabaseSafeSerializationTest extends TestCase
{
    public function init()
    {
        Database::registerConnections(['current_database', 'other_database']);
        app()->bind(Selector::class, function () {
            return new class implements Selector
            {
                public function current(): Database
                {
                    return new FakeDatabase('current_database');
                }

                public function select(Database $database)
                {
                }
            };
        });
    }

    public function test_database_is_available_after_unserializing(): void
    {
        $object = new FakeModel;

        $this->assertNull($object->database);

        $result = unserialize(serialize($object));

        $this->assertEquals('current_database', $result->database);
    }

    public function test_custom_properties_are_included(): void
    {
        $object = new FakeModel('some_name');

        $result = unserialize(serialize($object));

        $this->assertEquals('some_name', $result->name());
    }
}

class FakeModel
{
    use SerializesModels;

    protected $name;

    protected $age;

    public function __construct($name = null)
    {
        $this->name = $name;
    }

    public function name()
    {
        return $this->name;
    }

    public function age()
    {
        return $this->age;
    }

    protected function completeSleep(): array
    {
        return ['name'];
    }

    protected function completeWakeup()
    {
        $this->age = 'old';
    }

    protected function database(): Database
    {
        return new FakeDatabase($this->database);
    }
}

class FakeDatabase extends Database
{
    public function __construct(string $name)
    {
        $this->name = $name;
    }
}
