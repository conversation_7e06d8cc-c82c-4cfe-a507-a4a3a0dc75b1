<?php

namespace Tests\Bus\Pipeline;

use Illuminate\Contracts\Bus\Dispatcher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Mockery as m;
use Platform\Bus\Pipeline\QueuedRetryOnException;
use Platform\Bus\Pipeline\RetryOnException;
use Psr\Log\LoggerInterface as Log;
use Tests\TestCase;

final class QueuedRetryOnExceptionTest extends TestCase
{
    /**
     * @var QueuedRetryOnException
     */
    private $pipe;

    /**
     * @var \Mockery\MockInterface
     */
    private $log;

    /**
     * @var \Mockery\MockInterface
     */
    private $dispatcher;

    public function init()
    {
        config(['queue.default' => 'redis']);

        $this->pipe = new QueuedRetryOnException(
            $this->log = m::mock(Log::class),
            $this->dispatcher = m::mock(Dispatcher::class)
        );
    }

    public function test_pipe_without_trait(): void
    {
        $command = m::mock();

        $this->pipe->handle($command, function ($next) use ($command) {
            $this->assertSame($command, $next);
        });
    }

    public function test_pipe_with_trait(): void
    {
        $command = new class implements ShouldQueue
        {
            use RetryOnException;

            protected function allowedRetries(): int
            {
                return 3;
            }
        };

        $this->pipe->handle($command, function ($next) use ($command) {
            $this->assertSame($command, $next);
        });
    }

    public function test_fail_with_retry(): void
    {
        $command = new class implements ShouldQueue
        {
            use RetryOnException;

            protected function allowedRetries(): int
            {
                return 3;
            }
        };

        $this->log->shouldReceive('notice');
        $this->dispatcher->shouldReceive('dispatch')->with(m::on(function ($command) {
            return $command->remainingAttempts() == 2 && $command->delay > 0;
        }));

        $this->pipe->handle($command, function ($next) {
            throw new \Exception;
        });
    }

    public function test_fail_with_no_retries(): void
    {
        $command = new class implements ShouldQueue
        {
            use RetryOnException;

            protected function allowedRetries(): int
            {
                return 0;
            }
        };

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('This should fail.');

        $this->pipe->handle($command, function ($next) {
            throw new \Exception('This should fail.');
        });
    }
}
