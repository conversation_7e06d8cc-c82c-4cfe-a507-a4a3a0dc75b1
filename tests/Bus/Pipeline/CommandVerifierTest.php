<?php

namespace Tests\Bus\Pipeline;

use Illuminate\Contracts\Container\Container;
use Mockery as m;
use Platform\Bus\Pipeline\CommandVerifier;
use Tests\TestCase;

final class CommandVerifierTest extends TestCase
{
    private $container;

    private $verifier;

    public function init()
    {
        $this->container = m::mock(Container::class);
        $this->verifier = new CommandVerifier($this->container);
    }

    public function test_it_throws_the_correct_exception(): void
    {
        $this->expectException(VerificationFailedException::class);

        $this->container->shouldReceive('make')->once()->andReturn(new QueuedCommandCommandVerifier);

        $this->verifier->handle(new QueuedCommandCommand('account'), function () {
        });
    }

    public function test_it_executes_the_required_command(): void
    {
        $result = $this->verifier->handle(new class
        {
        }, function () {
            return 'blah';
        });

        $this->assertSame('blah', $result);
    }
}
