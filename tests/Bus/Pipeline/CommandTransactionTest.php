<?php

namespace Tests\Bus\Pipeline;

use Illuminate\Support\Facades\DB;
use Platform\Bus\Pipeline\CommandTransaction;
use Platform\Bus\Pipeline\DatabaseTransactions;
use Tests\TestCase;

final class CommandTransactionTest extends TestCase
{
    public function test_it_can_execute_database_transactions_as_required(): void
    {
        DB::swap(new MockDb);

        $command = new class implements DatabaseTransactions
        {
        };
        $next = function () {
            return 'transactified!';
        };

        $transactional = new CommandTransaction;

        $this->assertSame('transactified!', $transactional->handle($command, $next));
    }

    public function test_it_just_continues_the_chain(): void
    {
        $this->assertSame('result', (new CommandTransaction)->handle(new class
        {
        }, function () {
            return 'result';
        }));
    }
}

class MockDb
{
    public function transaction(\Closure $function)
    {
        return $function();
    }
}
