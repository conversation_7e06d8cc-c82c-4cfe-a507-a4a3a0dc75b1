<?php

namespace Tests\Bus\Pipeline;

use Illuminate\Contracts\Bus\Dispatcher;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Mockery as m;
use Platform\Bus\Debounce;
use Platform\Bus\Pipeline\QueuedDebounce;
use Tests\TestCase;

final class QueuedDebounceTest extends TestCase
{
    const RESPONSE = 'Use the force, Luke!';

    public function init()
    {
        config(['queue.default' => 'redis']);
    }

    public function test_ignore_other_commands(): void
    {
        $response = (new QueuedDebounce)->handle($cmd = new \stdClass, $this->next($cmd));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function test_unique(): void
    {
        $response = (new QueuedDebounce)->handle($cmd = $this->command('key'), $this->next($cmd));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function test_debounce_outdated(): void
    {
        $outdated = tap($this->command('key'))->queueDebounce();
        $latest = tap($this->command('key'))->queueDebounce();

        $response = (new QueuedDebounce)->handle($outdated, $this->noNext());
        $this->assertNull($response);

        $response = (new QueuedDebounce)->handle($latest, $this->next($latest));
        $this->assertEquals(self::RESPONSE, $response);
    }

    public function test_overdue(): void
    {
        $outdated = tap($this->command('key'))->queueDebounce();
        $overdue = tap($this->command('key', [], true))->queueDebounce();
        $latest = tap($this->command('key'))->queueDebounce();

        // Outdated skipped
        $response = (new QueuedDebounce)->handle($outdated, $this->noNext());
        $this->assertNull($response);

        // Overdue runs
        $response = (new QueuedDebounce)->handle($overdue, $this->next($overdue));
        $this->assertEquals(self::RESPONSE, $response);

        // Latest runs
        $response = (new QueuedDebounce)->handle($latest, $this->next($latest));
        $this->assertEquals(self::RESPONSE, $response);
    }

    public function test_debounce_exclusive(): void
    {
        $locked = tap($this->command('key'))->queueDebounce();
        $delayed = $this->command('key');

        $this->app->instance(Dispatcher::class, $dispatcher = m::mock(Dispatcher::class));
        $dispatcher->shouldReceive('dispatch')->with($delayed)->once();

        (new QueuedDebounce)->handle($locked, function ($locked) use ($delayed) {
            $delayed->queueDebounce();
            (new QueuedDebounce)->handle($delayed, $this->noNext());
        });
    }

    public function test_forget_cached_payload_after_locked(): void
    {
        $original = tap($this->command('key', ['original' => true]))->queueDebounce();

        (new QueuedDebounce)->handle($original, function ($original) {
            $next = tap($this->command('key', ['next' => true]))->queueDebounce();
            $this->assertEquals(['next' => true, 'key' => 'key'], $next->debouncePayload([]));
        });
    }

    public function testDebounceUnlocksAfterThrowable(): void
    {
        $command = tap($this->command('key'))->queueDebounce();

        $this->assertFalse($command->debounceLocked());

        try {
            (new QueuedDebounce)->handle($command, function ($command) {
                $this->assertTrue($command->debounceLocked());
                throw new \Exception('Hello world.');
            });
        } catch (\Exception $exception) {
            // ignore exception
        }

        $this->assertFalse($command->debounceLocked());
    }

    public function test_debounce_interacts_with_queue(): void
    {
        $locked = tap($this->commandInteractsWithQueue('key'))->queueDebounce();
        $delayed = $this->commandInteractsWithQueue('key');

        $this->app->instance(Dispatcher::class, $dispatcher = m::mock(Dispatcher::class));
        $dispatcher->shouldNotReceive('dispatch');

        (new QueuedDebounce)->handle($locked, function ($locked) use ($delayed) {
            $delayed->queueDebounce();
            (new QueuedDebounce)->handle($delayed, $this->noNext());
        });
    }

    private function next($expected)
    {
        return function ($actual) use ($expected) {
            $this->assertSame($expected, $actual);

            return self::RESPONSE;
        };
    }

    private function noNext()
    {
        return function () {
            throw new \Exception('$next($command) should not have run!');
        };
    }

    private function command(string $key, array $payload = [], bool $overdue = false)
    {
        return new class($key, $payload, $overdue) implements ShouldQueue
        {
            use Debounce;

            /** @var string */
            private $key;

            /** @var array */
            private $payload;

            /** @var bool */
            private $overdue;

            public function __construct(string $key, array $payload, bool $overdue)
            {
                $this->key = $key;
                $this->payload = $payload;
                $this->overdue = $overdue;
            }

            public function debounceKey(): string
            {
                return $this->key;
            }

            public function debouncePayload(array $existing): array
            {
                return $this->payload = array_merge($this->payload, $existing, ['key' => $this->key]);
            }

            public function debounceOverdue(): bool
            {
                return $this->overdue;
            }
        };
    }

    private function commandInteractsWithQueue(string $key, array $payload = [], bool $overdue = false)
    {
        return new class($key, $payload, $overdue) implements ShouldQueue
        {
            use Debounce;
            use InteractsWithQueue;

            /** @var string */
            private $key;

            /** @var array */
            private $payload;

            /** @var bool */
            private $overdue;

            public function __construct(string $key, array $payload, bool $overdue)
            {
                $this->key = $key;
                $this->payload = $payload;
                $this->overdue = $overdue;
            }

            public function debounceKey(): string
            {
                return $this->key;
            }

            public function debouncePayload(array $existing): array
            {
                return $this->payload = array_merge($this->payload, $existing, ['key' => $this->key]);
            }

            public function debounceOverdue(): bool
            {
                return $this->overdue;
            }
        };
    }
}
