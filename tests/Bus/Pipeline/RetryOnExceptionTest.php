<?php

namespace Tests\Bus\Pipeline;

use Platform\Bus\Pipeline\RetryOnException;
use Tests\TestCase;

final class RetryOnExceptionTest extends TestCase
{
    public function test_retry(): void
    {
        $trait = $this->make(3);

        $this->assertEquals(3, $trait->remainingAttempts());
        $this->assertEquals(0, $trait->delay);

        $trait = $trait->retry();

        $this->assertEquals(2, $trait->remainingAttempts());
        $this->assertGreaterThan(59, $trait->delay);
    }

    private function make($retries = 3)
    {
        return new class($retries)
        {
            use RetryOnException;

            public $delay;

            private $testRetries;

            public function __construct($retries)
            {
                $this->testRetries = $retries;
            }

            protected function allowedRetries(): int
            {
                return $this->testRetries;
            }
        };
    }
}
