<?php

namespace Tests\Bus\Pipeline;

use Illuminate\Contracts\Queue\ShouldQueue;
use Platform\Bus\Pipeline\QueuedOrSync;
use Tests\TestCase;

final class QueuedOrSyncTest extends TestCase
{
    use QueuedOrSync;

    public function test_it_knows_sync_commands(): void
    {
        config(['queue.default' => 'redis']);

        $this->assertTrue($this->isSync(new class
        {
        }));
        $this->assertFalse($this->isQueued(new class
        {
        }));
    }

    public function test_it_knows_queued_commands(): void
    {
        config(['queue.default' => 'redis']);

        $this->assertTrue($this->isQueued(new class implements ShouldQueue
        {
        }));
        $this->assertFalse($this->isSync(new class implements ShouldQueue
        {
        }));
    }

    public function test_it_knows_sync_queue_enabled(): void
    {
        config(['queue.default' => 'sync']);

        $this->assertTrue($this->isSync(new class implements ShouldQueue
        {
        }));
        $this->assertFalse($this->isQueued(new class implements ShouldQueue
        {
        }));
    }
}
