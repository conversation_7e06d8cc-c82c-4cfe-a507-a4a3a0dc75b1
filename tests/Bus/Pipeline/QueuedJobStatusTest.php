<?php

namespace Tests\Bus\Pipeline;

use Illuminate\Contracts\Queue\ShouldQueue;
use Mockery as m;
use Platform\Bus\JobStatus;
use Platform\Bus\Pipeline\QueuedJobStatus;
use Tests\TestCase;

final class QueuedJobStatusTest extends TestCase
{
    public function init()
    {
        config(['queue.default' => 'redis']);
    }

    public function testNotJobStatusModel(): void
    {
        $actual = (new QueuedJobStatus)->handle(new QueuedNonJobStatusCommand, function () {
            return 'Success!';
        });

        $this->assertEquals('Success!', $actual);
    }

    public function testWithJobStatusModel(): void
    {
        $command = m::Mock(JobStatusCommand::class);
        $command->shouldReceive('setProcessing')->once();
        $command->shouldReceive('setFinished')->withAnyArgs()->once();

        $actual = (new QueuedJobStatus)->handle($command, function () {
            return 'Success!';
        });

        $this->assertEquals('Success!', $actual);
    }
}

class QueuedNonJobStatusCommand implements ShouldQueue
{
}

class JobStatusCommand implements ShouldQueue
{
    use JobStatus;

    protected function jobStatusResource()
    {
    }

    public function consumer()
    {
    }

    protected function debounceKey(): string
    {
        return 'jobstatus';
    }
}
