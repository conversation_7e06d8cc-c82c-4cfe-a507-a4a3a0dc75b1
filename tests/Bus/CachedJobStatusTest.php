<?php

namespace Tests\Bus;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Mockery as m;
use Platform\Bus\CachedJobStatus;
use Tests\TestCase;

final class CachedJobStatusTest extends TestCase
{
    /**
     * @var CachedJobStatus
     */
    private $resource;

    public function init()
    {
        Cache::swap(m::mock('cache'));
        $this->resource = new CachedJobStatus('testCacheKey', 1);
    }

    public function test_it_can_set_the_job_status(): void
    {
        Cache::shouldReceive('put')->with(m::on(function ($value) {
            return Str::contains($value, ['1', 'testCacheKey']);
        }), 'queued', m::any('integer'))->once();

        Cache::shouldReceive('forget')->once();

        $this->resource->setJobStatus('queued');
        $this->resource->setJobStatus('');
    }

    public function test_job_status_retrieval(): void
    {
        Cache::shouldReceive('get')->with(m::on(function ($value) {
            return Str::contains($value, ['1', 'testCacheKey']);
        }))->once()->andReturn('queued');

        $this->assertEquals('queued', $this->resource->getJobStatus());
    }

    public function test_the_job_has_finished_processing(): void
    {
        Cache::shouldReceive('get')->with(m::any())->once()->andReturn('queued');

        $this->assertFalse($this->resource->hasFinishedProcessing());

        Cache::shouldReceive('get')->with(m::any())->once()->andReturnNull();

        $this->assertTrue($this->resource->hasFinishedProcessing());
    }

    public function test_it_can_return_the_broadcast_key(): void
    {
        $this->assertEquals('testCacheKey', $this->resource->getBroadcastKey());
    }

    public function test_it_can_retrieve_the_broadcast_token(): void
    {
        $this->assertNotNull($this->resource->getBroadcastRefreshToken());
    }
}
