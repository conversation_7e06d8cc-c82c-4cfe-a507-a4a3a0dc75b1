<?php

namespace Tests\Bus;

use Illuminate\Contracts\Database\ModelIdentifier;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Platform\Bus\SerializesModels;
use Tests\TestCase;

final class SerializesModelsTest extends TestCase
{
    public function init()
    {
        $this->migrate();
    }

    public function test_it_can_retrieve_the_restored_property_value_of_a_model(): void
    {
        $serializer = new Serializer;
        $m1 = ModelOne::create();
        $m2 = ModelTwo::create();
        $model1 = new ModelIdentifier('Tests\Bus\ModelOne', $m1->id, [], null);
        $model2 = new ModelIdentifier('Tests\Bus\ModelTwo', $m2->id, [], null);

        $m2->delete();

        $this->assertInstanceOf(ModelOne::class, $restored1 = $serializer->restoredValue($model1));
        $this->assertInstanceOf(ModelTwo::class, $restored2 = $serializer->restoredValue($model2));
        $this->assertEquals($m1->id, $restored1->id);
        $this->assertEquals($m2->id, $restored2->id);
        $this->assertNotEmpty($restored2->deleted_at);
    }

    private function migrate(): void
    {
        Schema::create('model_ones', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
        });

        Schema::create('model_twos', function (Blueprint $table) {
            $table->increments('id');
            $table->timestamps();
            $table->softDeletes();
        });
    }
}

class Serializer
{
    use SerializesModels;

    public function restoredValue($value)
    {
        return $this->restoreModel($value);
    }
}

class ModelOne extends Model
{
}

class ModelTwo extends ModelOne
{
    use SoftDeletes;
}
