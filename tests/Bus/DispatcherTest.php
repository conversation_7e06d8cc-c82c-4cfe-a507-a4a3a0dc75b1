<?php

namespace Tests\Bus;

use Illuminate\Contracts\Container\Container;
use Mockery as m;
use Platform\Bus\Dispatcher;
use Tests\TestCase;

final class DispatcherTest extends TestCase
{
    /**
     * @var Dispatcher
     */
    private $dispatcher;

    /**
     * @var m\MockInterface
     */
    private $container;

    public function init()
    {
        $this->dispatcher = new Dispatcher($this->container = m::mock(Container::class));
    }

    public function test_cannot_find_handler(): void
    {
        $this->assertFalse($this->dispatcher->getCommandHandler(new IHaveNoHandler));
    }

    public function test_has_handler(): void
    {
        $this->container->shouldReceive('make')
            ->with(HandlerIDoHaveHandler::class)
            ->andReturn($handler = new HandlerIDoHaveHandler);

        $this->assertSame($handler, $this->dispatcher->getCommandHandler(new HandlerIDoHave));
    }
}

class IHaveNoHandler
{
}

class HandlerIDoHave
{
}

class HandlerIDoHaveHandler
{
}
