<?php

namespace Tests\Bus;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use Mockery as m;
use Platform\Authorisation\Consumer;
use Platform\Bus\JobStatus;
use Platform\Bus\JobStatusEvents;
use Platform\Bus\QueuedJob;
use stdClass;
use Tests\TestCase;

class JobStatusTest extends TestCase
{
    public function init()
    {
        Log::swap(m::mock('logger'));

        Log::shouldReceive('debug');
    }

    public function test_it_returns_queued_job_status(): void
    {
        $resource = m::mock('resource');
        $resource->shouldReceive('setJobStatus')->with('queued')->once();

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = $resource;
        $jobStatus->queueJobStatus();
    }

    public function test_it_sets_queued_job_status(): void
    {
        $resource = m::mock(stdClass::class);
        $resource->shouldReceive('setJobStatus')->with('queued')->once();

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = $resource;
        $jobStatus->setQueued();

        $this->assertEmpty($jobStatus->events);
    }

    public function test_it_sets_status_to_processing(): void
    {
        $resource = m::mock(stdClass::class);
        $resource->shouldReceive('setJobStatus')->with('processing')->once();

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = $resource;
        $jobStatus->setProcessing();

        $this->assertEmpty($jobStatus->events);
    }

    public function test_job_status_completion(): void
    {
        $resource = m::mock(stdClass::class);
        $resource->shouldReceive('getJobStatus')->once()->andReturnNull();
        $resource->shouldReceive('setJobStatus')->with(null)->once();

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = $resource;
        $jobStatus->setFinished();

        $this->assertCount(1, $jobStatus->events);
    }

    public function test_finished_job_status_is_blocked_when_queued(): void
    {
        $resource = m::mock(stdClass::class);
        $resource->shouldReceive('getJobStatus')->once()->andReturn('queued');

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = $resource;
        $jobStatus->setFinished();

        $this->assertEmpty($jobStatus->events);
    }

    public function test_it_accepts_resource_collections(): void
    {
        $resource = m::mock('resource');
        $resource->shouldReceive('setJobStatus')->with('queued')->once();

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = collect([$resource]);
        $jobStatus->queueJobStatus();
    }

    public function test_it_accepts_resource_arrays(): void
    {
        $resource = m::mock('resource');
        $resource->shouldReceive('setJobStatus')->with('queued')->once();

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = [$resource];
        $jobStatus->queueJobStatus();
    }

    public function test_complete_collection_resource(): void
    {
        $resourceOne = m::mock(stdClass::class);
        $resourceOne->shouldReceive('getJobStatus')->once()->andReturnNull();
        $resourceOne->shouldReceive('setJobStatus')->with(null)->once();

        $resourceTwo = m::mock(stdClass::class);
        $resourceTwo->shouldReceive('getJobStatus')->once()->andReturnNull();
        $resourceTwo->shouldReceive('setJobStatus')->with(null)->once();

        $jobStatus = new MockJobStatus;
        $jobStatus->resource = collect([$resourceOne, $resourceTwo]);
        $jobStatus->setFinished();

        $this->assertCount(2, $jobStatus->events);
    }

    public function test_event_dispatched_when_queued(): void
    {
        $jobStatus = new MockJobStatus;
        $jobStatus->resource = new MockResource();
        $jobStatus->queueJobStatus();

        $this->assertCount(1, $jobStatus->events);
        $this->assertEquals('queued', Arr::first($jobStatus->events)->status);
    }

    public function test_event_dispatched_when_processing(): void
    {
        $jobStatus = new MockJobStatus;
        $jobStatus->resource = new MockResource();
        $jobStatus->setProcessing();

        $this->assertCount(1, $jobStatus->events);
        $this->assertEquals('processing', Arr::first($jobStatus->events)->status);
    }

    /**
     * The `setFinished()` method is called when jobs are debounced, so we only want it triggered when going from
     * 'processing', as these aren't debounced.
     */
    public function test_event_dispatched_when_finished_from_processing_only(): void
    {
        $jobStatus = new MockJobStatus;
        $jobStatus->resource = new MockResource('queued');
        $jobStatus->setFinished();

        $this->assertEmpty($jobStatus->events);

        $jobStatus->setProcessing();
        $jobStatus->setFinished();

        $this->assertCount(2, $jobStatus->events);
        $this->assertEquals(['processing', null], Arr::pluck($jobStatus->events, 'status'));
    }
}

class MockJobStatus extends QueuedJob
{
    use JobStatus;

    public $resource;

    public $events = [];

    public function dispatch($events)
    {
        $this->events = array_merge($this->events, is_array($events) ? $events : [$events]);
    }

    protected function jobStatusResource()
    {
        return $this->resource;
    }

    public function consumer()
    {
        return m::mock(Consumer::class)->shouldIgnoreMissing();
    }

    public function account()
    {
    }

    /**
     * Whenever a queued job is used, the job should specify who it was dispatched by.
     *
     * @return mixed
     */
    protected function queueDispatchedBy()
    {
        // TODO: Implement queueDispatchedBy() method.
    }

    protected function debounceKey(): string
    {
        return 'mockjob';
    }
}

class MockResource implements JobStatusEvents
{
    private $status;

    private $events = [];

    public function __construct(?string $status = null)
    {
        $this->status = $status;
    }

    public function getJobStatus(): ?string
    {
        return $this->status;
    }

    public function setJobStatus(?string $status)
    {
        $this->status = $status;
        $this->events[] = (object) ['status' => $this->status];
    }

    public function releaseEvents(): array
    {
        $events = $this->events;
        $this->events = [];

        return $events;
    }
}
