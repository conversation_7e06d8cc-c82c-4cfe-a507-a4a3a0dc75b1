<?php

namespace Tests\Bus;

use Platform\Bus\JobStatusModel;
use Tests\TestCase;

final class JobStatusModelTest extends TestCase
{
    public function test_we_can_set_the_job_status(): void
    {
        $model = new MockJobStatusModel;

        $model->setJobStatus('queued');
        $this->assertEquals('queued', $model->getJobStatus());

        $model->setJobStatus(null);
        $this->assertNull($model->getJobStatus());
    }

    public function test_the_status_checks_of_the_status_model(): void
    {
        $model = new MockJobStatusModel;

        $model->setJobStatus('queued');
        $this->assertFalse($model->hasFinishedProcessing());

        $model->setJobStatus(null);
        $this->assertTrue($model->hasFinishedProcessing());

        $model->setJobStatus('');
        $this->assertTrue($model->hasFinishedProcessing());
    }

    public function test_it_returns_the_correct_broadcast_key(): void
    {
        $model = new MockJobStatusModel;

        $this->assertEquals('mockjobstatusmodel', $model->getBroadcastKey());
    }

    public function test_we_can_retrieve_the_broadcast_name(): void
    {
        $model = new MockJobStatusModel;

        $this->assertIsString($model->getBroadcastName());
    }

    public function test_that_the_broadcast_name_can_retrieve_translated_model_names(): void
    {
        $titleModel = new TranslatedTitleModel;
        $namedModel = new TranslatedNameModel;

        $this->assertSame('title', $titleModel->getBroadcastName());
        $this->assertSame('name', $namedModel->getBroadcastName());
    }

    public function test_it_returns_the_broadcast_token(): void
    {
        $this->assertNotNull((new MockJobStatusModel)->getBroadcastRefreshToken());
    }
}

class MockJobStatusModel
{
    use JobStatusModel;

    public $title = 'this is a title';

    public function save()
    {
    }

    public function getKey()
    {
        return 'key';
    }
}

class TranslatedTitleModel
{
    use JobStatusModel;

    public $title = 'title';

    public function getTranslatableFields()
    {
        return ['title'];
    }
}

class TranslatedNameModel
{
    use JobStatusModel;

    public $name = 'name';

    public function getTranslatableFields()
    {
        return ['name'];
    }
}
