<?php

namespace Tests\Bus;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Platform\Bus\Debounce;
use Tests\TestCase;

final class DebounceTest extends TestCase
{
    public function test_payload_merged(): void
    {
        $outdated = tap($this->command('key', ['outdated' => true]))->queueDebounce();
        $latest = tap($this->command('key', ['latest' => true]))->queueDebounce();

        $this->assertEquals(['outdated' => true, 'latest' => true, 'key' => 'key'], $latest->debouncePayload([]));
    }

    public function test_locks(): void
    {
        $locked = tap($this->command('key'))->queueDebounce();
        $unlocked = tap($this->command('key'))->queueDebounce();

        // Lock one
        $locked->lockDebounce();

        // Both should say locked
        $this->assertTrue($locked->debounceLocked());
        $this->assertTrue($unlocked->debounceLocked());

        // Unlock
        $locked->unlockDebounce();

        // Both say unlocked
        $this->assertFalse($locked->debounceLocked());
        $this->assertFalse($unlocked->debounceLocked());
    }

    public function test_debounce_overdue(): void
    {
        $command = $this->command('key');
        $cacheKey = $command->getHashedKey('last');

        // Not overdue if no last time set
        $this->assertFalse($command->debounceOverdue());

        // Cache timestamp a few seconds ago is not overdue
        Cache::put($cacheKey, Carbon::now()->subSeconds(5), 1);
        $this->assertFalse($command->debounceOverdue());

        // Cache timestamp from yesterday is overdue
        Cache::put($cacheKey, Carbon::yesterday(), 1);
        $this->assertTrue($command->debounceOverdue());

        // Unlock also bumps timestamp
        $command->unlockDebounce();
        $this->assertFalse($command->debounceOverdue());
    }

    private function command(string $key, array $payload = [])
    {
        return new class($key, $payload)
        {
            use Debounce;

            /**
             * @var string
             */
            private $key;

            /**
             * @var array
             */
            private $payload;

            public function __construct(string $key, array $payload)
            {
                $this->key = $key;
                $this->payload = $payload;
            }

            public function debounceKey(): string
            {
                return $this->key;
            }

            public function debouncePayload(array $existing): array
            {
                return $this->payload = array_merge($this->payload, $existing, ['key' => $this->key]);
            }

            public function getHashedKey(string $type): string
            {
                return $this->hashedKey($type);
            }
        };
    }
}
