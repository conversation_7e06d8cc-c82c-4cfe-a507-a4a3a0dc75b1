<?php

namespace Tests\Bus;

use AwardForce\Modules\Accounts\Models\Account;
use Illuminate\Contracts\Queue\Queue;
use Mockery as m;
use Platform\Bus\QueuedJob;
use Tests\TestCase;

final class QueuedJobTest extends TestCase
{
    public function test_queue(): void
    {
        $command = new QueuedJobCommand;

        $queue = m::mock(Queue::class);
        $queue->shouldReceive('push')->with($command, m::any(), m::any())->once();

        $this->assertFalse($command->testRun);

        $command->queue($queue);

        $this->assertTrue($command->testRun);
        $this->assertIsArray($command->dispatchedBy);
    }

    public function test_debounce_call(): void
    {
        $command = new OtherQueuedJobCommand;

        $queue = m::mock(Queue::class);
        $queue->shouldReceive('push')->with($command, m::any(), m::any())->once();

        $command->mock = m::spy('caller');
        $command->queue($queue);

        $command->mock->shouldHaveReceived('call')->once();
    }
}

class QueuedJobCommand extends QueuedJob
{
    public $testRun = false;

    public function account()
    {
    }

    protected function queueTest()
    {
        $this->testRun = true;
    }

    /**
     * Whenever a queued job is used, the job should specify who it was dispatched by.
     *
     * @return mixed
     */
    protected function queueDispatchedBy()
    {
        $this->dispatchedBy = [];
    }
}

class OtherQueuedJobCommand extends QueuedJob
{
    public $mock;

    /**
     * Returns the account that this command is for.
     */
    public function account()
    {
        // TODO: Implement account() method.
    }

    /**
     * Whenever a queued job is used, the job should specify who it was dispatched by.
     *
     * @return mixed
     */
    protected function queueDispatchedBy()
    {
        // TODO: Implement queueDispatchedBy() method.
    }

    protected function queueDebounce()
    {
        $this->mock->call();
    }
}
