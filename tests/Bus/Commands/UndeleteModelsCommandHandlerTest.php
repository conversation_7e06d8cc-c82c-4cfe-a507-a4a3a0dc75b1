<?php

namespace Tests\Bus\Commands;

use Illuminate\Database\Eloquent\Collection;
use Mockery as m;
use Platform\Bus\Commands\UndeleteModelsCommand;
use Platform\Bus\Commands\UndeleteModelsCommandHandler;
use Platform\Database\Repository;
use Tests\Database\Eloquent\ModelStub;
use Tests\TestCase;

final class UndeleteModelsCommandHandlerTest extends TestCase
{
    private UndeleteModelsCommandHandler $handler;

    public function init()
    {
        $this->handler = new UndeleteModelsCommandHandler;
    }

    public function testCopying(): void
    {
        $collection = new Collection([new ModelStub]);

        $repository = m::mock(Repository::class);
        $repository->shouldReceive('getTrashedByIds')->once()->with([1])->andReturn($collection);
        $repository->shouldReceive('restore')->once()->with($collection);

        $command = new UndeleteModelsCommand([1], $repository);

        $this->handler->handle($command);
    }
}
