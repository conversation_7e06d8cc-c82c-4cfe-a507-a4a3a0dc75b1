<?php

namespace Tests\Bus\Commands;

use Illuminate\Database\Eloquent\Collection;
use Mockery as m;
use Platform\Bus\Commands\CopyModelsCommand;
use Platform\Bus\Commands\CopyModelsCommandHandler;
use Platform\Database\Repository;
use Tests\Database\Eloquent\ModelStub;
use Tests\TestCase;

final class CopyModelsCommandHandlerTest extends TestCase
{
    private CopyModelsCommandHandler $handler;

    public function init()
    {
        $this->handler = new CopyModelsCommandHandler;
    }

    public function test_copying_of_existing_models(): void
    {
        $repository = m::mock(Repository::class);
        $repository->shouldReceive('copyByIds')->once()->with([1, 3])->andReturn(new Collection([new ModelStub]));

        $command = new CopyModelsCommand([1, 3], $repository);

        $this->handler->handle($command);
    }
}
