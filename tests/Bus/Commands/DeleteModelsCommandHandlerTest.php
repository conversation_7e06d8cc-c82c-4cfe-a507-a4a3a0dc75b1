<?php

namespace Tests\Bus\Commands;

use Eloquence\Database\Model;
use Mockery as m;
use Platform\Bus\Commands\DeleteModelsCommand;
use Platform\Bus\Commands\DeleteModelsCommandHandler;
use Platform\Database\Repository;
use Tests\TestCase;

final class DeleteModelsCommandHandlerTest extends TestCase
{
    private DeleteModelsCommandHandler $handler;

    public function init()
    {
        $this->handler = new DeleteModelsCommandHandler;
    }

    public function testCopying(): void
    {
        $model = m::mock(Model::class);
        $model->shouldReceive('releaseEvents')->once()->andReturn([]);

        $repository = m::mock(Repository::class);
        $repository->shouldReceive('getById')->once()->with(2)->andReturn(null);
        $repository->shouldReceive('getById')->once()->with(5)->andReturn($model);

        $repository->shouldReceive('delete')->once()->with($model);

        $command = new DeleteModelsCommand([2, 5], $repository);

        $this->handler->handle($command);
    }
}
