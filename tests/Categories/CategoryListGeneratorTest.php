<?php

namespace Tests\Categories;

use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Mockery as m;
use Platform\Categories\CategoryList;
use Platform\Categories\CategoryListGenerator;
use Platform\Categories\CategorySlugListGenerator;
use Platform\Database\Repository;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tests\TestCase;

final class CategoryListGeneratorTest extends TestCase
{
    private $repository;

    private $translator;

    /** @var CategoryListGenerator */
    private $generator;

    public function init()
    {
        $this->repository = m::mock(Repository::class);
        $this->translator = m::mock(Engine::class);

        $this->generator = new CategoryListGenerator($this->repository, $this->translator);
    }

    public function test_it_generates_a_category_list(): void
    {
        $categories = new Collection([
            new Category(1, 'Category 1'),
            new Category(2, 'Category 2'),
            new Category(3, 'Category 3'),
        ]);

        $this->translator->shouldReceive('translate')->andReturn($categories);

        $categoryList = $this->generator->generate($categories);

        $this->assertInstanceOf(Collection::class, $categoryList);
        $this->assertEquals(3, $categoryList->get(CategoryList::NOPARENT)->count());
    }

    public function test_it_generates_a_category_list_with_divisions(): void
    {
        $categories = new Collection([
            new Category(1, 'Category 1'),
            new Category(2, 'Category 2'),
            new Category(3, 'Category 3', null, 3),
        ]);

        $this->translator->shouldReceive('translate')->andReturn($categories);

        $categoryList = $this->generator->generate($categories, true);

        $this->assertInstanceOf(Collection::class, $categoryList);
        $this->assertEquals(6, $categoryList->get(CategoryList::NOPARENT)->count());

        $this->assertStringContainsString('3.1', $categoryList->get(CategoryList::NOPARENT)->keys());
        $this->assertStringContainsString('3.2', $categoryList->get(CategoryList::NOPARENT)->keys());
        $this->assertStringContainsString('3.3', $categoryList->get(CategoryList::NOPARENT)->keys());
    }

    public function test_it_groups_parent_and_child_categories(): void
    {
        $categories = new Collection([
            new Category(1, 'Category'),
            new Category(2, 'Parent category'),
            new Category(3, 'Child category', 2),
        ]);

        $this->translator->shouldReceive('translate')->andReturn($categories);

        $categoryList = $this->generator->generate($categories);

        $this->assertInstanceOf(Collection::class, $categoryList);
        $this->assertEquals(2, $categoryList->get(CategoryList::NOPARENT)->count());
        $this->assertEquals(1, $categoryList->get('Parent category')->count());
        $this->assertEquals('Child category', $categoryList->get('Parent category')->first());
    }

    public function test_it_generates_a_category_list_in_json_format(): void
    {
        $categories = new Collection([
            new Category(1, 'Category 1'),
            new Category(2, 'Category 2'),
        ]);

        $this->translator->shouldReceive('translate')->andReturn($categories);

        $categoryList = $this->generator->toJson($categories);

        $this->assertEquals('[{"id":1,"name":"Category 1"},{"id":2,"name":"Category 2"}]', $categoryList);
    }

    public function test_it_generates_a_category_list_using_slugs(): void
    {
        $categories = new Collection([
            $category1 = new Category(1, 'Category 1', null, 1, 'dsaWSf'),
            $category2 = new Category(2, 'Category 2', null, 1, 'jlpAcz'),
        ]);
        $this->translator->shouldReceive('translate')->andReturn($categories);
        $this->generator = new CategorySlugListGenerator($this->repository, $this->translator);

        $categoryList = $this->generator->generate($categories, false, true);

        $this->assertEquals(2, $categoryList->get(CategoryList::NOPARENT)->count());
        $expectedList = [$category1->slug => $category1->name, $category2->slug => $category2->name];
        $this->assertEquals($expectedList, $categoryList->get(CategoryList::NOPARENT)->toArray());
    }

    public function test_it_generates_with_deleted_parents(): void
    {
        $categories = new Collection([
            new Category(1, 'Category 1'),
            new Category(2, 'Category 2', 4),
            new Category(3, 'Category 3'),
        ]);

        $this->translator->shouldReceive('translate')->andReturn($categories);
        // Infinite recursion if missing parent is not handled
        $this->repository->shouldReceive('getByIds')->andReturn(collect());

        $categoryList = $this->generator->generate($categories);

        $this->assertInstanceOf(Collection::class, $categoryList);

        $this->assertEquals(3, $categoryList->get(CategoryList::NOPARENT)->count());
    }

    public function test_it_generates_list_ordered_by_category_name(): void
    {
        $categories = new Collection([
            new Category(1, 'C Category'),
            new Category(2, 'A Category'),
            new Category(3, 'B Category'),
        ]);

        $this->translator->shouldReceive('translate')->andReturn($categories);

        $categoryList = $this->generator->generate($categories, divisions: true);

        $this->assertInstanceOf(Collection::class, $categoryList);
        $this->assertEquals(3, $categoryList->get(CategoryList::NOPARENT)->count());
        $this->assertEquals('A Category', $categoryList->get(CategoryList::NOPARENT)->first());
        $this->assertEquals('C Category', $categoryList->get(CategoryList::NOPARENT)->last());
    }
}

class Category
{
    public $id;

    public $name;

    public $parentId;

    public $divisions;

    public $slug;

    public function __construct(int $id, string $name, ?int $parentId = null, int $divisions = 1, $slug = null)
    {
        $this->id = $id;
        $this->name = $name;
        $this->parentId = $parentId;
        $this->divisions = $divisions;
        $this->slug = $slug ?? Str::random(8);
    }
}
