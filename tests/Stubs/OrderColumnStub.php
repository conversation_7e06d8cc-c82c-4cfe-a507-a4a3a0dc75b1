<?php

namespace Tests\Stubs;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Column;
use Platform\Search\Defaults;

class OrderColumnStub implements Api<PERSON>olumn, Column
{
    public function title()
    {
    }

    public function name(): string
    {
        return 'order column';
    }

    public function dependencies(): Collection
    {
    }

    public function field()
    {
        return DB::raw('field AS ordered.column');
    }

    public function value($record)
    {
    }

    public function html($record)
    {
    }

    public function default(): Defaults
    {
    }

    public function visible(): bool
    {
    }

    public function fixed(): bool
    {
    }

    public function priority(): int
    {
    }

    public function sortable(): bool
    {
        return true;
    }

    public function apiName()
    {
        return 'column';
    }

    public function apiValue($record)
    {
    }

    public function apiVisibility(): ApiVisibility
    {
    }
}
