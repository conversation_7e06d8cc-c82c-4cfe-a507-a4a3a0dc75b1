<?php

namespace Tests\Stubs\Module;

use Platform\Providers\ModuleServiceProvider;

class StubModuleServiceProvider extends ModuleServiceProvider
{
    public function getFiles(string $path): array
    {
        return parent::getFiles($path);
    }

    public function moduleDirectory(): string
    {
        return parent::moduleDirectory();
    }

    public function moduleNamespace(): string
    {
        return parent::moduleNamespace();
    }

    /**
     * Generate a fully qualified namespace for a given path and optionally a file.
     */
    public function namespaced(string $path, string $file = ''): string
    {
        return parent::namespaced($path, $file);
    }

    public function loadRoutes(): void
    {
        parent::loadRoutes();
    }
}
