<?php

namespace Tests\Stubs;

use Platform\Search\Defaults;
use Platform\Search\TranslatedColumn;

class TranslatedColumnStub extends TranslatedColumn
{
    public function title()
    {
        return 'translated_column_stub';
    }

    public function name(): string
    {
        return 'joined.alias_table';
    }

    public function html($record)
    {
        return $this->value($record);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function priority(): int
    {
        return 1;
    }

    public function fieldName(): string
    {
        return 'name';
    }

    public function sortable(): bool
    {
        return true;
    }
}
