<?php

namespace Tests\Language;

use Illuminate\Support\Facades\Config;
use Platform\Language\LanguageConfig;
use Tests\TestCase;

final class LanguageConfigTest extends TestCase
{
    public function test_it_can_get_rtl_config_value(): void
    {
        Config::set('lang.config.ar_AR.rtl', true);

        $languageConfig = app(LanguageConfig::class);

        $this->assertTrue($languageConfig->rtl('ar_AR'));
        $this->assertFalse($languageConfig->rtl('en_GB'));
    }

    public function test_it_can_get_system_config_value(): void
    {
        Config::set('lang.config', ['es_LA' => ['system' => 'es_MX.utf8']]);

        $languageConfig = app(LanguageConfig::class);

        $this->assertEquals('en_GB.utf8', $languageConfig->system('en_GB'));
        $this->assertEquals('es_MX.utf8', $languageConfig->system('es_LA'));
    }

    public function test_it_can_get_config_value(): void
    {
        Config::set('lang.cy_GB', 'cy');
        Config::set('lang.config', ['cy_GB' => ['funcaptcha' => 'en']]);

        $languageConfig = app(LanguageConfig::class);

        $this->assertEquals('cy', $languageConfig->recaptcha('cy_GB'));
        $this->assertEquals('en', $languageConfig->funcaptcha('cy_GB'));

        $this->assertNull($languageConfig->magicCaptcha('cy_GB'));
    }
}
