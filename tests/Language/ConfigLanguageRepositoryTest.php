<?php

namespace Tests\Language;

use Platform\Language\ConfigLanguageRepository;
use Platform\Language\Language;
use Tests\TestCase;

final class ConfigLanguageRepositoryTest extends TestCase
{
    private $repository;

    /**
     * @var array
     */
    private $languages;

    public function init()
    {
        $this->languages = [
            'en_GB' => 'English of Britannia',
            'en_US' => 'English of the New World',
        ];

        Language::setLanguages($this->languages);

        $this->repository = new ConfigLanguageRepository($this->languages);
    }

    public function test_it_returns_all_languages(): void
    {
        $languages = $this->repository->getAll();

        $this->assertCount(2, $languages);
        $this->assertEquals('en_GB', $languages[0]->code());
        $this->assertEquals('en_US', $languages[1]->code());
    }

    public function test_it_can_return_a_specific_language_by_code(): void
    {
        $this->assertSame('en_GB', $this->repository->getByCode('en_GB')->code());
    }

    public function test_it_can_retrieve_more_than_one_language_by_its_language_code(): void
    {
        $this->assertCount(2, $this->repository->getByCodes(['en_GB', 'en_US']));
    }
}
