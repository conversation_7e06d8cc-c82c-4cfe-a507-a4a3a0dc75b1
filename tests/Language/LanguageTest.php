<?php

namespace Tests\Language;

use Platform\Language\Language;
use Platform\Language\UnsupportedLanguage;
use Tests\TestCase;

final class LanguageTest extends TestCase
{
    public function test_it_can_work_with_valid_languages(): void
    {
        Language::setLanguages(['en_GB' => 'English (Great Britain)']);

        $language = new Language('en_GB');

        $this->assertSame('en_GB', $language->code());
        $this->assertSame('English (Great Britain)', $language->language());
    }

    public function test_it_deals_with_invalid_languages(): void
    {
        $this->expectException(UnsupportedLanguage::class);

        new Language('muffins!');
    }

    public function test_it_can_check_equivalence_accurately(): void
    {
        Language::setLanguages(['en_GB' => 'English (Great Britain)', 'en_US' => 'American']);

        $this->assertTrue((new Language('en_GB'))->equals(new Language('en_GB')));
    }
}
