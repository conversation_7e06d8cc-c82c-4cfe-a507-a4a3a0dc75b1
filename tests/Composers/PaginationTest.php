<?php

namespace Tests\Composers;

use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Mockery as m;
use Platform\Composers\Pagination;
use Tests\TestCase;

final class PaginationTest extends TestCase
{
    /** @var View */
    private $view;

    /** @var Request */
    private $request;

    public function init()
    {
        $this->request = m::mock(Request::class);
        $this->request->shouldReceive('except');
        $this->request->shouldReceive('path')->andReturn('/');

        $this->view = m::spy(View::class);
    }

    public function test_it_is_able_to_set_limits_correctly(): void
    {
        $paginator = new LengthAwarePaginator([], 100, 10, 3);

        $this->view->paginator = $paginator;

        $composer = new Pagination($this->request);
        $composer->compose($this->view);

        $this->view->shouldHaveReceived('with')->with('start', 1)->once();
        $this->view->shouldHaveReceived('with')->with('end', 10)->once();
    }

    public function test_it_can_output_pagination_links_with_extreme_results(): void
    {
        $paginator = new LengthAwarePaginator([], 145931, 10, 67);

        $this->view->paginator = $paginator;

        $composer = new Pagination($this->request);
        $composer->compose($this->view);

        $this->view->shouldHaveReceived('with')->with('start', 62)->once();
        $this->view->shouldHaveReceived('with')->with('end', 71)->once();
    }

    public function test_it_limits_the_last_page_value(): void
    {
        $paginator = new LengthAwarePaginator([], 145931, 1000, 146);

        $this->view->paginator = $paginator;

        $composer = new Pagination($this->request);
        $composer->compose($this->view);

        $this->view->shouldHaveReceived('with')->with('start', 137)->once();
        $this->view->shouldHaveReceived('with')->with('end', 146)->once();
    }
}
