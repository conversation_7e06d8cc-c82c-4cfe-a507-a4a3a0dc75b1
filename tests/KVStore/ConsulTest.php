<?php

namespace Tests\KVStore;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Psr7\Response;
use Mockery as m;
use Platform\KVStore\Consul;
use Tests\TestCase;

final class ConsulTest extends TestCase
{
    /** @var m\MockInterface */
    private $client;

    public function init()
    {
        $this->client = m::mock(Client::class);
    }

    public function test_create(): void
    {
        $this->client->shouldR<PERSON>eive('request')
            ->with('PUT', 'http://testing.domain:8500/v1/kv/domain.name')
            ->andReturn(m::mock(Response::class));

        (new Consul('testing.domain'))
            ->setClient($this->client)
            ->create('domain.name');
    }

    public function test_remove(): void
    {
        $this->client->shouldR<PERSON>eive('request')
            ->with('DELETE', 'http://testing.domain:8500/v1/kv/domain.name')
            ->andReturn(m::mock(Response::class));

        (new <PERSON>('testing.domain'))
            ->setClient($this->client)
            ->delete('domain.name');
    }

    public function test_check_exists(): void
    {
        $response = m::mock(Response::class);
        $response->shouldReceive('getBody->getContents')->andReturn('random value');

        $this->client->shouldReceive('request')
            ->with('GET', 'http://testing.domain:8500/v1/kv/domain.name')
            ->andReturn($response);

        $response = (new Consul('testing.domain'))
            ->setClient($this->client)
            ->get('domain.name');

        $this->assertEquals('random value', $response);
    }

    public function test_check_missing(): void
    {
        $this->client->shouldReceive('request')
            ->with('GET', 'http://testing.domain:8500/v1/kv/domain.name')
            ->andThrow(m::mock(ClientException::class));

        $response = (new Consul('testing.domain'))
            ->setClient($this->client)
            ->get('domain.name');

        $this->assertFalse($response);
    }
}
