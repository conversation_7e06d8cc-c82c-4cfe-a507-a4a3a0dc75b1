<?php

namespace Tests\Logging;

use Eloquence\Behaviours\Slug;
use Platform\Database\Eloquent\Model;
use Tests\TestCase;

class LoggableModelTest extends TestCase
{
    public function testModelsOnlyLogsIdAndSlugByDefault()
    {
        $model = new FakeModel;
        $model->id = 5;
        $model->slug = new Slug('some-slug');
        $model->property1 = 'some value';
        $model->property2 = 'another value';

        $logReady = $model->toLog();

        $this->assertEquals(5, $logReady['id']);
        $this->assertEquals('some-slug', $logReady['slug']);
        $this->assertArrayNotHasKey('property1', $logReady);
        $this->assertArrayNotHasKey('property2', $logReady);
    }

    public function testModelsCanLogAdditionalAttributes()
    {
        $model = new FakeModelWithAdditionalLogAttributes;
        $model->property1 = 'some value';
        $model->property2 = 'another value';

        $logReady = $model->toLog();

        $this->assertEquals('some value', $logReady['property1']);
        $this->assertArrayNotHasKey('property2', $logReady);
    }
}

class FakeModel extends Model
{
}

class FakeModelWithAdditionalLogAttributes extends Model
{
    protected function visibleLogAttributes(): array
    {
        return ['property1'];
    }
}
