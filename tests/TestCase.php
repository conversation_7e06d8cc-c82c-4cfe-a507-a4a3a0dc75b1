<?php

namespace Tests;

use Closure;
use Illuminate\Support\Arr;
use Mockery as m;
use Platform\PlatformServiceProvider;
use Platform\Strings\HtmlSanitiser;
use Platform\Strings\Sanitiser;
use Spatie\Html\Facades\Html;
use Symfony\Component\HtmlSanitizer\HtmlSanitizer as SymfonyHtmlSanitizer;
use Symfony\Component\HtmlSanitizer\HtmlSanitizerConfig;

class TestCase extends \Orchestra\Testbench\TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        $this->bindHtmlSanitiser();
        $this->init();
    }

    protected function tearDown(): void
    {
        \Mockery::close();
    }

    public function init()
    {
        // implement in children, if required
    }

    protected function getPackageProviders($app)
    {
        return [PlatformServiceProvider::class];
    }

    protected function getEnvironmentSetUp($app)
    {
        $this->setupConfiguration($app, 'features', 'payments', 'domains', 'products');

        if (! ($attribute = $this->requiresDatabase())) {
            return;
        }

        $attribute->setupDatabase($app);
    }

    protected function getApplicationAliases($app)
    {
        return [
            'HTML' => Html::class,
        ];
    }

    protected function mock($param, ?Closure $mock = null)
    {
        return m::mock($param);
    }

    protected function spy($param, ?Closure $mock = null)
    {
        return m::spy($param);
    }

    private function setupConfiguration($app, ...$configFiles)
    {
        foreach ($configFiles as $file) {
            $app['config']->set($file, require (__DIR__.'/../config/'.$file.'.php'));
        }
    }

    private function requiresDatabase(): false|Acceptance
    {
        $reflection = new \ReflectionClass($this);
        $attributes = $reflection->getAttributes(Acceptance::class);

        if (! $attributes) {
            return false;
        }

        return new (Arr::first($attributes)->getName());
    }

    public function bindHtmlSanitiser(): void
    {
        // Here we're simply injecting the 3rd party library to our own Sanitiser implementation.
        app()->bind(Sanitiser::class, function () {
            // Simple config to be overridden in other projects.
            $htmlSanitizerConfig = (new HtmlSanitizerConfig)
                ->allowRelativeLinks()
                ->allowSafeElements()
                ->allowElement('a', ['href', 'class', 'data-attr', 'data-redirector', 'rel', 'target', 'title'])
                ->allowElement('div', ['class', 'data-oembed-url', 'style'])
                ->allowElement('figure', ['class'])
                ->allowElement('iframe', ['allow', 'allowfullscreen', 'aria-label', 'frameborder', 'height', 'src', 'style', 'title', 'width'])
                ->allowElement('img', ['src', 'alt', 'title', 'width', 'height', 'style'])
                ->allowElement('p', ['class', 'style'])
                ->allowElement('span', ['class']);

            // Needed to instantiate sanitiser in Platform to run tests.
            return new HtmlSanitiser(new SymfonyHtmlSanitizer($htmlSanitizerConfig));
        });
    }
}
