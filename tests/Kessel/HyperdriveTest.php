<?php

namespace Tests\Kessel;

use Assert\InvalidArgumentException;
use AwardForce\Library\Authorization\Manager;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Promise\PromiseInterface;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Mockery as m;
use Platform\Authorisation\Consumer;
use Platform\Authorisation\Signatory;
use Platform\Authorisation\Signature;
use Platform\Kessel\Data\RegionalResource;
use Platform\Kessel\Data\RegionalResourceCollection;
use Platform\Kessel\Endpoints\AwardForce;
use Platform\Kessel\Exceptions\ResourceNotFound;
use Platform\Kessel\Exceptions\ValidationFailed;
use Platform\Kessel\Hyperdrive;
use Platform\Language\Language;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Tests\FakeStream;
use Tests\TestCase;
use Throwable;

final class HyperdriveTest extends TestCase
{
    /** @var m\MockInterface */
    protected $client;

    /** @var Hyperdrive */
    protected $hyperdrive;

    /** @var Signatory */
    protected $signatory;

    public function init()
    {
        $this->client = m::mock(Client::class);
        app()->instance(Client::class, $this->client);

        $manager = m::mock(Manager::class);
        app()->instance(Manager::class, $manager);

        $this->signatory = new class implements Signatory
        {
            public function signature(): Signature
            {
                return new Signature('af4', 'system');
            }
        };

        $manager->shouldReceive('has')->andReturn(true);
        $manager->shouldReceive('get')->andReturn($this->signatory);

        Config::set('kessel.endpoints', [
            'default' => 'https://myaf.com',
            'af_au' => 'https://au.af.com',
            'af_eu' => 'https://eu.af.com',
            'af_au_uat' => 'https://uat.au.af.com',
            'my_af' => 'https://myaf.com',
        ]);
        Config::set('kessel.key', 'apikey001');
        Config::set('kessel.system_consumer', MockSystemConsumer::class);
        Config::set('awardforce.regions', ['au', 'eu', 'us']);
    }

    public function test_get_resource(): void
    {
        $this->client->shouldReceive('request')
            ->with('get', 'https://myaf.com/accounts', $this->requestOptions(['query' => ['parameter' => 'one']]))->once()
            ->andReturn($this->response(['accounts' => 'response']));

        $response = (Hyperdrive::forMyAF())->get('accounts', ['parameter' => 'one']);

        $this->assertEquals(['accounts' => 'response'], $response);
    }

    /**
     * @throws Throwable
     */
    public function test_get_pool_for_af(): void
    {
        $regionalResourceCollection = new RegionalResourceCollection([
            new RegionalResource('au', 'accounts/123'),
            new RegionalResource('eu', 'accounts/456'),
        ]);

        $expectedResponses = [
            'au' => ['account' => 'response au'],
            'eu' => ['account' => 'response eu'],
        ];

        $this->client->shouldReceive('getAsync')
            ->andReturnUsing(function ($url) use ($expectedResponses) {
                $region = explode('.', parse_url($url, PHP_URL_HOST))[0];

                return m::mock(PromiseInterface::class, [
                    'wait' => $expectedResponses[$region],
                ]);
            });

        $responses = app(Hyperdrive::class)->getPoolForAf($regionalResourceCollection);

        $this->assertEquals($expectedResponses['au'], $responses[0]['value']);
        $this->assertEquals($expectedResponses['eu'], $responses[1]['value']);
    }

    public function test_create_resource(): void
    {
        $this->client->shouldReceive('request')
            ->with('post', 'https://myaf.com/accounts', $this->requestOptions(['json' => ['parameter' => 'one']]))->once()
            ->andReturn($this->response(['id' => 123]));

        (Hyperdrive::forMyAF())->create('accounts', ['parameter' => 'one']);
    }

    public function test_update_resource(): void
    {
        $this->client->shouldReceive('request')
            ->with('put', 'https://myaf.com/accounts/tEsTsLuG', $this->requestOptions(['json' => ['parameter' => 'one']]))->once()
            ->andReturn($this->response());

        (Hyperdrive::forMyAF())->update('accounts/tEsTsLuG', ['parameter' => 'one']);
    }

    public function test_delete_resource(): void
    {
        $this->client->shouldReceive('request')
            ->with('delete', 'https://myaf.com/accounts/tEsTsLuG', $this->requestOptions())->once()
            ->andReturn($this->response());

        (Hyperdrive::forMyAF())->delete('accounts/tEsTsLuG');
    }

    public function test_delete_resources_bulk(): void
    {
        $this->client->shouldReceive('request')
            ->with('delete', 'https://myaf.com/accounts', $this->requestOptions([
                'json' => [
                    'ids' => [
                        'id1',
                        'id2',
                        'id3',
                    ],
                ],
            ]))->once()
            ->andReturn($this->response());

        (Hyperdrive::forMyAF())->deleteBulk('accounts', ['ids' => [
            'id1', 'id2', 'id3',
        ]]);
    }

    public function test_it_can_ping_the_kessel_endpoint(): void
    {
        $this->client->shouldReceive('request')
            ->with('get', 'https://myaf.com/ping', $this->requestOptions())->once()
            ->andReturn($this->response());

        (Hyperdrive::forMyAF())->ping();
    }

    public function test_it_can_handle_validation_error_responses(): void
    {
        $this->expectException(ValidationFailed::class);
        $this->setupFailure(422);

        (Hyperdrive::forMyAF())->ping();
    }

    public function test_it_can_handle_404s_correctly(): void
    {
        $this->expectException(ResourceNotFound::class);
        $this->setupFailure(404);

        (Hyperdrive::forMyAF())->ping();
    }

    public function test_it_succeeds_with_valid_region(): void
    {
        $this->client->shouldReceive('request')
            ->with('get', 'https://au.af.com/accounts', $this->requestOptions(['query' => ['parameter' => 'one']]))->once()
            ->andReturn($this->response(['accounts' => 'response']));

        $response = (Hyperdrive::forAF(AwardForce::AU))->get('accounts', ['parameter' => 'one']);

        $this->assertEquals(['accounts' => 'response'], $response);
    }

    public function test_it_fails_with_invalid_region(): void
    {
        $this->expectException(InvalidArgumentException::class);

        $response = (Hyperdrive::forAF('abc'))->get('accounts', ['parameter' => 'one']);
    }

    public function test_it_succeeds_with_uat_environment(): void
    {
        $this->client->shouldReceive('request')
            ->with('get', 'https://uat.au.af.com/accounts', $this->requestOptions(['query' => ['parameter' => 'one']]))->once()
            ->andReturn($this->response(['accounts' => 'response']));

        $response = (Hyperdrive::forAF(AwardForce::AU, 'uat'))->get('accounts', ['parameter' => 'one']);
        $this->assertEquals(['accounts' => 'response'], $response);
    }

    /**
     * @param  mixed  $return
     */
    private function response($return = null): ResponseInterface
    {
        return tap(m::mock(ResponseInterface::class), function ($mock) use ($return) {
            $mock->shouldReceive('getBody')->andReturn(new FakeStream(json_encode($return)));
        });
    }

    public function requestOptions(array $options = []): array
    {
        return array_merge($options, [
            'headers' => [
                'Accept' => 'application/json',
                Hyperdrive::HEADER_KEY => 'apikey001',
                Hyperdrive::HEADER_SIGNATURE => (string) $this->signatory->signature(),
                Hyperdrive::HEADER_ORIGIN => null,
            ],
        ]);
    }

    private function setupFailure(int $statusCode)
    {
        $exception = new ClientException('Failed', m::mock(RequestInterface::class), new Response($statusCode));

        $this->client->shouldReceive('request')->andThrow($exception);
    }
}

class MockSystemConsumer implements Consumer, Signatory
{
    public function accounts()
    {
        // TODO: Implement accounts() method.
    }

    public function language(): Language
    {
        return new Language('en_GB');
    }

    public function id()
    {
        // TODO: Implement id() method.
    }

    public function type()
    {
        return 'system';
    }

    public function roles()
    {
        return new Collection;
    }

    public function user()
    {
        // TODO: Implement user() method.
    }

    public function timezone()
    {
        // TODO: Implement timezone() method.
    }

    public function name(): string
    {
        return 'system';
    }

    public function signature(): Signature
    {
        return new Signature('af4', 'system');
    }

    public function globalId(): ?string
    {
        // TODO: Implement globalId() method.
    }

    public function globalAccountId(): ?string
    {
        // TODO: Implement globalAccountId() method.
    }

    public function isChapterLimited(): bool
    {

    }

    public function strictLanguage(): Language
    {
        return $this->language();
    }
}
