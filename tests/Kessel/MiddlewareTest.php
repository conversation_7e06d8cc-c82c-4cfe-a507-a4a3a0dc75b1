<?php

namespace Tests\Kessel;

use Illuminate\Http\Request;
use Platform\Kessel\Hyperdrive;
use Platform\Kessel\Middleware;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

final class MiddlewareTest extends TestCase
{
    public function test_invalid_token(): void
    {
        $this->expectException(HttpException::class);

        $request = \Mockery::mock(Request::class);
        $request->shouldReceive('header')->with(Hyperdrive::HEADER_KEY)->once()->andReturn('otherkey');
        $request->shouldReceive('url')->once();
        $request->shouldReceive('except')->with('password')->once();

        $middleware = new Middleware(['key' => 'secretkey']);

        $middleware->handle($request, function () {
            throw new \Exception('This should not be run!');
        });
    }

    public function test_missing_signature(): void
    {
        $this->expectException(HttpException::class);

        $request = \Mockery::mock(Request::class);
        $request->shouldReceive('header')->with(Hyperdrive::HEADER_KEY)->once()->andReturn('secretkey');
        $request->shouldReceive('header')->with(Hyperdrive::HEADER_SIGNATURE)->once()->andReturnNull();
        $request->shouldReceive('url')->once();
        $request->shouldReceive('except')->with('password')->once();

        $middleware = new Middleware(['key' => 'secretkey']);

        $middleware->handle($request, function () {
            throw new \Exception('This should not be run!');
        });
    }

    public function test_authenticated(): void
    {
        $request = \Mockery::mock(Request::class);
        $request->shouldReceive('header')->with(Hyperdrive::HEADER_KEY)->once()->andReturn('secretkey');
        $request->shouldReceive('header')->with(Hyperdrive::HEADER_SIGNATURE)->once()->andReturn('signature');
        $request->shouldReceive('url')->once();
        $request->shouldReceive('except')->with('password')->once();

        $middleware = new Middleware(['key' => 'secretkey']);

        $response = $middleware->handle($request, function () {
            return 'Valid!';
        });

        $this->assertEquals('Valid!', $response);
    }
}
