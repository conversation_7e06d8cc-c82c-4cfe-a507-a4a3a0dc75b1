<?php

namespace Tests\Kessel\Endpoints;

use Illuminate\Support\Facades\Config;
use Platform\Kessel\Endpoints\AwardForce;
use Tests\TestCase;

final class AwardForceTest extends TestCase
{
    public function init()
    {
        Config::set('kessel.endpoints', [
            'af_au' => 'https://au.af.com',
            'af_au_uat' => 'https://uat.au.af.com',
        ]);
        Config::set('awardforce.regions', ['au', 'eu', 'us']);
    }

    public function testDefaultValue(): void
    {
        $this->assertEquals('https://au.af.com', (new AwardForce(AwardForce::AU))->value());
    }

    public function testUatValue(): void
    {
        $this->assertEquals('https://uat.au.af.com', (new AwardForce(AwardForce::AU, true))->value());
    }
}
