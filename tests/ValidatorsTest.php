<?php

namespace Tests;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;

final class ValidatorsTest extends TestCase
{
    public function test_domain(): void
    {
        $this->assertFails('invalid@domain', 'domain');
        $this->assertFails('invalid', 'domain');
        $this->assertFails('-invalid.domain', 'domain');
        $this->assertFails('invalid.domain-', 'domain');
        $this->assertFails('invalid-.domain', 'domain');
        $this->assertFails('invalid.-domain', 'domain');
        $this->assertFails('.invalid.domain', 'domain');
        $this->assertFails('invalid.domain.', 'domain');

        $this->assertPasses('valid.domain', 'domain');       // Yes, this is a valid domain.
    }

    public function test_domain_reserved(): void
    {
        $this->assertFails($this->reserved(), 'domain_reserved');
        $this->assertFails($this->reserved().'.domain', 'domain_reserved');

        $this->assertPasses('available', 'domain_reserved');
        $this->assertPasses('available.domain', 'domain_reserved');
        $this->assertPasses('available.'.$this->reserved(), 'domain_reserved');
    }

    public function test_domain_whitelabel(): void
    {
        $this->assertFails('invalid', 'domain_whitelabel');
        $this->assertFails($this->whiteLabel().'.domain', 'domain_whitelabel');

        $this->assertPasses($this->whiteLabel(), 'domain_whitelabel');
        $this->assertPasses('valid.'.$this->whiteLabel(), 'domain_whitelabel');
    }

    public function test_subdomain(): void
    {
        $this->assertFails('invalid@domain', 'subdomain');
        $this->assertFails('invalid.domain', 'subdomain');
        $this->assertFails('-a', 'subdomain');
        $this->assertFails('a-', 'subdomain');

        $this->assertPasses('valid', 'subdomain');
        $this->assertPasses('a', 'subdomain');
    }

    private function whiteLabel(): string
    {
        return Arr::random(config('domains.white_label'));
    }

    private function reserved()
    {
        return Arr::random(config('domains.reserved'));
    }

    private function assertPasses($value, $rule, array $context = [])
    {
        $values = array_merge($context, ['domain' => $value]);

        $this->assertTrue(
            Validator::make($values, ['domain' => $rule])->passes(),
            "Failed asserting '{$value}' passes '{$rule}' validation."
        );
    }

    private function assertFails($value, $rule, array $context = [])
    {
        $values = array_merge($context, ['domain' => $value]);

        $this->assertFalse(
            Validator::make($values, ['domain' => $rule])->passes(),
            "Failed asserting '{$value}' fails '{$rule}' validation."
        );
    }
}
