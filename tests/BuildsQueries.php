<?php

namespace Tests;

use Illuminate\Database\ConnectionInterface;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\Grammars\Grammar;
use Illuminate\Database\Query\Processors\Processor;
use Mockery as m;

trait BuildsQueries
{
    private $connection;

    private $grammar;

    private $processor;

    public function preparedQuery($query)
    {
        $sql = str_replace(['?'], ["'%s'"], $query->toSql());

        return vsprintf($sql, $query->getBindings());
    }

    protected function fakeBuilder()
    {
        $this->builderParams();

        return m::mock(Builder::class, [$this->connection, $this->grammar, $this->processor])->makePartial();
    }

    protected function builder()
    {
        $this->builderParams();

        return new Builder($this->connection, $this->grammar, $this->processor);
    }

    private function builderParams()
    {
        $this->connection = app(ConnectionInterface::class);
        $this->grammar = app(Grammar::class);
        $this->processor = app(Processor::class);
    }
}
