<?php

namespace Providers;

use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use Tests\Stubs\Module\Events\Subscribers\Subscriber;
use Tests\Stubs\Module\StubModuleServiceProvider;
use Tests\TestCase;

class ModuleServiceProviderTest extends TestCase
{
    public function testNamespacedGeneratesCorrectNamespace(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $this->assertEquals(
            'Tests\Stubs\Module\Events\Subscribers\UserSubscriber',
            $provider->namespaced('Events/Subscribers', 'UserSubscriber.php')
        );
    }

    public function testGetFilesReturnsPhpFiles(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $files = $provider->getFiles('Http/Routes');
        $this->assertNotEmpty($files);
        foreach ($files as $file) {
            $this->assertStringEndsWith('.php', $file);
        }
    }

    public function testGetFilesExcludesTestFiles(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $files = $provider->getFiles('Http/Routes');
        foreach ($files as $file) {
            $this->assertFalse(Str::endsWith($file, ['Test.php', 'test.php']));
        }
    }

    public function testGetFilesReturnsEmptyArrayForNonExistentDirectory(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $this->assertEmpty($provider->getFiles('NonExistentDirectory'));
    }

    public function testModuleDirectoryReturnsCorrectPath(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $this->assertStringEndsWith('Stubs/Module', $provider->moduleDirectory());
    }

    public function testModuleNamespaceReturnsCorrectNamespace(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $this->assertEquals('Tests\Stubs\Module', $provider->moduleNamespace());
    }

    public function testBootEventSubscribers(): void
    {
        Event::fake();
        Event::shouldReceive('subscribe')->with(Subscriber::class)->once();
        $provider = new StubModuleServiceProvider($this->app);
        $provider->boot();
    }

    public function testRegisterRoutes(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $provider->boot();
        $provider->loadRoutes();
        $this->assertStringEndsWith('/test', route('test.index'));
    }

    public function testLoadMigrations(): void
    {
        $provider = new StubModuleServiceProvider($this->app);
        $provider->boot();
        $migrator = $this->app->make('migrator');
        $migrations = $migrator->getMigrationFiles($migrator->paths());

        $this->assertNotEmpty($migrations);
        $this->assertArrayHasKey('2024-10-23_1234556_stub_migration', $migrations);
    }
}
