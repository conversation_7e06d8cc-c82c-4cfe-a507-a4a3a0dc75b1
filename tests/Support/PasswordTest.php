<?php

namespace Tests\Support;

use Platform\Support\StrongPasswordGenerator;
use Tests\TestCase;

final class PasswordTest extends TestCase
{
    public function testPasswordGeneration(): void
    {
        $generatedPassword = StrongPasswordGenerator::generate();

        $this->assertMatchesRegularExpression('/(?=.*([A-Z]))/', $generatedPassword);
        $this->assertMatchesRegularExpression('/(?=.*([a-z]))/', $generatedPassword);
        $this->assertMatchesRegularExpression('/(?=.*([0-9]))/', $generatedPassword);
        $this->assertMatchesRegularExpression('/(?=.*([~`\!@#\$%\^&\*\(\)_+=-\{\}\[\]]))/', $generatedPassword);
        $this->assertEquals(12, strlen($generatedPassword));
    }

    public function testPasswordGenerationWithInvalidLengthProvided(): void
    {
        $generatedPassword = StrongPasswordGenerator::generate(3);
        $this->assertGreaterThanOrEqual(12, strlen($generatedPassword));
    }
}
