<?php

namespace Tests\Support;

use Illuminate\Contracts\Cookie\QueueingFactory;
use Illuminate\Http\Request;
use Mockery as m;
use Platform\Support\UserTracker;
use Tests\TestCase;

final class UserTrackerTest extends TestCase
{
    private $request;

    private $cookie;

    private $tracker;

    public function init()
    {
        $this->request = Request::create('uri');
        $this->cookie = m::mock(QueueingFactory::class);

        $this->tracker = new UserTracker($this->request, $this->cookie);
    }

    public function test_it_can_return_the_users_ip(): void
    {
        $this->assertSame('127.0.0.1', $this->tracker->userIP());
    }

    public function test_it_returns_a_browser_fingerprint(): void
    {
        $fingerprint = json_decode($this->tracker->browserFingerprint(), true);

        $this->assertArrayHasKey('accept', $fingerprint);
        $this->assertArrayHasKey('encoding', $fingerprint);
        $this->assertArrayHasKey('lang', $fingerprint);
        $this->assertArrayHasKey('useragent', $fingerprint);
    }

    public function test_it_returns_the_user_cookie(): void
    {
        $request = m::mock(Request::class);
        $request->shouldReceive('cookie')->andReturn(true);

        $cookie = m::mock(QueueingFactory::class);

        $tracker = new UserTracker($request, $cookie);

        $this->assertTrue($tracker->cookie());
    }

    public function test_it_returns_a_generated_cookie(): void
    {
        $request = m::mock(Request::class);
        $request->shouldReceive('cookie')->andReturn(false);

        $cookie = m::mock(QueueingFactory::class);
        $cookie->shouldReceive('queue');

        $tracker = new UserTracker($request, $cookie);

        $this->assertSame(32, strlen($tracker->cookie()));
    }
}
