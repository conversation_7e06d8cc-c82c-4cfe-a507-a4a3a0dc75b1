<?php

namespace Tests\Support\Values;

use Assert\AssertionFailedException;
use Assert\InvalidArgumentException;
use Platform\Support\Values\Email;
use Tests\TestCase;

final class EmailTest extends TestCase
{
    public function test_it_is_initialisable(): void
    {
        $email = new Email('<EMAIL>');

        $this->assertInstanceOf(Email::class, $email);
        $this->assertSame('<EMAIL>', (string) $email);
    }

    public function test_it_validates_email_input(): void
    {
        $this->expectException(InvalidArgumentException::class);

        new Email('invalid email');
    }

    public function test_it_can_check_valid_emails(): void
    {
        $this->assertTrue(Email::check('<EMAIL>'));
        $this->assertFalse(Email::check('invalid email'));
    }

    public function test_it_raises_assertion_failed_exception_when_email_is_null(): void
    {
        $this->expectException(AssertionFailedException::class);

        new Email(null);
    }
}
