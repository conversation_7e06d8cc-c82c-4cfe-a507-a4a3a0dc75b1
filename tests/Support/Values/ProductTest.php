<?php

namespace Tests\Support\Values;

use Platform\Features\Plan;
use Platform\Support\Values\Product;
use Tests\TestCase;

final class ProductTest extends TestCase
{
    public function test_it_is_initialisable(): void
    {
        $this->assertInstanceOf(Product::class, new Product('some-product'));
    }

    public function test_plan_is_retrievable_from_product(): void
    {
        $product = new Product('USD-m-starter-1');

        $this->assertInstanceOf(Plan::class, $product->plan());
        $this->assertSame('starter', (string) $product->plan());
    }

    public function test_it_returns_the_product_version(): void
    {
        $product = new Product('USD-m-starter-1');

        $this->assertSame(1, $product->version());
    }
}
