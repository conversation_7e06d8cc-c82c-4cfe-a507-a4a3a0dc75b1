<?php

namespace Tests\Support\Values;

use Platform\Support\Values\PhoneNumber;
use Tests\TestCase;

final class PhoneNumberTest extends TestCase
{
    public function test_it_is_initialisable(): void
    {
        $number = new PhoneNumber('+1234512334');

        $this->assertInstanceOf(PhoneNumber::class, $number);
        $this->assertSame('+1234512334', (string) $number);
    }

    public function test_it_validates_phone_numbers(): void
    {
        $this->assertTrue(PhoneNumber::check('+7648723486'));
        $this->assertFalse(PhoneNumber::check('9872349874'));
        $this->assertFalse(PhoneNumber::check('number'));
    }

    public function test_it_removes_leading_zeroes(): void
    {
        $this->assertEquals('+94712345678', (string) (new PhoneNumber('+94712345678')));
        $this->assertEquals('+94712345678', (string) (new PhoneNumber('+940712345678')));
        $this->assertEquals('+919090974785', (string) (new PhoneNumber('+9109090974785')));
        $this->assertEquals('+33112345678', (string) (new PhoneNumber('+330112345678')));
        $this->assertEquals('+61412345678', (string) (new PhoneNumber('+610412345678')));

        // Ensure we don't break valid numbers
        $this->assertEquals('+2250551234567', (string) (new PhoneNumber('+2250551234567')));
        $this->assertEquals('+380666777888', (string) (new PhoneNumber('+380666777888')));
        $this->assertEquals('+12025550156', (string) (new PhoneNumber('+12025550156')));
    }
}
