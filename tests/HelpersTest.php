<?php

namespace Tests;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\HtmlString;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Psr\Http\Message\ResponseInterface;

final class HelpersTest extends TestCase
{
    public function test_format_size(): void
    {
        $this->assertEquals('0 B', format_size(0));
        $this->assertEquals('1 B', format_size(1));
        $this->assertEquals('2 B', format_size(2));
        $this->assertEquals('1 KiB', format_size(1024));
        $this->assertEquals('1.0 MiB', format_size(1024 * 1024));
        $this->assertEquals('1.0 GiB', format_size(1024 * 1024 * 1024));
        $this->assertEquals('3.6 GiB', format_size(3833388576));            // Random file on my laptop!
    }

    public function test_format_time(): void
    {
        $this->assertEquals('00:00:00', format_time(0));
        $this->assertEquals('00:01:00', format_time(60));
        $this->assertEquals('01:00:00', format_time(60 * 60));
        $this->assertEquals('100:00:00', format_time(60 * 60 * 100));
        $this->assertEquals('101:02:16', format_time(60 * 60 * 101 + 120 + 16));
    }

    public function testParseResponse(): void
    {
        $response = $this->mockResponse(['data' => 'value']);
        $this->assertEquals(['data' => 'value'], parse_response($response));

        $response = $this->mockResponse('string value');
        $this->assertEquals('string value', parse_response($response));
    }

    public function testSortConfigList(): void
    {
        Config::set('tests.configlist', [
            'alfa',
            'Bravo?',
            'Charlie!',
            'Delta???',
            'echo!!!',
        ]);

        $lang = 'en_GB';

        $this->assertEquals([
            'alfa' => "$lang.alfa",
            'Bravo?' => "$lang.bravo?",
            'Charlie!' => "$lang.charlie!",
            'Delta???' => "$lang.delta???",
            'echo!!!' => "$lang.echo!!!",
        ], sort_config_list('tests.configlist', $lang));
    }

    public function testKeyValuePairs(): void
    {
        $input = ['all' => 'All', 'active' => 'Active'];

        $output = [
            ['id' => 'all', 'name' => 'All'],
            ['id' => 'active', 'name' => 'Active'],
        ];

        $this->assertEquals(key_value_pairs($input, 'id', 'name'), $output);
    }

    public function testValidateSlug(): void
    {
        $this->assertFalse(validate_slug(null));
        $this->assertFalse(validate_slug(0));
        $this->assertFalse(validate_slug(''));
        $this->assertFalse(validate_slug('XrJ'));

        $this->assertTrue(validate_slug('XrJbqdjl'));

        $this->assertFalse(validate_slug('abcd1234'));
        $this->assertTrue(validate_slug('abcd1234', true));
    }

    private function mockResponse($value): m\MockInterface
    {
        $response = m::mock(ResponseInterface::class);
        $response->shouldReceive('getBody')->andReturn(new FakeStream(json_encode($value)));

        return $response;
    }

    public function testValidateToken(): void
    {
        $this->assertFalse(validate_token(null));
        $this->assertFalse(validate_token(0));
        $this->assertFalse(validate_token(''));
        $this->assertFalse(validate_token('XrJ'));

        $this->assertTrue(validate_token('XrJbqdjlXrJbqdjl'));
        $this->assertTrue(validate_token('XrJbqdjlXrJbqdjlXrJbqdjlXrJbqdjl'));
    }

    public function testValidateIdentifierType(): void
    {
        $this->expectException(\Exception::class);
        $this->assertFalse(identifier_type(null));

        $this->expectException(\Exception::class);
        $this->assertFalse(identifier_type(0));

        $this->expectException(\Exception::class);
        $this->assertFalse(identifier_type(''));

        $this->expectException(\Exception::class);
        $this->assertFalse(identifier_type('XrJ'));

        $this->assertEquals('slug', identifier_type('aBcDeFgH'));
        $this->assertEquals('token', identifier_type('aBcDeFgHaBcDeFgH'));
        $this->assertEquals('slug', identifier_type('abcd1234', true));
    }

    public function testIsJson(): void
    {
        $this->assertFalse(is_json('1'));
        $this->assertFalse(is_json('asda'));
        $this->assertTrue(is_json('{"1": "asda"}'));
    }

    public function testLinkHelper()
    {
        Route::get('fake/route', fn() => 'response')->name('fake.route');

        $withText = (string) link_to('fake.com', 'Fake Text');
        $withoutText = (string) link_to('fake.com');
        $routeWithText = (string) link_to_route('fake.route', 'Fake Text');
        $routeWithoutText = (string) link_to_route('fake.route');

        $this->assertEquals('<a href="fake.com">Fake Text</a>', $withText);
        $this->assertEquals('<a href="fake.com">fake.com</a>', $withoutText);
        $this->assertEquals('<a href="http://localhost/fake/route">Fake Text</a>', $routeWithText);
        $this->assertEquals('<a href="http://localhost/fake/route">http://localhost/fake/route</a>', $routeWithoutText);
    }
}
