<?php

namespace Tests\Features;

use Platform\Features\Feature;
use Platform\Features\Features;
use Tests\TestCase;

final class FeaturesTest extends TestCase
{
    public function test_it_merge_two_collections_together(): void
    {
        $oldFeatures = new Features([new Feature('audit', 'enabled'), new Feature('order_payments', 'disabled')]);
        $newFeatures = new Features([new Feature('audit', 'disabled'), new Feature('multi_chapter', 'enabled')]);

        $mergedFeatures = $oldFeatures->merge($newFeatures);

        $this->assertCount(3, $mergedFeatures->all());
        $this->assertTrue($this->find($mergedFeatures, 'audit')->disabled());
        $this->assertTrue($this->find($mergedFeatures, 'order_payments')->disabled());
        $this->assertTrue($this->find($mergedFeatures, 'multi_chapter')->enabled());
    }

    public function test_it_can_draft_a_collection_from_configuration_values(): void
    {
        $features = Features::fromConfig(config('features.features'), 'disabled');
        $planFeatures = Features::fromConfig(config('features.plans.starter.features'), 'enabled');

        $features = $features->merge($planFeatures);

        // This is one of the few features enabled for starter accounts, so checking that this is enabled proves
        // it was able to construct the collection properly AND merge those values from the base feature set.
        $this->assertTrue($this->find($features, 'judging_mode.vip_judging')->enabled());
    }

    public function testItCanFilterOutOnlyEnabledFeatures(): void
    {
        $feature1 = new Feature('audit', 'enabled');
        $feature2 = new Feature('multi_chapter', 'disabled');

        $enabledFeatures = (new Features([$feature1, $feature2]))->enabled();

        $this->assertCount(1, $enabledFeatures);
        $this->assertContains($feature1, $enabledFeatures);
        $this->assertNotContains($feature2, $enabledFeatures);
    }

    public function testCommonEnabledFeatures(): void
    {
        $feature1 = new Feature('audit', 'enabled');
        $feature2 = new Feature('multi_chapter', 'enabled');
        $feature3 = new Feature('order_payments', 'enabled');
        $feature4 = new Feature('documents', 'enabled');
        $collection1 = new Features([$feature1, $feature2, $feature3]);
        $collection2 = new Features([$feature2, $feature3, $feature4]);

        $result = $collection1->commonEnabledFeatures($collection2);

        $this->assertCount(2, $result);
        $this->assertNotContains($feature1, $result);
        $this->assertContains($feature2, $result);
        $this->assertContains($feature3, $result);
        $this->assertNotContains($feature4, $result);
    }

    private function find(Features $features, string $feature)
    {
        return $features->filter(function ($f) use ($feature) {
            return $f->value() == $feature;
        })->first();
    }
}
