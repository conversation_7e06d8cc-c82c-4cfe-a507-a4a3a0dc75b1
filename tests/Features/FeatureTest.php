<?php

namespace Tests\Features;

use Platform\Features\Feature;
use Platform\Features\InvalidFeature;
use Platform\Features\InvalidFeatureStatus;
use Platform\Features\NullFeature;
use Tests\TestCase;

final class FeatureTest extends TestCase
{
    public function test_it_throws_invalid_feature_exception(): void
    {
        $this->expectException(InvalidFeature::class);
        $feature = new Feature('invalid', 'enabled');
    }

    public function test_it_throws_invalid_feature_status_exception(): void
    {
        $this->expectException(InvalidFeatureStatus::class);
        $feature = new Feature('order_payments', 'off');
    }

    public function test_it_instantiates_a_feature(): void
    {
        $feature = Feature::new('order_payments', 'enabled');
        $this->assertInstanceOf(Feature::class, $feature);
    }

    public function test_it_instantiates_a_null_feature(): void
    {
        $nullFeature = Feature::new('null', 'enabled');

        $this->assertInstanceOf(NullFeature::class, $nullFeature);
        $this->assertTrue($nullFeature->disabled());
        $this->assertFalse($nullFeature->enabled());
        $this->assertFalse($nullFeature->valid());
    }
}
