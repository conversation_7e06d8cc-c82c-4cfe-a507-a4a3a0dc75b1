<?php

namespace Tests\Features;

use Assert\AssertionFailedException;
use Platform\Features\Feature;
use Platform\Features\Features;
use Platform\Features\Plan;
use Tests\TestCase;

final class PlanTest extends TestCase
{
    public function test_it_is_initialisable(): void
    {
        $plan = new Plan('professional');

        $this->assertInstanceOf(Plan::class, $plan);
    }

    public function test_it_handles_invalid_plan_values(): void
    {
        $this->expectException(AssertionFailedException::class);

        new Plan('invalid plan');
    }

    public function test_it_can_return_all_features_for_a_plan(): void
    {
        $plan = new Plan('enterprise');
        $features = $plan->features();

        $this->assertInstanceOf(Features::class, $features);
        $this->assertGreaterThan(0, $features->count());
        $this->assertInstanceOf(Feature::class, $features->pop());
    }
}
