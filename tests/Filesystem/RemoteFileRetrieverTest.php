<?php

namespace Tests\Filesystem;

use Illuminate\Contracts\Filesystem\Factory;
use League\Flysystem\FilesystemException;
use League\Flysystem\MountManager;
use Mockery as m;
use Platform\Filesystem\RemoteFileRetriever;
use Tests\TestCase;

final class RemoteFileRetrieverTest extends TestCase
{
    protected $filesystem;

    protected $mountManager;

    public function init(): void
    {
        $this->filesystem = m::mock(Factory::class);
        $this->mountManager = m::mock(MountManager::class);
    }

    /**
     * @throws FilesystemException
     */
    public function test_copying_from_s3_to_local(): void
    {
        $this->mountManager->shouldReceive('copy')->once()->andReturn(true);

        $remoteFileRetriever = new RemoteFileRetriever($this->filesystem, $this->mountManager);
        $remoteFileRetriever->s3ToLocal('remoteFile', 'localFile');
    }
}
