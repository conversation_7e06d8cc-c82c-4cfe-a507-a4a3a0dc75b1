<?php

namespace Tests\Filesystem\Paths;

use Eloquence\Behaviours\Slug;
use Platform\Filesystem\Paths\ThemeCssPath;
use Tests\TestCase;

final class ThemeCssPathTest extends TestCase
{
    public function testPathCreation(): void
    {
        $themeCssPath = ThemeCssPath::fromSlug(new Slug('abcdefghk123873'));

        $this->assertMatchesRegularExpression('#^/themes/abcdefghk123873/theme-[a-z0-9]+\.css$#i', $themeCssPath->path());
    }
}
