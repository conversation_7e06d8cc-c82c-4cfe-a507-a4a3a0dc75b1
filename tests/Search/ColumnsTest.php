<?php

namespace Tests\Search;

use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Columns;
use Platform\Search\Columns\HasEagerLoadedRelations;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeAggregateFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\IncludeFilterCollection;
use Platform\Search\Filters\RelationshipFilter;
use Tests\TestCase;
use Mockery as m;

final class ColumnsTest extends TestCase
{
    public function test_it_returns_columns_based_on_settings(): void
    {
        $settings = [
            'name',
            'email',
        ];

        $columns = new Columns([new Name, new Email, new Phone]);
        $columns = $columns->fromSettings($settings);

        $this->assertCount(2, $columns);

        $this->assertNotEmpty($columns->first(function (Column $column) {
            return $column->name() == 'name';
        }));

        $this->assertNotEmpty($columns->first(function (Column $column) {
            return $column->name() == 'email';
        }));

        $this->assertEmpty($columns->first(function (Column $column) {
            return $column->name() == 'phone';
        }));
    }
    
    public function test_it_returns_instances_of_relationship_filters_with_unique_relations(): void
    {
        $columns = new Columns([new Name, new Email, new Phone]);
        $query = m::mock('query');
        $query->shouldReceive('with')->with(m::type('array'))->times(3)->andReturnSelf();
        $query->shouldReceive('withCount')->with('contacts')->once()->andReturnSelf();
        $query->shouldReceive('withSum')->with('user.posts')->once()->andReturnSelf();
        $query->shouldReceive('withAvg')->with('user.profile.views')->once()->andReturnSelf();
        
        $this->assertContainsOnlyInstancesOf(RelationshipFilter::class, $columns->resolveRelations());
        
        foreach ($columns->resolveRelations() as $includeFilter) {
            $this->assertTrue($includeFilter instanceof IncludeFilter || $includeFilter instanceof IncludeAggregateFilter);
            $includeFilter->applyToEloquent($query);
        }
    }
}

abstract class TestColumn implements Column
{
    public function title(): string
    {
    }

    public function dependencies(): Collection
    {
    }

    public function field()
    {
    }

    public function value($record)
    {
    }

    public function html($record): string
    {
    }

    public function default(): Defaults
    {
    }

    public function visible(): bool
    {
    }

    public function fixed(): bool
    {
    }

    public function priority(): int
    {
    }

    public function sortable(): bool
    {
    }
}

class Name extends TestColumn implements Column, HasEagerLoadedRelations
{
    public function name(): string
    {
        return 'name';
    }

    public function relations(): IncludeFilterCollection
    {
        return (new IncludeFilterCollection)
            ->addIncludeFilter(['user', 'user.profile'])
            ->addIncludeCountFilter('contacts')
            ->addIncludeSumFilter( 'user.posts')
            ->addIncludeAvgFilter('user.profile.views');
    }
}

class Email extends TestColumn implements Column, HasEagerLoadedRelations
{
    public function name(): string
    {
        return 'email';
    }
    
    public function relations(): IncludeFilterCollection
    {
        return new IncludeFilterCollection([
            new IncludeFilter([
                'user',
                'user.profile' => function ($query) {
                    $query->where('active', true);
                }
            ])
        ]);
    }
}

class Phone extends TestColumn implements Column, HasEagerLoadedRelations
{
    public function name(): string
    {
        return 'phone';
    }
    
    public function relations(): IncludeFilterCollection
    {
        return new IncludeFilterCollection([
            new IncludeFilter([
                [
                    'user',
                    'user.contacts',
                    'catalog',
                ]
            ])
        ]);
    }
}
