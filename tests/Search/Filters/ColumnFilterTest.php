<?php

namespace Tests\Search\Filters;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Mockery as m;
use Platform\Search\Column;
use Platform\Search\Columns\HasSelectStrategy;
use Platform\Search\Defaults;
use Platform\Search\FieldColumn as FieldColumnInterface;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\HasValues;
use Platform\Search\Services\ColumnSelectStrategies\SelectStrategy;
use Platform\Search\Services\ColumnsSelectQueryBuilder;
use Tests\TestCase;

final class ColumnFilterTest extends TestCase
{
    public function test_it_selects_the_appropriate_columns(): void
    {
        $query = m::spy('query');

        $filter = new ColumnFilter('name', 'email');
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('addSelect')->with('name')->once();
        $query->shouldHaveReceived('addSelect')->with('email')->once();
    }

    public function test_it_adds_values_and_hashes_for_field_columns(): void
    {
        $query = m::spy('query');

        $filter = new ColumnFilter(new FieldColumn);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('addSelect')->with('test-selector')->once();
    }

    public function test_it_can_be_instantiated_from_an_array_of_column_objects(): void
    {
        $query = m::spy('query');

        $filter = new ColumnFilter(new NameColumn, new FullNameColumn);
        $filter->applyToEloquent($query);

        $this->assertInstanceOf(ColumnFilter::class, $filter);

        $query->shouldHaveReceived('addSelect')->twice();
    }

    public function test_it_can_accept_raw_query_in_with_method(): void
    {
        $query = $this->spy('query');

        $rawQuery = DB::raw('COUNT(*) as total');

        $filter = new ColumnFilter();
        $filter->with('name', $rawQuery)->applyToEloquent($query);

        $query->shouldHaveReceived('addSelect')->with('name')->once();
        $query->shouldHaveReceived('addSelect')->with($rawQuery)->once();
    }
}

class Model
{
    public function getTable()
    {
        return 'table_name';
    }
}

class FieldColumn implements FieldColumnInterface, HasSelectStrategy
{
    public function selectStrategy(): SelectStrategy
    {
        return new class extends SelectStrategy
        {
            public function applyTo(ColumnsSelectQueryBuilder $builder)
            {
                $builder->get()->add('test-selector');
            }
        };
    }
}

class NameColumn implements Column
{
    public function title(): string
    {
        return 'name';
    }

    public function name(): string
    {
        return 'test.name';
    }

    public function dependencies(): Collection
    {
        return collect([]);
    }

    public function field()
    {
        return 'table.name';
    }

    public function value($record)
    {
        return $record->name;
    }

    public function html($record): string
    {
        return e($record->name);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 10;
    }

    public function sortable(): bool
    {
        return true;
    }
}

class FullNameColumn implements Column
{
    public function title(): string
    {
        return 'full name';
    }

    public function name(): string
    {
        return 'test.full_name';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return 'table.full_name';
    }

    public function value($record)
    {
        return $record->name();
    }

    public function html($record): string
    {
        return e($record->name());
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 10;
    }

    public function sortable(): bool
    {
        return true;
    }
}

class Entity implements HasValues
{
    public function getTable(): string
    {
        return 'table_name';
    }
}
