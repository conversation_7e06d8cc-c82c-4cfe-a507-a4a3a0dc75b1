<?php

namespace Tests\Search\Filters;

use Illuminate\Support\Collection;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\TranslatedColumn;
use Tests\BuildsQueries;

final class TranslatedColumnSearchFilterTest extends \Tests\TestCase
{
    use BuildsQueries;

    public function test_it_builds_an_appropriate_query(): void
    {
        $query = $this->fakeBuilder();

        $query->shouldReceive('getQuery')->andReturn($query);
        $query->shouldReceive('where')->once();

        $filter = new TranslatedColumnSearchFilter($this->columns(), 'Categories', 1, ['keywords' => 'abc']);
        $filter->applyToEloquent($query);

        $sql = $this->preparedQuery($query);
        $this->assertStringContainsString('join translations', $sql);
        $this->assertStringContainsString('"resource" = \'Categories\'', $sql);
        $this->assertStringContainsString('"account_id" = \'1\'', $sql);
        $this->assertStringContainsString('"field" = \'name\'', $sql);
    }

    public function test_can_set_custom_join_table(): void
    {
        $query = $this->fakeBuilder();

        $query->shouldReceive('getQuery')->andReturn($query);
        $query->shouldReceive('where')->once();

        $filter = new TranslatedColumnSearchFilter($this->columns(), 'Categories', 1, ['keywords' => 'abc']);
        $filter->setJoinTable('my_table');
        $filter->applyToEloquent($query);

        $sql = $this->preparedQuery($query);
        $this->assertStringContainsString('"foreign_id" = "my_table"."id"', $sql);
    }

    public function test_filters_query_by_language(): void
    {
        $query = $this->fakeBuilder();

        $query->shouldReceive('getQuery')->andReturn($query);
        $query->shouldReceive('where')->once();

        $filter = new TranslatedColumnSearchFilter($this->columns(), 'Categories', 1, ['keywords' => 'abc']);
        $filter->restrictLanguage('en_GB');
        $filter->applyToEloquent($query);

        $sql = $this->preparedQuery($query);
        $this->assertStringContainsString('"language" = \'en_GB\'', $sql);

        // Assertion for not using fallback language
        $this->assertStringNotContainsString('_fallback', $sql);
    }

    public function test_uses_fallback_language(): void
    {
        $query = $this->fakeBuilder();

        $query->shouldReceive('getQuery')->andReturn($query);
        $query->shouldReceive('where')->once();

        $filter = new TranslatedColumnSearchFilter(new Columns([new Name('en_GB', 'el_GR')]), 'Categories', 1, ['keywords' => 'abc']);

        $filter->applyToEloquent($query);

        $sql = $this->preparedQuery($query);
        // Restricts to default language automatically if it is set and fallback is also set
        $this->assertStringContainsString('"language" = \'en_GB\'', $sql);
        $this->assertStringContainsString('"language" = \'el_GR\'', $sql);
        $this->assertStringContainsString('_fallback', $sql);
    }

    public function test_restrict_language_when_default_is_set(): void
    {
        $query = $this->fakeBuilder();

        $query->shouldReceive('getQuery')->andReturn($query);
        $query->shouldReceive('where')->once();

        $filter = new TranslatedColumnSearchFilter($this->columns(), 'Categories', 1, ['keywords' => 'abc']);
        $filter->restrictLanguage('en_GB');
        $filter->applyToEloquent($query);

        $sql = $this->preparedQuery($query);
        $this->assertStringNotContainsString('_fallback', $sql);
    }

    private function columns()
    {
        return new Columns([new Name]);
    }
}

class Name extends TranslatedColumn
{
    private $fallback;

    private $default;

    public function __construct($fallback = null, $default = null)
    {
        $this->fallback = $fallback;
        $this->default = $default;
    }

    public function title()
    {
        return 'Name';
    }

    public function name(): string
    {
        return 'category.name';
    }

    public function dependencies(): Collection
    {
        return collect([
            TranslatedColumnSearchFilter::class,
        ]);
    }

    public function field()
    {
        return null;
    }

    public function fieldName(): string
    {
        return 'name';
    }

    public function value($record)
    {
        return lang($record, 'name');
    }

    public function html($record)
    {
        return $this->value($record);
    }

    public function default(): Defaults
    {
        return new Defaults('search');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 10;
    }

    public function sortable(): bool
    {
        return true;
    }

    public function fallbackLanguage(): ?string
    {
        return $this->fallback;
    }

    public function defaultLanguage(): ?string
    {
        return $this->default;
    }
}
