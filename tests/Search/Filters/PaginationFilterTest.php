<?php

namespace Tests\Search\Filters;

use Platform\Search\Filters\PaginationFilter;
use Tests\TestCase;

final class PaginationFilterTest extends TestCase
{
    public function test_it_sets_page_viewing_parameters_on_the_query(): void
    {
        $query = new \stdClass;

        $filter = new PaginationFilter(['page' => 35, 'per_page' => 107]);
        $filter->applyToEloquent($query);

        $this->assertSame(35, request('page'));
        $this->assertSame(100, request('per_page'));
    }
}
