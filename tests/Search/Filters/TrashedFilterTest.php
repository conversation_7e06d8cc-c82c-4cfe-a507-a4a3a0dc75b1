<?php

namespace Tests\Search\Filters;

use Mockery as m;
use Platform\Search\Filters\TrashedFilter;
use Tests\TestCase;

final class TrashedFilterTest extends TestCase
{
    public function test_it_works_with_no_params_set(): void
    {
        $query = m::spy('query');

        $filter = new TrashedFilter([]);
        $filter->applyToEloquent($query);

        $query->shouldNotHaveReceived('onlyTrashed');
        $query->shouldNotHaveReceived('withTrashed');
    }

    public function test_it_restricts_queries_by_deleted_resources_only(): void
    {
        $query = m::spy('query');

        $filter = new TrashedFilter(['trashed' => 'only']);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('onlyTrashed');
        $query->shouldNotHaveReceived('withTrashed');

        $filter = new TrashedFilter(['deleted' => 'only']);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('onlyTrashed');
        $query->shouldNotHaveReceived('withTrashed');
    }

    public function test_it_returns_all_records_based_on_input(): void
    {
        $query = m::spy('query');

        $filter = new TrashedFilter(['trashed' => 'all']);
        $filter->applyToEloquent($query);

        $query->shouldNotHaveReceived('onlyTrashed');
        $query->shouldHaveReceived('withTrashed');

        $filter = new TrashedFilter(['deleted' => 'included']);
        $filter->applyToEloquent($query);

        $query->shouldNotHaveReceived('onlyTrashed');
        $query->shouldHaveReceived('withTrashed');
    }
}
