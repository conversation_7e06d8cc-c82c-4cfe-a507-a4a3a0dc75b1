<?php

namespace Tests\Search\Filters;

use Mockery as m;
use Platform\Search\Filters\ArchivedFilter;
use Tests\TestCase;

final class ArchivedFilterTest extends TestCase
{
    public function test_it_returns_only_archived_records_when_archived_is_only(): void
    {
        $query = m::spy('query');

        $filter = new ArchivedFilter(['archived' => 'only']);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('archived')->once();
        $query->shouldNotHaveReceived('current');
    }

    public function test_it_return_current_records_when_archived_is_none(): void
    {
        $query = m::spy('query');

        $filter = new ArchivedFilter(['archived' => 'none']);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('current')->once();
        $query->shouldNotHaveReceived('archived');
    }

    public function test_it_returns_all_records_when_archived_is_all(): void
    {
        $query = m::spy('query');

        $filter = new ArchivedFilter(['archived' => 'all']);
        $filter->applyToEloquent($query);

        $query->shouldNotHaveReceived('archived');
        $query->shouldNotHaveReceived('current');
    }

    public function test_it_returns_all_records_when_trashed_is_only(): void
    {
        $query = m::spy('query');

        $filter = new ArchivedFilter(['archived' => 'none', 'trashed' => 'only']);
        $filter->applyToEloquent($query);

        $query->shouldNotHaveReceived('archived');
        $query->shouldNotHaveReceived('current');
    }
}
