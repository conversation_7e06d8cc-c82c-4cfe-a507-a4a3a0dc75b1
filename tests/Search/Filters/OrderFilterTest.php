<?php

namespace Tests\Search\Filters;

use Mockery as m;
use Platform\Search\Columns;
use Platform\Search\Filters\OrderFilter;
use Tests\Stubs\OrderColumnStub;
use Tests\Stubs\TranslatedColumnStub;
use Tests\TestCase;

final class OrderFilterTest extends TestCase
{
    private $query;

    public function init()
    {
        $this->query = m::mock('query');
    }

    public function test_it_orders_by_field_and_direction(): void
    {
        $this->query->shouldReceive('orderByRaw')->with('field ASC')->once();

        $filter = OrderFilter::byFieldAndDirection('field', 'asc');
        $filter->setFieldMap(['field' => 'field']);
        $filter->applyToEloquent($this->query);
    }

    public function test_ordering_when_only_field_is_provided(): void
    {
        $this->query->shouldReceive('orderByRaw')->with('something DESC')->once();

        $filter = OrderFilter::byInput(['order' => 'something']);
        $filter->setFieldMap(['something' => 'something']);
        $filter->applyToEloquent($this->query);
    }

    public function test_it_orders_when_an_invalid_sort_direction_is_provided(): void
    {
        $this->expectException(\Exception::class);

        $filter = OrderFilter::byFieldAndDirection('field', 'invalid direction');
        $filter->setFieldMap(['field' => 'field']);
        $filter->applyToEloquent($this->query);
    }

    public function test_it_sets_up_a_column_to_field_map_correctly(): void
    {
        $columns = new Columns([new OrderColumnStub]);

        $query = m::spy('query');

        $filter = OrderFilter::fromColumns($columns, ['order' => 'order column']);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('orderByRaw')->once()->with('ordered.column DESC');
    }

    public function test_does_not_apply_any_ordering_if_not_available(): void
    {
        $query = m::spy('query');

        $filter = OrderFilter::fromColumns(new Columns, [], 'order column');
        $filter->applyToEloquent($query);

        $query->shouldNotHaveReceived('orderByRaw');
        $query->shouldNotHaveReceived('orderBy');
    }

    public function test_default_is_used_if_ordering_column_not_available(): void
    {
        $query = m::spy('query');

        $filter = OrderFilter::fromColumns(new Columns, ['order' => 'order column']);
        $filter->setDefaultField('order.id');
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('orderByRaw')->once()->with('order.id DESC');
    }

    public function test_ordering_when_order_value_does_not_include_table_name(): void
    {
        $columns = new Columns([new OrderColumnStub]);

        $query = m::spy('query');

        $filter = OrderFilter::fromColumns($columns, ['order' => 'column'], null);
        $filter->setFieldMap(['order column' => 'ordered.column']);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('orderByRaw')->once()->with('ordered.column DESC');
    }

    public function test_it_uses_unique_ordering_on_translated_columns(): void
    {
        $columns = new Columns([$column = new TranslatedColumnStub]);

        $query = m::spy('query')->makePartial();

        $filter = OrderFilter::fromColumns($columns, ['order' => $column->name()], null)->uniqueColumn('stub.id');

        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('orderByRaw')->with($column->alias().'_name DESC')->once();
        $query->shouldHaveReceived('orderBy')->with('stub.id')->once();
    }

    public function test_it_uses_unique_column_to_order(): void
    {
        $query = m::spy('query');

        $filter = OrderFilter::fromColumns(new Columns)->uniqueColumn($column = 'test');
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('orderBy')->once()->with($column);
    }

    public function test_it_does_not_order_by_default_if_order_field_is_present(): void
    {
        $columns = new Columns([new OrderColumnStub]);

        $query = m::spy('query');

        $filter = OrderFilter::fromColumns($columns, ['order' => 'column'], 'id');
        $filter->setFieldMap(['order column' => 'ordered.column']);
        $filter->applyToEloquent($query);

        $query->shouldHaveReceived('orderByRaw')->once()->with('ordered.column DESC');
        $query->shouldNotHaveReceived('orderBy');
    }
}
