<?php
namespace Tests\Search\Filters;


use Assert\InvalidArgumentException;
use Platform\Search\Filters\IncludeFilterCollection;
use Tests\TestCase;

class IncludeFilterCollectionTest extends TestCase
{
    public function testItValidatesRelationshipFilters()
    {
        $this->expectException(InvalidArgumentException::class);
        new IncludeFilterCollection(['notARelationshipFilter']);
    }
}
