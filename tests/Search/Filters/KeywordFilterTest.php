<?php

namespace Tests\Search\Filters;

use Mockery as m;
use Platform\Search\Filters\KeywordFilter;
use Tests\BuildsQueries;

final class KeywordFilterTest extends \Tests\TestCase
{
    use BuildsQueries;

    public function test_it_throws_an_error_when_invalid_keywords_are_provided(): void
    {
        $mockQuery = m::spy('query');

        $filter = KeywordFilter::fromKeywords('');
        $filter->applyToEloquent($mockQuery);

        $mockQuery->shouldNotHaveReceived('where');
    }

    public function test_using_custom_field_name(): void
    {
        $mockQuery = m::spy('query');

        $filter = KeywordFilter::fromKeywords('keywords', 'title');
        $filter->applyToEloquent($mockQuery);

        $mockQuery->shouldHaveReceived('where')->once();
    }

    public function testUsingAnArrayOfFieldNames(): void
    {
        $mockQuery = m::mock('query');
        $mockQuery->shouldReceive('where')->once()->andReturn($mockQuery);

        $filter = KeywordFilter::fromKeywords('keywords', ['title', 'name']);

        $filter->applyToEloquent($mockQuery);
    }

    public function testSearchKeywordWithSpace(): void
    {
        $mockQuery = m::spy('query');
        $mockQuery->shouldReceive('where')->times(3)->andReturn($mockQuery);

        $filter = KeywordFilter::fromSplitKeywords('title and me', ['title', 'first_name']);
        $filter->applyToEloquent($mockQuery);

        $mockQuery->shouldHaveReceived('where')->times(3);
    }

    public function test_it_can_handle_keywords_from_input(): void
    {
        $input = ['keywords' => 'that guy'];
        $filter = KeywordFilter::fromInput($input);

        $query = $filter->applyToEloquent($this->builder());

        $this->assertCount(1, $query->wheres);
    }
}
