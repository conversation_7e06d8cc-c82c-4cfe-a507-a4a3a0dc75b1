<?php
namespace Tests\Search\Filters;


use Platform\Search\Filters\IncludeAggregateFilter;
use Tests\TestCase;
use Mockery as m;

class IncludeAggregateFilterTest extends TestCase
{
    public function init()
    {
        $this->query = m::mock('query');
    }
    
    public function testItResolvesWithCount(): void
    {
        $filter = new IncludeAggregateFilter('count', 'test_relation');
        $this->query->shouldReceive('withCount')->with('test_relation')->once();
        $filter->applyToEloquent($this->query);
    }
    
    public function testItResolvesWithAvg(): void
    {
        $filter = new IncludeAggregateFilter('avg', 'test_relation');
        $this->query->shouldReceive('withAvg')->with('test_relation')->once();
        $filter->applyToEloquent($this->query);
    }
    
    public function testItResolvesSum(): void
    {
        $filter = new IncludeAggregateFilter('sum', 'test_relation');
        $this->query->shouldReceive('withSum')->with('test_relation')->once();
        $filter->applyToEloquent($this->query);
    }

    public function testItResolvesMin(): void
    {
        $filter = new IncludeAggregateFilter('min', 'test_relation');
        $this->query->shouldReceive('withMin')->with('test_relation')->once();
        $filter->applyToEloquent($this->query);
    }

    public function testItResolvesMax(): void
    {
        $filter = new IncludeAggregateFilter('max', 'test_relation');
        $this->query->shouldReceive('withMax')->with('test_relation')->once();
        $filter->applyToEloquent($this->query);
    }
}
