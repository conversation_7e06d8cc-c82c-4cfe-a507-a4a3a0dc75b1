<?php

namespace Search\Filters;

use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Platform\Database\Eloquent\Builder;
use Platform\Search\Filters\Dates\DateFilter;
use Platform\Search\Filters\Dates\DateFilterType;
use Tests\TestCase;

class DateFilterTest extends TestCase
{
    private $query;

    public function init()
    {
        $this->query = m::spy(Builder::class);
        $this->query->shouldReceive('getModel')->andReturn(new class()
        {
            public function getTable()
            {
                return 'table_name';
            }
        });
    }

    #[TestWith([DateFilterType::On])]
    #[TestWith([DateFilterType::After])]
    #[TestWith([DateFilterType::Before])]
    public function test_should_filter(DateFilterType $dateFilter)
    {
        $filter = new DateFilter(['fake_at' => [$dateFilter->value => '2024-01-08T16:47:46Z']], 'fake_at', 'fake_at');
        $filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('where')->with('table_name.fake_at', $dateFilter->operator(), '2024-01-08 16:47:46');
    }

    public function test_should_filter_between()
    {
        $filter = new DateFilter(['fake_at' => [DateFilterType::Between->value => '[2024-01-08T16:47:46Z,2024-01-09 16:47:46Z]']], 'fake_at', 'fake_at');
        $filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('where')->with('table_name.fake_at', DateFilterType::After->operator(), '2024-01-08 16:47:46');
        $this->query->shouldHaveReceived('where')->with('table_name.fake_at', DateFilterType::Before->operator(), '2024-01-09 16:47:46');
    }

    public function test_validate_filter_name_should_fail_when_filter_is_not_valid()
    {
        $filter = new DateFilter(['fake_at' => [DateFilterType::Between->value => '[2024-01-08T16:47:46Z,2024-01-08 16:47:46Z]']], 'fake_at', 'fake_at');

        $this->assertTrue($filter->validateFilterName('fake_at'));
        $this->assertFalse($filter->validateFilterName('invalid_filter'));
    }

    public function test_validate_should_fail_when_option_is_not_valid()
    {
        $this->expectExceptionMessage('Value must be one of the following: ['.implode(', ', collect(DateFilterType::cases())->pluck('value')->toArray()).']. You provided [invalid]');
        $filter = new DateFilter(['fake_at' => ['invalid' => '[2024-01-08T16:47:46Z,2024-01-08 16:47:46Z]']], 'fake_at', 'fake_at');

        $filter->validateFilterValue('fake_at', '');
    }

    public function test_validate_should_pass_when_option_is_valid()
    {
        $this->expectNotToPerformAssertions();
        $filter = new DateFilter(['fake_at' => [DateFilterType::On->value => '2024-01-08T16:47:46Z']], 'fake_at', 'fake_at');
        $filter->validateFilterValue('fake_at', '');
    }

    public function test_validate_should_fail_when_date_is_wrong()
    {
        $this->expectExceptionMessage('Value is not a valid date. Must be in the format Y-m-d\TH:i:s\Z. You provided [2024-01-0]');
        $filter = new DateFilter(['fake_at' => [DateFilterType::On->value => '2024-01-0']], 'fake_at', 'fake_at');
        $filter->validateFilterValue('fake_at', '');
    }
}
