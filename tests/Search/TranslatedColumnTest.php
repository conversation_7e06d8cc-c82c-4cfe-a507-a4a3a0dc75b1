<?php

namespace Tests\Search;

use Illuminate\Support\Facades\DB;
use Platform\Search\Defaults;
use Platform\Search\TranslatedColumn;
use Platform\Search\TranslatedColumnExpression;
use Tests\TestCase;

final class TranslatedColumnTest extends TestCase
{
    public function testTranslatedValue(): void
    {
        $column = $this->getTranslatedColumn();
        $dummyRecord = $this->getRecord(
            [$column->columnName() => $value = 'randomValue']
        );

        $this->assertEquals($value, $column->value($dummyRecord));
    }

    public function testTranslatedField(): void
    {
        $column = $this->getTranslatedColumn();

        $this->assertInstanceOf(TranslatedColumnExpression::class, $translatedField = $column->translatedField());
        $this->assertStringContainsString($column->alias(), $translatedField->getValue(DB::connection()->getQueryGrammar()));
        $this->assertStringContainsString($column->alias().'_fallback', $translatedField->getValue(DB::connection()->getQueryGrammar()));
    }

    private function getRecord(array $properties = [])
    {
        return new class($properties) extends \stdClass
        {
            public function __construct($properties)
            {
                foreach ($properties as $property => $value) {
                    $this->$property = $value;
                }
            }
        };
    }

    private function getTranslatedColumn(): TranslatedColumn
    {
        return new class extends TranslatedColumn
        {
            public function fallbackLanguage(): string
            {
                return 'es_LA';
            }

            public function title()
            {
                return 'some.title';
            }

            public function name(): string
            {
                return 'columnName';
            }

            public function html($record)
            {
                return 'html';
            }

            public function default(): Defaults
            {
                return new Defaults('all');
            }

            public function priority(): int
            {
                return 1;
            }

            public function fieldName(): string
            {
                return 'fieldName';
            }
        };
    }
}
