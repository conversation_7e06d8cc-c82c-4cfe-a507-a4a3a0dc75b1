<?php

namespace Tests\Search;

use Illuminate\Database\Eloquent\Builder;
use Mockery as m;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Enhancers\SearchEnhancer;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Filters\UnionFilter;
use Platform\Search\MultiColumnator;
use Tests\TestCase;

final class MultiColumnatorTest extends TestCase
{
    public function test_generates_union_query_from_columnators(): void
    {
        $multicolumnator = new MultiColumnator;
        $multicolumnator->add(new ColumnatorA([], []));
        $multicolumnator->add(new ColumnatorB([], []));

        $filters = $multicolumnator->filters(new Defaults('all'));

        $this->assertCount(3, $filters);
        $this->assertInstanceOf(FilterA::class, $filters->get(0));
        $this->assertInstanceOf(UnionFilter::class, $filters->get(1));
        $this->assertInstanceOf(OrderFilter::class, $filters->get(2));
    }

    public function test_returns_all_enhancers_including_duplicates(): void
    {
        $multicolumnator = new MultiColumnator;
        $multicolumnator->add(new ColumnatorA([], []));
        $multicolumnator->add(new ColumnatorB([], []));

        $enhnacers = $multicolumnator->enhancers(new Defaults('all'));

        $this->assertCount(3, $enhnacers);
        $this->assertCount(2, $enhnacers->filter(function ($enhancer) {
            return $enhancer instanceof EnhancerA;
        }));
        $this->assertCount(1, $enhnacers->filter(function ($enhancer) {
            return $enhancer instanceof EnhancerB;
        }));
    }
}

class ColumnatorA extends Columnator
{
    public function availableDependencies(Defaults $view): Dependencies
    {
        return new Dependencies([
            new FilterA,
            new EnhancerA,
        ]);
    }

    protected function baseColumns()
    {
        return new Columns;
    }

    public function resource()
    {
    }

    public static function key(): string
    {
    }

    public static function exportKey(): string
    {
    }

    public function repository(): Repository
    {
        return m::mock(Repository::class);
    }
}

class ColumnatorB extends Columnator
{
    public function availableDependencies(Defaults $view): Dependencies
    {
        return new Dependencies([
            new FilterB,
            new FilterC,
            new EnhancerA,
            new EnhancerB,
            OrderFilter::fromColumns($this->columns($view)),
        ]);
    }

    protected function baseColumns()
    {
        return new Columns;
    }

    public function resource()
    {
    }

    public static function key(): string
    {
    }

    public static function exportKey(): string
    {
    }

    public function repository(): Repository
    {
        $repository = m::mock(Repository::class);
        $repository->shouldReceive('applyFilters')->andReturn(m::mock(Builder::class));

        return $repository;
    }
}

class FilterA implements ColumnatorFilter, SearchFilter
{
    public function applies(): bool
    {
        return true;
    }

    public function applyToEloquent($query)
    {
        return $query;
    }
}

class FilterB implements ColumnatorFilter, SearchFilter
{
    public function applies(): bool
    {
        return true;
    }

    public function applyToEloquent($query)
    {
        return $query;
    }
}

class FilterC implements ColumnatorFilter, SearchFilter
{
    public function applies(): bool
    {
        return true;
    }

    public function applyToEloquent($query)
    {
        return $query;
    }
}

class EnhancerA implements SearchEnhancer
{
    public function applies(): bool
    {
        return true;
    }

    public function enhance($results): void
    {
    }
}

class EnhancerB implements SearchEnhancer
{
    public function applies(): bool
    {
        return true;
    }

    public function enhance($results): void
    {
    }
}
