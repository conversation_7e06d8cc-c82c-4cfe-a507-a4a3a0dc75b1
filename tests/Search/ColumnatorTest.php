<?php

namespace Tests\Search;

use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumn;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\ApiVisibility;
use Platform\Search\Column;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\SavedSearch;
use Platform\Search\Services\Factory;
use Platform\Search\View\Columnator as ColumnatorView;
use Tests\TestCase;

final class ColumnatorTest extends TestCase
{
    public function test_it_returns_default_columns(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestColumnatorColumn('all', 'one'),
                    new TestColumnatorColumn('none', 'two'),
                    new TestColumnatorColumn('none', 'three'),
                    new TestColumnatorColumn('all', 'four'),
                ]);
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $columns = $columnator->defaultColumns(new Defaults('all'));

        $this->assertCount(2, $columns);

        $this->assertEmpty($columns->first(function (Column $column) {
            return $column->name() == 'two';
        }));
        $this->assertEmpty($columns->first(function (Column $column) {
            return $column->name() == 'three';
        }));
        $this->assertNotEmpty($columns->first(function (Column $column) {
            return $column->name() == 'one';
        }));
        $this->assertNotEmpty($columns->first(function (Column $column) {
            return $column->name() == 'four';
        }));
    }

    public function test_it_returns_an_intersection_of_saved_search_configuration(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public $savedSearch;

            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestColumnatorColumn('all', 'one'),
                    new TestColumnatorColumn('none', 'two'),
                    new TestColumnatorColumn('none', 'three'),
                    new TestColumnatorColumn('all', 'four'),
                ]);
            }

            public function setSavedSearch(SavedSearch $search)
            {
                $this->savedSearch = $search;
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $columnator->setSavedSearch(new ColumnatorSearch([
            'columns' => ['one', 'three'],
        ]));

        // We're only asserting length here. A more in-depth test of how columns intersect filters and
        // saved search settings can be found in the ColumnsTest class.
        $this->assertCount(2, $columnator->columns(new Defaults('all')));
    }

    public function test_it_returns_only_unique_columns(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestColumnatorColumn('all', 'one'),
                    new TestColumnatorColumn('all', 'two'),
                    new TestColumnatorColumn('all', 'one'),
                ]);
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $this->assertCount(2, $columnator->columns(new Defaults('export')));
    }

    public function test_it_returns_all_api_columns(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestApiColumnAll('all', 'test-api-column-all'),
                    new TestApiColumnList('all', 'test-api-column-list'),
                    new TestApiColumnView('all', 'test-api-column-view'),
                ]);
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $columns = $columnator->apiColumns(new ApiVisibility('all'));

        $this->assertCount(3, $columns);
    }

    public function test_it_returns_list_api_columns(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestApiColumnAll('all', 'test-api-column-all'),
                    new TestApiColumnList('all', 'test-api-column-list'),
                    new TestApiColumnView('all', 'test-api-column-view'),
                ]);
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $columns = $columnator->apiColumns(new ApiVisibility('list'));

        $this->assertCount(2, $columns);

        $this->assertEmpty($columns->first(function (Column $column) {
            return $column->name() == 'test-api-column-view';
        }));
    }

    public function test_it_returns_view_api_columns(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestApiColumnAll('all', 'test-api-column-all'),
                    new TestApiColumnList('all', 'test-api-column-list'),
                    new TestApiColumnView('all', 'test-api-column-view'),
                ]);
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $columns = $columnator->apiColumns(new ApiVisibility('view'));

        $this->assertCount(2, $columns);

        $this->assertEmpty($columns->first(function (Column $column) {
            return $column->name() == 'test-api-column-list';
        }));
    }

    public function test_api_columns_trait(): void
    {
        $columnator = new class([], []) extends Columnator implements ApiColumnator
        {
            use ApiColumns;

            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestColumnatorColumn('all', 'one'),
                    new TestColumnatorColumn('all', 'two'),
                    new TestApiColumnList('all', 'test-api-column-list'),
                    new TestApiColumnView('all', 'test-api-column-view'),
                ]);
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $columnator->setApiVisibility(new ApiVisibility('list'));

        $columns = $columnator->columns(new Defaults('all'));

        $this->assertCount(1, $columns);
        $this->assertInstanceOf(ApiColumn::class, $columns->first());
    }

    public function test_columns_method_does_not_return_not_visible_columns(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function baseColumns(): Columns
            {
                return new Columns([
                    new TestColumnatorColumn('all', 'one'),
                    new TestColumnatorColumn('none', 'two'),
                    new TestColumnatorColumn('none', 'three'),
                    new TestColumnatorColumn('all', 'four'),
                ]);
            }

            public function availableDependencies(Defaults $view): Dependencies {}

            public function resource() {}

            protected function fieldColumns() {}

            public static function key(): string {}

            public static function exportKey(): string {}

            public function repository(): Repository {}
        };

        $columns = $columnator->columns(new Defaults('all'));

        $this->assertCount(2, $columns);

        $this->assertEmpty($columns->first(function (Column $column) {
            return $column->name() == 'two';
        }));
        $this->assertEmpty($columns->first(function (Column $column) {
            return $column->name() == 'three';
        }));
        $this->assertNotEmpty($columns->first(function (Column $column) {
            return $column->name() == 'one';
        }));
        $this->assertNotEmpty($columns->first(function (Column $column) {
            return $column->name() == 'four';
        }));
    }

    public function test_it_passes_query_parameters_to_the_columnator_factory(): void
    {
        $request = $this->mock(Request::class);
        $factory = $this->mock(Factory::class);
        $request->shouldReceive('get')->with('area')->once()->andReturn('judging.search');
        $request->shouldReceive('query')->with('filters', [])->once()->andReturn(['score-set' => 1]);
        $factory->shouldReceive('forArea')->with('judging.search', ['score-set' => 1])->andReturn($this->mock(Columnator::class));

        $view = new ColumnatorView($request, $factory);

        $view->columnator();
    }
}

class TestColumnatorColumn implements Column
{
    private $default;

    private $name;

    public function __construct(string $default, string $name)
    {
        $this->default = $default;
        $this->name = $name;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function title(): string {}

    public function dependencies(): Collection {}

    public function field() {}

    public function value($record) {}

    public function html($record): string {}

    public function default(): Defaults
    {
        return new Defaults($this->default);
    }

    public function visible(): bool
    {
        return $this->default === 'all';
    }

    public function fixed(): bool {}

    public function priority(): int {}

    public function sortable(): bool {}
}

class TestApiColumnAll implements ApiColumn, Column
{
    private $default;

    private $name;

    public function __construct(string $default, string $name)
    {
        $this->default = $default;
        $this->name = $name;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function title(): string {}

    public function dependencies(): Collection {}

    public function field() {}

    public function value($record) {}

    public function html($record): string {}

    public function default(): Defaults
    {
        return new Defaults($this->default);
    }

    public function visible(): bool {}

    public function fixed(): bool {}

    public function priority(): int {}

    public function sortable(): bool {}

    public function apiName()
    {
        return 'test-api-column';
    }

    public function apiValue($record)
    {
        return 'test-api-value';
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}

class TestApiColumnList implements ApiColumn, Column
{
    private $default;

    private $name;

    public function __construct(string $default, string $name)
    {
        $this->default = $default;
        $this->name = $name;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function title(): string {}

    public function dependencies(): Collection {}

    public function field() {}

    public function value($record) {}

    public function html($record): string {}

    public function default(): Defaults
    {
        return new Defaults($this->default);
    }

    public function visible(): bool {}

    public function fixed(): bool {}

    public function priority(): int {}

    public function sortable(): bool {}

    public function apiName()
    {
        return 'test-api-column';
    }

    public function apiValue($record)
    {
        return 'test-api-value';
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('list');
    }
}

class TestApiColumnView implements ApiColumn, Column
{
    private $default;

    private $name;

    public function __construct(string $default, string $name)
    {
        $this->default = $default;
        $this->name = $name;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function title(): string {}

    public function dependencies(): Collection {}

    public function field() {}

    public function value($record) {}

    public function html($record): string {}

    public function default(): Defaults
    {
        return new Defaults($this->default);
    }

    public function visible(): bool {}

    public function fixed(): bool {}

    public function priority(): int {}

    public function sortable(): bool {}

    public function apiName()
    {
        return 'test-api-column';
    }

    public function apiValue($record)
    {
        return 'test-api-value';
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('view');
    }
}

class ColumnatorSearch implements SavedSearch
{
    private $configuration;

    public function __construct(array $configuration)
    {
        $this->configuration = $configuration;
    }

    public function configuration(?string $key = null): array
    {
        if ($key) {
            return $this->configuration[$key];
        }

        return $this->configuration;
    }
}
