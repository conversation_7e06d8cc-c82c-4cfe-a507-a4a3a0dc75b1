<?php

namespace Tests\Search;

use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Column;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Enhancers\SearchEnhancer;
use Platform\Search\Filters\ColumnatorFilter;
use Tests\TestCase;

final class ColumnatorDependenciesTest extends TestCase
{
    public function test_it_only_returns_default_filters(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function availableDependencies(Defaults $view): Dependencies
            {
                return new Dependencies([
                    new DefaultFilter,
                    new NotDefaultFilter,
                ]);
            }

            public function baseColumns(): Columns
            {
                return new Columns;
            }

            public function resource()
            {
            }

            protected function fieldColumns()
            {
            }

            public static function key(): string
            {
            }

            public static function exportKey(): string
            {
            }

            public function repository(): Repository
            {
            }
        };

        $filters = $columnator->filters($view = new Defaults('all'));

        $this->assertCount(1, $filters);
        $this->assertInstanceOf(DefaultFilter::class, $filters->first());
        $this->assertEmpty($columnator->enhancers($view));
    }

    public function test_it_includes_column_filter_dependencies(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function availableDependencies(Defaults $view): Dependencies
            {
                return new Dependencies([
                    new NotDefaultFilter,
                ]);
            }

            public function baseColumns(): Columns
            {
                return new Columns([
                    new FiltersTestColumn,
                ]);
            }

            public function resource()
            {
            }

            protected function fieldColumns()
            {
            }

            public static function key(): string
            {
            }

            public static function exportKey(): string
            {
            }

            public function repository(): Repository
            {
            }
        };

        $filters = $columnator->filters($view = new Defaults('all'));

        $this->assertCount(1, $filters);
        $this->assertInstanceOf(NotDefaultFilter::class, $filters->first());
        $this->assertEmpty($columnator->enhancers($view));
    }

    public function test_it_only_returns_default_enhancers(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function availableDependencies(Defaults $view): Dependencies
            {
                return new Dependencies([
                    new DefaultEnhancer,
                    new NotDefaultEnhancer,
                ]);
            }

            public function baseColumns(): Columns
            {
                return new Columns;
            }

            public function resource()
            {
            }

            protected function fieldColumns()
            {
            }

            public static function key(): string
            {
            }

            public static function exportKey(): string
            {
            }

            public function repository(): Repository
            {
            }
        };

        $enhancers = $columnator->enhancers($view = new Defaults('all'));

        $this->assertCount(1, $enhancers);
        $this->assertInstanceOf(DefaultEnhancer::class, $enhancers->first());
        $this->assertEmpty($columnator->filters($view));
    }

    public function test_it_includes_column_enhancer_dependencies(): void
    {
        $columnator = new class([], []) extends Columnator
        {
            public function availableDependencies(Defaults $view): Dependencies
            {
                return new Dependencies([
                    new NotDefaultEnhancer,
                ]);
            }

            public function baseColumns(): Columns
            {
                return new Columns([
                    new FiltersTestColumn,
                ]);
            }

            public function resource()
            {
            }

            protected function fieldColumns()
            {
            }

            public static function key(): string
            {
            }

            public static function exportKey(): string
            {
            }

            public function repository(): Repository
            {
            }
        };

        $enhancers = $columnator->enhancers($view = new Defaults('all'));

        $this->assertCount(1, $enhancers);
        $this->assertInstanceOf(NotDefaultEnhancer::class, $enhancers->first());
        $this->assertEmpty($columnator->filters($view));
    }
}

class FiltersTestColumn implements Column
{
    public function dependencies(): Collection
    {
        return collect([
            NotDefaultFilter::class,
            NotDefaultEnhancer::class,
        ]);
    }

    public function title()
    {
    }

    public function name(): string
    {
        return 'my_column';
    }

    public function field()
    {
    }

    public function value($record)
    {
    }

    public function html($record)
    {
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
    }

    public function priority(): int
    {
    }

    public function sortable(): bool
    {
    }
}

class DefaultFilter implements ColumnatorFilter
{
    public function applies(): bool
    {
        return true;
    }
}

class NotDefaultFilter implements ColumnatorFilter
{
    public function applies(): bool
    {
        return false;
    }
}

class DefaultEnhancer implements SearchEnhancer
{
    public function enhance($results): void
    {
    }

    public function applies(): bool
    {
        return true;
    }
}

class NotDefaultEnhancer implements SearchEnhancer
{
    public function enhance($results): void
    {
    }

    public function applies(): bool
    {
        return false;
    }
}
