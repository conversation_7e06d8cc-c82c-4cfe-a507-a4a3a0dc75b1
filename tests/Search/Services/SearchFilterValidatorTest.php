<?php

namespace Tests\Search\Filters;

use Mockery as m;
use Platform\Search\ApiVisibility;
use Platform\Search\Columnator;
use Platform\Search\Filters\Dates\DateFilterType;
use Platform\Search\Filters\SearchFilterValidation;
use Platform\Search\SearchFilterCollection;
use Platform\Search\Services\SearchFilterValidator;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

final class SearchFilterValidatorTest extends TestCase
{
    use SearchFilterValidator;

    protected $columnator;

    public function init()
    {
        $this->columnator = m::mock(Columnator::class);
    }

    public function testValidateFiltersAllowed(): void
    {
        $this->expectException(HttpException::class);

        $this->validateFiltersAllowed(['name' => 'value'], new ApiVisibility('view'));
    }

    public function testValidateSlugFilterNotAllowed(): void
    {
        $parameters = ['slug' => 'abcdABCD'];
        $this->expectException(HttpException::class);
        $this->validateFiltersAllowed($parameters, new ApiVisibility('view'));
    }

    public function testInvalidFilterName(): void
    {
        $filters = [$filterName = 'invalidFilter' => 'value'];
        $searchFilters = new SearchFilterCollection([new FakeFilter(false, true, true)]);
        $this->columnator->shouldReceive('filters')->andReturn($searchFilters);

        $return = $this->validateFilterNamesAndValues($this->columnator, $filters, new ApiVisibility('view'));
        $this->assertSame($filterName, $return->getData()->errors->invalid_filter_names);
    }

    public function testInvalidFilterValue(): void
    {
        $filters = [$filterName = 'validFilter' => 'INVALID'];
        $searchFilters = new SearchFilterCollection([new FakeFilter(true, false, true)]);
        $this->columnator->shouldReceive('filters')->andReturn($searchFilters);

        $return = $this->validateFilterNamesAndValues($this->columnator, $filters, new ApiVisibility('view'));
        $this->assertEquals('INVALID', $return->getData()->errors->invalid_filter_values->$filterName);
    }

    public function testValidateValueInArray(): void
    {
        $this->expectException(HttpException::class);
        $this->validateValueInArray('status', 'activee', ['active', 'inactive']);
    }

    public function testValidateValueIsNumeric(): void
    {
        $this->expectException(HttpException::class);
        $this->validateValueIsNumeric('page', 'string');
    }

    public function testValidateValueIsBetween(): void
    {
        $this->expectException(HttpException::class);
        $this->validateValueIsBetween('per_page', 101, 1, 100);
    }

    public function testValidateValueIsPositiveNumber(): void
    {
        $this->expectException(HttpException::class);
        $this->validateValueIsPositiveNumber('page', 0);
    }

    public function testValidateValueIsSlug(): void
    {
        $this->expectException(HttpException::class);
        $this->validateValueIsSlug('slug', 'jkloiuj');
    }

    public function testInvalidFilterValueArray(): void
    {
        $filters = [$filterName = 'validFilter' => ['INVALID', 'INVALID']];
        $searchFilters = new SearchFilterCollection([new FakeFilter(true, false, true)]);
        $this->columnator->shouldReceive('filters')->andReturn($searchFilters);

        $return = $this->validateFilterNamesAndValues($this->columnator, $filters, new ApiVisibility('view'));

        $this->assertEquals('INVALID', $return->getData()->errors->invalid_filter_values->$filterName);
    }

    public function testValidateValueIsZuluDateWrongFormat(): void
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('Value is not a valid date. Must be in the format Y-m-d\TH:i:s\Z. You provided [2019-01-01].');

        $this->validateValueDatesAreZuluDate('created_at', '2019-01-01', DateFilterType::On);
    }

    public function testValidateValueIsZuluDateMissingDates(): void
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('Value must be filled with two dates. You provided: [2024-01-08T16:47:46Z]');

        $this->validateValueDatesAreZuluDate('created_at', '[2024-01-08T16:47:46Z]', DateFilterType::Between);
    }
}

class FakeFilter implements SearchFilterValidation
{
    public $name;

    public $value;

    public $scope;

    public function __construct($name, $value, $scope)
    {
        $this->name = $name;
        $this->value = $value;
        $this->scope = $scope;
    }

    public function validateFilterValue(string $filterName, string $filterValue)
    {
        if (! $this->value) {
            throw new HttpException(400, $filterValue);
        }
    }

    public function validateFilterName(string $filterName): bool
    {
        return $this->name;
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return $this->scope;
    }
}
