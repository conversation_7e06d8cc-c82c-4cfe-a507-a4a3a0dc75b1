<?php

namespace Tests\Search\Validators;

use Illuminate\Validation\Validator;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Services\Factory;
use Platform\Search\Validators\ValidColumns;
use Tests\TestCase;

final class ValidColumnsTest extends TestCase
{
    protected $columnator;

    protected $factory;

    protected $collection;

    protected $validator;

    public function init()
    {
        $this->columnator = $this->mock(Columnator::class);
        $this->factory = $this->mock(ColumnatorFactory::class);
        $this->collection = $this->mock(Columns::class);
        $this->validator = $this->mock(Validator::class);
    }

    public function testValidationOnLocaleCode(): void
    {
        $validator = new ValidColumns($this->factory);

        $this->columnator->shouldReceive('availableColumns')
            ->andReturn($this->collection)
            ->byDefault();

        $this->collection->shouldReceive('map->toArray')
            ->andReturn(['a', 'b', 'c'])
            ->byDefault();

        $this->factory->shouldReceive('forArea')
            ->with('type', ['score-set' => 1])
            ->times(5)
            ->andReturn($this->columnator)
            ->byDefault();

        $this->validator->shouldReceive('getValue')
            ->with('filters')
            ->times(5)
            ->andReturn(['score-set' => 1]);

        $this->assertFalse($validator->validColumns('columns', '', ['type'], $this->validator));
        $this->assertFalse($validator->validColumns('columns', 'z', ['type'], $this->validator));
        $this->assertFalse($validator->validColumns('columns', 'a,b,c,z', ['type'], $this->validator));
        $this->assertTrue($validator->validColumns('columns', 'a,b,c', ['type'], $this->validator));
        $this->assertTrue($validator->validColumns('columns', 'a', ['type'], $this->validator));
    }
}

class ColumnatorFactory implements Factory
{
    private $columnator;

    public function __construct($columnator)
    {
        $this->columnator = $columnator;
    }

    public function forArea($area, array $input = []): Columnator
    {
        return $this->columnator;
    }
}
