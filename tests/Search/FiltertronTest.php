<?php

namespace Tests\Search;

use Illuminate\Http\Request;
use Mockery as m;
use Platform\Search\Filtertron;
use Tests\TestCase;

final class FiltertronTest extends TestCase
{
    private $mockRequest;

    private $filtertron;

    public function init()
    {
        $this->mockRequest = m::mock(Request::class);
        $this->mockRequest->shouldReceive('query')->once()->andReturn(['title' => 'Entry', 'category' => 'Places to be']);
        $this->filtertron = new Filtertron($this->mockRequest);
    }

    public function test_it_can_remove_parameters(): void
    {
        $this->filtertron->without('title');

        $this->assertArrayNotHasKey('title', $this->filtertron->params());
    }

    public function test_removal_of_parameters(): void
    {
        $this->filtertron
            ->with('chapter', 'Sydney')
            ->with('field', 'anything');

        $this->assertArrayHasKey('chapter', $this->filtertron->params());
        $this->assertArray<PERSON><PERSON><PERSON>ey('field', $this->filtertron->params());
    }

    public function test_usage_of_parameters(): void
    {
        $this->filtertron
            ->with('chapter', 'Sydney')
            ->without('title');

        $this->assertArrayHasKey('chapter', $this->filtertron->params());
        $this->assertArrayNotHasKey('title', $this->filtertron->params());
    }

    public function test_url_creation(): void
    {
        $this->mockRequest->shouldReceive('path')->twice()->andReturn('');

        $expectedUrl = '/?title=Entry&category=Places+to+be';

        $this->assertEquals($expectedUrl, $this->filtertron->url());
        $this->assertEquals($expectedUrl, (string) $this->filtertron);
    }

    public function test_it_can_return_a_sorter_html_output(): void
    {
        $request = m::mock(Request::class);
        $request->shouldReceive('get');
        $request->shouldReceive('query');
        $request->shouldReceive('path');

        $sorter = Filtertron::sorter($request, 'id', 'ID', ['test' => 1234, 'test2' => 5678]);

        $this->assertSame('<a href="/?order=id&dir=asc&page=1&test=1234&test2=5678" role="button" data-order="id" class="sortable" data-sort="none" aria-pressed="false">ID</a>', $sorter);
    }

    public function test_correct_data_sort_and_pressed_attributes(): void
    {
        $request = m::mock(Request::class);
        $request->shouldReceive('query');
        $request->shouldReceive('path');
        $request->shouldReceive('get')->withArgs(['dir', 'asc'])->andReturn('asc');
        $request->shouldReceive('get')->withArgs(['dir'])->andReturn('asc');
        $request->shouldReceive('get')->withArgs(['order'])->andReturn('test');

        $sorter = Filtertron::sorter($request, 'test', 'Test');

        $this->assertSame('<a href="/?order=test&dir=desc&page=1" role="button" data-order="test" class="sortable sort-asc" data-sort="ascending" aria-pressed="true">Test</a>', $sorter);
    }
}
