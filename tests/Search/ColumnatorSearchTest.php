<?php

namespace Tests\Search\Search;

use Illuminate\Support\Fluent;
use Mockery as m;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\ColumnatorSearch;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Dependency;
use Platform\Search\Enhancers\ExportEnhancer;
use Platform\Search\Enhancers\SearchEnhancer;
use Platform\Search\RelaxedColumnatorSearch;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tests\TestCase;

final class ColumnatorSearchTest extends TestCase
{
    /** @var FakeColumnator */
    private $columnator;

    /** @var Repository */
    private $repository;

    public function init()
    {
        app()->instance('localisation.translator', $translator = m::mock(Engine::class)->makePartial());

        $this->repository = m::mock(Repository::class);
        $this->columnator = new FakeColumnator([], []);
        $this->columnator->setRepository($this->repository);
    }

    public function testExecutesSearch(): void
    {
        $this->repository->shouldReceive('getByFilters')->andReturn(collect([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ]));

        $results = (new ColumnatorSearch($this->columnator))->search();

        $this->assertEquals([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ], $results->all());
    }

    public function testExecutesSearchLimit(): void
    {
        $this->repository->shouldReceive('getByFiltersLimit')
            ->with(m::any(), 100, 0)
            ->andReturn(collect([
                new Fluent(['key' => 1]),
                new Fluent(['key' => 2]),
            ]));

        $results = (new ColumnatorSearch($this->columnator))->searchLimit();

        $this->assertEquals([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ], $results->all());
    }

    public function testEnhancesSearchResults(): void
    {
        $this->columnator->addDependency(new FakeSearchEnhancer);

        $this->repository->shouldReceive('getByFilters')->andReturn(collect([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ]));

        $results = (new ColumnatorSearch($this->columnator))->search();

        $this->assertEquals([
            new Fluent(['key' => 1, 'type' => 'odd']),
            new Fluent(['key' => 2, 'type' => 'even']),
        ], $results->all());
    }

    public function testDoesntUseEnhancers(): void
    {
        $this->columnator->addDependency(new FakeSearchEnhancer);

        $this->repository->shouldReceive('getByFilters')->andReturn(collect([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ]));

        $results = (new ColumnatorSearch($this->columnator))->useEnhancers(false)->search(false);

        $this->assertEquals([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ], $results->all());

        $results = (new ColumnatorSearch($this->columnator))->useEnhancers(false)->fullSearch(false);

        $this->assertEquals([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ], $results->all());
    }

    public function testExportWithResultLimit(): void
    {
        $this->columnator->addDependency(new FakeSearchEnhancer);
        $total = collect();
        $columnatorSearch = new ColumnatorSearch($this->columnator);
        $columnatorSearch->setExportLimit($exportLimit = 500);

        $results = [0 => 0, 1 => 0, 2 => 0];
        for ($i = 0; $i < 510; $i++) {
            $total->push(new Fluent(['key' => $i]));
        }

        $this->repository->shouldReceive('getByFiltersForExport')
            ->with(m::any(), 0, 500)
            ->once()
            ->andReturn($total->skip(0)->take($exportLimit));

        $this->repository->shouldReceive('getByFiltersForExport')
            ->with(m::any(), 500, $exportLimit)
            ->once()
            ->andReturn($total->skip(500)->take($exportLimit));

        $this->repository->shouldReceive('getByFiltersForExport')
            ->with(m::any(), 1000, $exportLimit)
            ->once()
            ->andReturn($total->skip(1000)->take($exportLimit));

        foreach ($columnatorSearch->export(0, 500) as $traversable) {
            $results[0] += $traversable->count();
        }

        foreach ($columnatorSearch->export(500, 500) as $traversable) {
            $results[1] += $traversable->count();
        }

        $this->assertEquals(500, $results[0]);
        $this->assertEquals(10, $results[1]);
    }

    public function testRelaxedColumnatorSearch(): void
    {
        $this->repository->shouldReceive('relaxed')->andReturn(collect([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ]));

        $results = (new RelaxedColumnatorSearch($this->columnator))->search();

        $this->assertEquals([
            new Fluent(['key' => 1]),
            new Fluent(['key' => 2]),
        ], $results->all());
    }

    public function testExportEnhancer()
    {
        $this->columnator->addDependency(new FakeExportEnhancer);

        $this->repository->shouldReceive('getByFiltersForExport')->andReturn(collect([
            new Fluent(['key' => 'a']),
            new Fluent(['key' => 'b']),
        ]));

        foreach ((new ColumnatorSearch($this->columnator))->export(0, 10) as $results) {
            foreach ($results as $result) {
                $result->key === 'a' ?
                    $this->assertEquals('vowel', $result->type) :
                    $this->assertEquals('consonant', $result->type);
            }
        }
    }
}

class FakeColumnator extends Columnator
{
    protected $repository;

    protected $dependencies;

    public function addDependency(Dependency $dependency)
    {
        $this->dependencies = $this->dependencies ?: new Dependencies;
        $this->dependencies->add($dependency);
    }

    public function setRepository($repository)
    {
        $this->repository = $repository;
    }

    public function resource() {}

    public function availableDependencies(Defaults $view): Dependencies
    {
        return $this->dependencies ?: new Dependencies;
    }

    protected function baseColumns()
    {
        return new Columns([]);
    }

    public static function key(): string {}

    public static function exportKey(): string {}

    public function repository(): Repository
    {
        return $this->repository;
    }
}

class FakeSearchEnhancer implements SearchEnhancer
{
    public function enhance($results): void
    {
        foreach ($results as $result) {
            $result->type = ($result->key % 2 == 0) ? 'even' : 'odd';
        }
    }

    public function applies(): bool
    {
        return true;
    }
}

class FakeExportEnhancer implements ExportEnhancer
{
    public function enhance($results): void
    {
        foreach ($results as $result) {
            $result->type = 'wrong';
        }
    }

    public function enhanceForExport($results): void
    {
        foreach ($results as $result) {
            $result->type = $result->key === 'a' ? 'vowel' : 'consonant';
        }
    }

    public function applies(): bool
    {
        return true;
    }
}
