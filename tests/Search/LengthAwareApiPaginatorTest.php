<?php

namespace Tests\Search;

use Platform\Search\LengthAwareApiPaginator;
use Tests\TestCase;

final class LengthAwareApiPaginatorTest extends TestCase
{
    public function test_it_follows_the_api_io_standards(): void
    {
        $paginator = new LengthAwareApiPaginator([], 0, 1);

        $results = $paginator->toArray();

        $this->assertArrayHasKey('first_page_url', $results);
        $this->assertEquals('/?page=1', $results['first_page_url']);
        $this->assertArrayHasKey('last_page_url', $results);
        $this->assertEquals('/?page=1', $results['last_page_url']);
        $this->assertArrayHasKey('next_page_url', $results);
        $this->assertEquals('', $results['next_page_url']); // Not null
        $this->assertArrayHasKey('prev_page_url', $results);
        $this->assertEquals('', $results['prev_page_url']); // Not null
    }
}
