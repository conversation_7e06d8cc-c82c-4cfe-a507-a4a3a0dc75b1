<?php

namespace Tests\Search\View;

use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Mockery as m;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Column;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Services\Factory;
use Platform\Search\View\Columnator as ColumnatorViewObject;
use Tests\TestCase;

final class ColumnatorViewObjectTest extends TestCase
{
    public function testItLeavesHtmlInColumnTitleByDefault(): void
    {
        $request = m::mock(Request::class);
        $factorySpy = m::mock(Factory::class);
        $columnator = new FakeViewColumnator([], []);

        $request->shouldReceive('get', 'html')->andReturn(true);
        $request->shouldReceive('query')->andReturn([]);
        $factorySpy->shouldReceive('forArea')->andReturn($columnator);

        $viewObject = new ColumnatorViewObject($request, $factorySpy);

        $this->assertCount(2, $viewObject->availableColumns());
        $this->assertEquals('<custom-vue foo="bar">Title #1</custom-vue>', $viewObject->availableColumns()['one']);
        $this->assertEquals('Plain title #2', $viewObject->availableColumns()['two']);
    }

    public function testItStripsHtmlInColumnTitle(): void
    {
        $request = m::mock(Request::class);
        $factorySpy = m::mock(Factory::class);
        $columnator = new FakeViewColumnator([], []);

        $request->shouldReceive('get', 'html')->andReturn(false);
        $request->shouldReceive('query')->andReturn(['score-set' => 1]);
        $factorySpy->shouldReceive('forArea')->with(false, ['score-set' => 1])->andReturn($columnator);

        $viewObject = new ColumnatorViewObject($request, $factorySpy);

        $this->assertCount(2, $viewObject->availableColumns());
        $this->assertEquals('Title #1', $viewObject->availableColumns()['one']);
        $this->assertEquals('Plain title #2', $viewObject->availableColumns()['two']);
    }
}

class FakeViewColumnator extends Columnator
{
    public function baseColumns(): Columns
    {
        return new Columns([
            new FakeColumnatorColumn('all', 'one', '<custom-vue foo="bar">Title #1</custom-vue>'),
            new FakeColumnatorColumn('all', 'two', 'Plain title #2'),
        ]);
    }

    public function availableDependencies(Defaults $view): Dependencies {}

    public function resource() {}

    protected function fieldColumns() {}

    public static function key(): string {}

    public static function exportKey(): string {}

    public function repository(): Repository {}
}

class FakeColumnatorColumn implements Column
{
    private $default;

    private $name;

    private $title;

    public function __construct(string $default, string $name, string $title)
    {
        $this->default = $default;
        $this->name = $name;
        $this->title = $title;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function title(): string
    {
        return $this->title;
    }

    public function dependencies(): Collection {}

    public function field() {}

    public function value($record) {}

    public function html($record): string {}

    public function default(): Defaults
    {
        return new Defaults($this->default);
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 1;
    }

    public function sortable(): bool
    {
        return true;
    }
}
