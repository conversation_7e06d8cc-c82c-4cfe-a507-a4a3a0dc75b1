<?php

namespace Search;

use Platform\Search\Defaults;
use Tests\TestCase;

final class DefaultsTest extends TestCase
{
    public function test_it_returns_true_when_values_are_identical(): void
    {
        $this->assertTrue((new Defaults('search'))->matches(new Defaults('search')));
    }

    public function test_it_returns_true_when_values_are_similar(): void
    {
        $this->assertTrue((new Defaults('all'))->matches(new Defaults('search')));
    }

    public function test_it_returns_true_when_comparison_value_is_similar(): void
    {
        $this->assertTrue((new Defaults('search'))->matches(new Defaults('all')));
    }

    public function test_it_returns_false_when_values_are_not_comparable(): void
    {
        $this->assertFalse((new Defaults('none'))->matches(new Defaults('all')));
    }
}
