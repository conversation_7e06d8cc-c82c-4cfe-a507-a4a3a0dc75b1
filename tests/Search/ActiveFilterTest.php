<?php

namespace Tests\Search;

use Platform\Search\ActiveFilter;
use Tests\TestCase;

final class ActiveFilterTest extends TestCase
{
    public function test_it_is_initialisable(): void
    {
        $filter = new ActiveFilter('key', 'value', 'text-a_licious');

        $this->assertSame($filter->key, 'key');
        $this->assertSame($filter->value, 'value');
        $this->assertSame($filter->text, 'text a licious');
    }
}
