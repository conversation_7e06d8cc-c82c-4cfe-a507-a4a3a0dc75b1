<?php

namespace Tests\Http\Middleware;

use Illuminate\Support\Facades\Session;
use Platform\Http\Middleware\Remembrance;
use Tests\TestCase;

final class RemembranceTest extends TestCase
{
    public function test_remembrance_for_url(): void
    {
        Session::shouldReceive('get')->with('Platform\Http\Middleware\Remembrance|path/path')->andReturn('result');

        $this->assertEquals('result', Remembrance::forUrl('http://test.awardsplatform.com/path/path'));
    }

    public function test_parameters_are_removed_from_url_and_session()
    {
        Session::spy();

        $this->assertEquals($twoParameters = '/category?parameter1=1&parameter2=2', Remembrance::removeFilters($twoParameters, []));
        $this->assertEquals('/category?parameter1=1', Remembrance::removeFilters($twoParameters, ['parameter2']));
        $this->assertEquals($noParameters = '/category', Remembrance::removeFilters($noParameters, []));

        Session::shouldHaveReceived('forget')->atLeast()->times(2);
    }
}
