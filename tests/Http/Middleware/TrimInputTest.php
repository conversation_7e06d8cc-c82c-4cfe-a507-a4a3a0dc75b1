<?php

namespace Tests\Http\Middleware;

use Illuminate\Http\Request;
use Platform\Http\Middleware\TrimInput;
use Tests\TestCase;

final class TrimInputTest extends TestCase
{
    public function test_trims_input(): void
    {
        $request = Request::capture();
        $request->merge(['one' => ' one ', ['two' => ' tw o '], 'threepassword' => '  pass  ']);

        $response = (new TrimInput)->handle($request, function ($request) {
            return $request;
        });

        $this->assertEquals(['one' => 'one', ['two' => 'tw o'], 'threepassword' => '  pass  '], $response->all());
    }
}
