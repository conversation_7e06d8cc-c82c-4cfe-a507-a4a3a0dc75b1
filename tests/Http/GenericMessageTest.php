<?php

namespace Tests\Http;

use Mockery as m;
use Platform\Http\GenericMessage;
use Tests\TestCase;

final class GenericMessageTest extends TestCase
{
    public function test_it_can_return_an_appropriate_redirect(): void
    {
        // This sucks, but I couldn't figure out how to setup routes in this test suite
        // As a result, we're just executing all code and making sure nothing errors. //- Kirk
        $redirector = m::mock('redirector');
        $redirector->shouldReceive('route')->andReturn($redirector);
        $redirector->shouldReceive('with')->andReturn($redirector);

        $this->app->instance('redirect', $redirector);

        $message = new GenericMessage('This is the message');
        $message->withTitle('Title');
        $message->withButtonText('Button text');
        $message->forwardTo('url');

        $message->response();
    }
}
