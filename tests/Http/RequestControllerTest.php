<?php

namespace Tests\Http;

use Illuminate\Http\Request;
use <PERSON><PERSON>y as m;
use Platform\Http\RequestController;
use Tests\TestCase;

final class RequestControllerTest extends TestCase
{
    private $request;

    public function init()
    {
        $this->request = m::mock(Request::class);
        $this->request->shouldR<PERSON>eive('route')->andReturn($this->request);
        $this->request->shouldReceive('getAction')->andReturn(['uses' => 'SomeController@action']);
    }

    public function test_it_can_return_the_controller_for_the_request(): void
    {
        $this->assertSame('SomeController', (new RequestControllerStub)->get('controller', $this->request));
    }

    public function test_it_can_return_the_action_for_the_request(): void
    {
        $this->assertSame('action', (new RequestControllerStub)->get('action', $this->request));
    }
}

class RequestControllerStub
{
    use RequestController;

    public function get($method, Request $request)
    {
        return $this->$method($request);
    }
}
