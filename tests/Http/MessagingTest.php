<?php

namespace Tests\Http;

use Platform\Http\Messaging;
use Tests\TestCase;

final class MessagingTest extends TestCase
{
    private $messenger;

    public function init()
    {
        $this->messenger = new class
        {
            use Messaging;

            public function message($message, $type, $route = null, $jsonStatus = 400)
            {
                return $this->withMessage($message, $type, $route, $jsonStatus);
            }
        };
    }

    public function test_it_returns_a_jspon_response_when_requested(): void
    {
        $response = $this->messenger->message('message', 'type');

        $this->assertTrue($response->isRedirect());
    }
}
