<?php

namespace Tests\Html;

use Illuminate\Contracts\Support\MessageBag;
use Illuminate\Contracts\View\Factory;
use Mockery as m;
use Platform\Html\FormError;
use Tests\TestCase;

final class FormErrorTest extends TestCase
{
    private $formError;

    private $mockErrorBag;

    private $viewFactory;

    private $mockErrors;

    public function init()
    {
        // Fix for GlobalComposer include on template
        $this->mockErrors = m::mock('errors');

        $this->mockErrorBag = m::mock(MessageBag::class);
        $this->mockErrorBag->shouldReceive('getBag')->once()->with('default')->andReturn($this->mockErrors);

        $this->viewFactory = m::mock(Factory::class);
        $this->viewFactory->shouldReceive('getShared')->once()->andReturn(['errors' => $this->mockErrorBag]);

        $this->formError = new FormError($this->viewFactory);
    }

    public function test_class_retrieval_when_errored(): void
    {
        $this->mockErrors->shouldReceive('has')->once()->with('field')->andReturn(true);

        $this->assertSame('error', $this->formError->classIfError('field'));
    }

    public function test_html_with_no_errors(): void
    {
        $this->mockErrors->shouldReceive('has')->once()->with('field')->andReturn(false);

        $this->assertEmpty($this->formError->classIfError('field'));
    }

    public function test_rendering_with_errors(): void
    {
        $this->mockErrors->shouldReceive('has')->twice()->with('field')->andReturn(true);
        $this->mockErrors->shouldReceive('first')->once()->with('field')->andReturn('an error message');

        $this->viewFactory->shouldReceive('make')->once()->andReturn($this->viewFactory);
        $this->viewFactory->shouldReceive('render')->once()->andReturn($this->viewFactory);

        $this->formError->message('field');
    }

    public function test_rendering_with_translated_errors(): void
    {
        $lang = function_exists('consumer') ? consumer()->languageCode() : '*';
        $this->mockErrors->shouldReceive('has')->atMost(3)->with('translated.field.'.$lang)->andReturn(true);
        $this->mockErrors->shouldReceive('first')->once()->with('translated.field.'.$lang)->andReturn('an error message');

        $this->viewFactory->shouldReceive('make')->once()->andReturn($this->viewFactory);
        $this->viewFactory->shouldReceive('render')->once()->andReturn($this->viewFactory);

        $this->assertNotEmpty($this->formError->classIfErrorTranslated('field', $lang));
        $this->assertNotEmpty($this->formError->messageTranslated('field', $lang));
    }
}
