<?php

namespace Tests\Html;

use <PERSON><PERSON><PERSON> as m;
use <PERSON><PERSON>\Html\Facades\Html;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tests\TestCase;

final class MacrosTest extends TestCase
{
    public function init()
    {
        require_once __DIR__.'/../../src/Html/macros.php';
    }

    public function test_it_returns_the_relative_time(): void
    {
        $this->assertStringEndsWith('ago', (string) HTML::relativeTime('2012-05-18'));
    }

    public function test_it_returns_valid_updated_value(): void
    {
        $model = new Model;
        $model->exists = false;

        $this->assertSame('<input type="hidden" name="updatedAt" value="">', Html::updatedAt($model)->toHtml());
    }

    public function test_it_signals_the_deleted_state(): void
    {
        $model1 = new Model;
        $model1->deletedAt = 'now';

        $model2 = new Model;

        $this->assertSame('<span class="af-icons af-icons-close deleted-state"></span>', HTML::deletedState($model1)->toHtml());
        $this->assertNull(HTML::deletedState($model2));
    }

    public function test_it_alters_output_based_on_model_deleted_state(): void
    {
        $model1 = new Model;
        $model1->deletedAt = 'now';
        $model1->value = 'Value';

        $this->assertSame('<span class="deleted">Value</span>', HTML::maybeDeleted($model1, $model1->value)->toHtml());
    }

    public function test_merge_fields_output(): void
    {
        $this->assertSame('<span class="hidden" id="trigger-merge-fields">{field1}, {field2}</span>', HTML::mergeFields('trigger', ['field1', 'field2'])->toHtml());
    }

    public function test_caret_output_reflects_change(): void
    {
        $this->assertSame('<span class="af-icons af-icons-arrow-up af-icons-sm"></span>', HTML::caretStat(1)->toHtml());
        $this->assertSame('<span class="af-icons af-icons-arrow-down af-icons-sm"></span>', HTML::caretStat(-2)->toHtml());
    }

    public function test_marker_checkbox(): void
    {
        $translator = m::mock(Engine::class);
        app()->instance('translator', $translator);

        $translator->shouldReceive('get')->with('fields.types.checkbox', [], null)->twice()
            ->andReturn('Checkbox');

        $this->assertSame(
            '<label for="23"><span class="sr-only">Checkbox</span></label><input class="marker-checkbox" type="checkbox" id="23" data-id="23">',
            (string) HTML::markerCheckbox(23)
        );
        $this->assertSame(
            '<label for="23"><span class="sr-only">Checkbox</span></label><input class="marker-checkbox" type="checkbox" id="23" data-id="23" disabled>',
            (string) HTML::markerCheckbox(23, true)
        );
    }

    public function test_iso_zulu_datetime(): void
    {
        $this->assertSame('2020-12-18T00:00:00Z', HTML::isoZuluDateTime('2020-12-18 00:00:00'));
        // GMT +02:00 timezone
        $this->assertSame('2020-12-17T22:00:00Z', HTML::isoZuluDateTime('2020-12-18 00:00:00', 'Europe/Athens'));
    }

    public function test_atom_datetime(): void
    {
        $this->assertSame('2020-12-18T00:00:00+00:00', HTML::atomDateTime('2020-12-18 00:00:00'));
        // GMT +02:00 timezone
        $this->assertSame('2020-12-18T00:00:00+02:00', HTML::atomDateTime('2020-12-18 00:00:00', 'Europe/Athens'));
    }

    public function test_timezone_works_when_model_is_null()
    {
        $timezone = Html::timezone('timezone', null, ['class' => 'form-control']);
        $this->withoutExceptionHandling();
        $this->assertStringContainsString('UTC', $timezone);
        $this->assertStringContainsString('form-control', $timezone);
    }

    public function test_datetimezone_works_when_timezone_is_empty_string()
    {
        $timeZone = HTML::dateTimezone(date('Y-m-d H:i:s'), '', 'Europe/Athens');

        $this->assertStringContainsString('UTC', $timeZone);
    }
}

class Model extends \Platform\Database\Eloquent\Model {}
