<?php

namespace Tests\Html\Multiselect;

use Illuminate\Support\Facades\Request;
use Mockery as m;
use Platform\Html\Multiselect\Multiselect;
use Platform\Html\Multiselect\OptionSubset;
use Platform\Html\Multiselect\SubsetMultiselect;
use Tests\CrawlsViews;
use Tests\TestCase;

final class SubsetMultiselectTest extends TestCase
{
    use CrawlsViews;

    public function test_it_is_initialisable(): void
    {
        $multiselect = new SubsetMultiselect('selector', [], new OptionSubset('radio', 'subset', 'Sbuset'));

        $this->assertInstanceOf(SubsetMultiselect::class, $multiselect);
        $this->assertInstanceOf(Multiselect::class, $multiselect);
    }

    public function test_it_set_the_selected_options(): void
    {
        $multiselect = new SubsetMultiselect('name', ['option' => 'value'], new OptionSubset('checkbox', 'subs', 'Check'));

        $multiselect->setSelected(['value'], ['subs']);

        $this->assertEquals(['value'], $multiselect->selected());
        $this->assertEquals(['subs'], $multiselect->subsetSelected());
    }

    public function test_it_is_renderable(): void
    {
        $request = m::mock(\Illuminate\Http\Request::class);
        $request->shouldReceive('setUserResolver');
        $request->shouldReceive('input')->andReturn([]);
        $request->shouldReceive('old')->with('name', [])->andReturn([]);
        $request->shouldReceive('old')->with('subs', m::any())->andReturn('');

        Request::swap($request);

        $multiselect = new SubsetMultiselect('name', ['option' => 'value'], new OptionSubset('checkbox', 'subs', 'Check'));
        $multiselect->setSelected(['value'], ['subs']);
        $multiselect->configure(['selectAll' => true]);

        $html = $multiselect->toHtml();

        $this->assertStringContainsString('subs', $html);
        $this->assertStringContainsString('option', $html);
    }
}
