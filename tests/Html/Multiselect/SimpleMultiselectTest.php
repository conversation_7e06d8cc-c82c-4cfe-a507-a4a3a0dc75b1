<?php

namespace Tests\Html\Multiselect;

use Illuminate\Support\Facades\Request;
use Mockery as m;
use Platform\Html\Multiselect\SimpleMultiselect;
use Tests\CrawlsViews;
use Tests\TestCase;

final class SimpleMultiselectTest extends TestCase
{
    use CrawlsViews;

    public function test_it_is_initialisable(): void
    {
        $this->assertInstanceOf(SimpleMultiselect::class, new SimpleMultiselect('m-select', [], []));
    }

    public function test_it_works_with_default_configuration(): void
    {
        $multiselect = new SimpleMultiselect('name', []);
        $multiselect->configure(['filter' => true]);

        $this->assertSame('name', $multiselect->name());
        $this->assertFalse($multiselect->counter());
        $this->assertTrue($multiselect->filter());
        $this->assertFalse($multiselect->selectAll());
    }

    public function test_it_sets_selected_options(): void
    {
        $multiselect = new SimpleMultiselect('name', []);
        $multiselect->setSelected(['one', 'two']);

        $this->assertCount(2, $multiselect->selected());
    }

    public function test_it_can_retrieve_configuration_values(): void
    {
        $multiselect = new SimpleMultiselect('name', []);
        $multiselect->configure(['setting' => 'value']);

        $this->assertSame('value', $multiselect->config('setting'));
        $this->assertNull($multiselect->config('nothing'));
    }

    public function test_it_is_renderable(): void
    {
        $multiselect = new SimpleMultiselect('name', ['bushell' => 'Bushell', 'carpio', 'Carpio']);

        $request = m::mock('somerequest');
        $request->shouldReceive('setUserResolver');
        $request->shouldReceive('old')->andReturn([]);

        Request::swap($request);

        $html = $multiselect->toHtml();

        $this->assertStringContainsString('name[]', $html);
        $this->assertStringContainsString('bushell', $html);
    }
}
