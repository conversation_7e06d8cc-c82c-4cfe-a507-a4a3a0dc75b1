<?php

namespace Tests\Html;

use Mockery as m;
use Platform\Html\Markdown;
use Platform\Html\Parsedown;
use Tests\TestCase;

final class MarkdownTest extends TestCase
{
    private Markdown $markdown;

    private Parsedown|m\MockInterface $parsedown;

    public function init(): void
    {
        $this->parsedown = m::mock(Parsedown::class);
        $this->parsedown->shouldReceive('setMarkupEscaped')->with(true);

        $this->markdown = new Markdown($this->parsedown);
    }

    public function test_single_line_content_uses_inline_formatting(): void
    {
        $this->parsedown->shouldReceive('text')->andReturn('asdfasdf');
        $this->markdown->parse('lkjasdflk');
    }

    public function test_full_content_uses_text_strategy(): void
    {
        $this->parsedown->shouldNotReceive('line');
        $this->parsedown->shouldReceive('setBreaksEnabled->text')->andReturn('kljasdflkjasdf');

        $this->markdown->parse("lkjasdflk\r\n\nOther line");
    }

    public function test_parse_renders_inline_markdown_correctly(): void
    {
        $markdown = new Markdown((new Parsedown));

        // Inline Markdown returns "Star Wars" without <p> a tag,
        // even though Parsedown::text() originally wraps it with <p>. We strip it manually.
        $this->assertEquals('Star Wars', $markdown->parse('Star Wars'));

        $this->assertSame("<ol start=\"2\">\n<li>Star Wars</li>\n</ol>", $markdown->parse('2. Star Wars'));
        $this->assertSame("<ul>\n<li>Star Wars</li>\n</ul>", $markdown->parse('- Star Wars'));
        $this->assertSame('<h2>Star Wars</h2>', $markdown->parse('## Star Wars'));
        $this->assertSame('<strong>Star Wars</strong>', $markdown->parse('**Star Wars**'));
        $this->assertSame('<em>Star Wars</em>', $markdown->parse('*Star Wars*'));
        $this->assertSame('<a href="https://starwars.com" data-redirector="https://starwars.com">Star Wars</a>', $markdown->parse('[Star Wars](https://starwars.com)'));
    }

    public function test_inline_markdown_conversion(): void
    {
        $markdown = new Markdown((new Parsedown));

        $inline = "If there is a :brand account associated with :login then you will receive an email with recovery options.  <br>
          
            If you don't see your email in a couple of minutes, please check your junk mail or spam folder.";

        $expected = "If there is a :brand account associated with :login then you will receive an email with recovery options.  <br><br>
If you don't see your email in a couple of minutes, please check your junk mail or spam folder.";

        $this->assertSame($expected, $markdown->inline($inline));
    }

    public function test_inline_markdown_conversion_with_newlines(): void
    {
        $markdown = new Markdown((new Parsedown));

        $this->assertSame("<h1>Header 1</h1>\n<h2>Header 2</h2>", $markdown->inline("# Header 1\n ## Header 2"));
        $this->assertSame("<h1>Header 1</h1>\n<h2>Header 2</h2>", $markdown->inline("# Header 1\r ## Header 2"));
        $this->assertSame("<h1>Header 1</h1>\n<h2>Header 2</h2>", $markdown->inline("# Header 1\r\n ## Header 2"));
        $this->assertSame("<h1>Header 1</h1>\n<h2>Header 2</h2>", $markdown->inline("# Header 1\r\n\r\n ## Header 2"));
        $this->assertSame("<h1>Header 1</h1>\n<h2>Header 2</h2>", $markdown->inline("# Header 1\n\n ## Header 2"));
    }

    public function test_inline_markdown_does_not_wrap_content_in_p_tags(): void
    {
        $markdown = new Markdown(new Parsedown);

        $input = "This is the first paragraph.\nThis is the second paragraph.";
        $expected = 'This is the first paragraph.<br>This is the second paragraph.';

        $this->assertSame($expected, $markdown->inline($input));
    }

    public function test_correctly_parses_markdown_with_lists(): void
    {
        $markdown = new Markdown(new Parsedown);

        $input = "This is a filed label\r\n 1. First item\n2. Second item\n3. Third item";
        $expected = "This is a filed label<br>\n<ol>\n<li>First item</li>\n<li>Second item</li>\n<li>Third item</li>\n</ol>";

        $this->assertSame($expected, $markdown->parse($input));
    }
}
