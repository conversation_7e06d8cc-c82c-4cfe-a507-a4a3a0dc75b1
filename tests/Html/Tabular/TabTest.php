<?php

namespace Tests\Html\Tabular;

use Platform\Html\Tabular\Tab;
use Tests\TestCase;

final class TabTest extends TestCase
{
    const TAB_NAME = 'Tab Name';

    const TAB_VIEW = 'tab.view';

    /**
     * @var Tab
     */
    protected $tab;

    public function init()
    {
        $this->tab = new Tab(self::TAB_NAME, self::TAB_VIEW, Tab::STATUS_ACTIVE);
    }

    public function testName(): void
    {
        $this->assertEquals(self::TAB_NAME, $this->tab->name());
    }

    public function testView(): void
    {
        $this->assertEquals(self::TAB_VIEW, $this->tab->view());
    }

    public function testStatus(): void
    {
        $this->assertEquals(Tab::STATUS_ACTIVE, $this->tab->status());

        $this->tab->setStatus(Tab::STATUS_DISABLED);
        $this->assertEquals(Tab::STATUS_DISABLED, $this->tab->status());
    }

    public function testSetActive(): void
    {
        $this->tab->setActive();
        $this->assertEquals(Tab::STATUS_ACTIVE, $this->tab->status());
    }

    public function testIsActive(): void
    {
        $this->tab->setActive();
        $this->assertTrue($this->tab->isActive());

        $this->tab->setDisabled();
        $this->assertFalse($this->tab->isActive());
    }

    public function testSetDisabled(): void
    {
        $this->tab->setDisabled();
        $this->assertEquals(Tab::STATUS_DISABLED, $this->tab->status());
    }

    public function testIsDisabled(): void
    {
        $this->tab->setDisabled();
        $this->assertTrue($this->tab->isDisabled());

        $this->tab->setActive();
        $this->assertFalse($this->tab->isDisabled());
    }

    public function testId(): void
    {
        $this->assertTrue(is_string($this->tab->id()));
    }

    public function testClasses(): void
    {
        $this->assertTrue(is_array($this->tab->classes()));

        $this->tab->setActive();
        $this->assertTrue(in_array(Tab::STATUS_ACTIVE, $this->tab->classes()));

        $this->tab->setDisabled();
        $this->assertTrue(in_array(Tab::STATUS_DISABLED, $this->tab->classes()));
    }
}
