<?php

namespace Tests\Html\Tabular;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Mockery as m;
use Platform\Html\Tabular\Tab;
use Platform\Html\Tabular\Tabular;
use Tests\TestCase;

final class TabularTest extends TestCase
{
    private $request;

    /**
     * @var Tabular
     */
    protected $tabular;

    public function init()
    {
        $view = m::mock('someview');
        $view->shouldReceive('make')->andReturn($view);
        $view->shouldReceive('render');

        View::swap($view);

        $this->request = m::mock(Request::class);
        $this->request->shouldReceive('filled')->andReturn(false)->byDefault();

        $this->tabular = new Tabular($this->request, 'normal');
    }

    public function test_when_adding_tabs_that_are_requested_by_user_it_should_activate_the_added_tab(): void
    {
        $request = m::mock(Request::class);
        $request->shouldR<PERSON>eive('filled')->once()->with('tab')->andReturn(true);
        $request->shouldR<PERSON>eive('get')->with('tab')->once()->andReturn('tab');

        $tabular = new Tabular($request, 'normal');
        $tab = new Tab('something', 'tab');

        $tabular->addTab($tab);

        $this->assertSame($tab, $tabular->getActive());
    }

    public function test_style_usage(): void
    {
        $this->assertEquals('normal', $this->tabular->style());
    }

    public function test_the_adding_of_inactive_tabs(): void
    {
        $tab = m::spy(Tab::class);
        $this->tabular->addTab($tab);

        $tab->shouldNotHaveReceived('id');
    }

    public function test_addition_of_active_tabs(): void
    {
        $tab1 = new Tab('something', 'blank-tab');
        $tab2 = new Tab('something else', 'another-tab');

        $this->tabular->addTab($tab1);
        $this->tabular->addTab($tab2);
        $this->tabular->setActive($tab1->id());

        $this->assertCount(2, $this->tabular->tabs());
        $this->assertSame($tab1, $this->tabular->getActive());
    }

    public function test_no_active_tab_returns_null(): void
    {
        $this->assertNull($this->tabular->getTab(9999));
        $this->assertNull($this->tabular->getActive());
    }

    public function test_tab_retrieval(): void
    {
        $tab = new Tab('Testing', 'blank-tab');
        $this->tabular->addTab($tab);
        $tabs = $this->tabular->tabs();

        $this->assertTrue(is_array($tabs));
        $this->assertTrue(in_array($tab, $tabs));
    }

    public function test_it_can_render_tabs(): void
    {
        $tab = new Tab('Testing', 'tab');
        $this->tabular->addTab($tab);

        $this->tabular->render();
    }

    public function test_to_array(): void
    {
        $this->tabular->addTab($tabA = new Tab('Testing A', 'tab_a', Tab::STATUS_ACTIVE));
        $this->tabular->addTab($tabB = new Tab('Testing B', 'tab_b'));

        $this->assertSame($this->tabular->toArray(), [
            [
                'id' => $tabA->id(),
                'name' => 'Testing A',
                'view' => 'tab_a',
                'status' => 'active',
                'classes' => ['active'],
            ],
            [
                'id' => $tabB->id(),
                'name' => 'Testing B',
                'view' => 'tab_b',
                'status' => '',
                'classes' => [],
            ],
        ]);
    }
}
