<?php

namespace Tests\Html;

use Illuminate\Contracts\View\Factory;
use Mockery as m;
use Platform\Html\ButtonBuilder;
use Platform\Html\Permissions;
use Tests\TestCase;

final class ButtonBuilderTest extends TestCase
{
    private $view;

    private $builder;

    private $permissions;

    public function init()
    {
        $this->permissions = m::mock(Permissions::class);
        $this->view = m::mock(Factory::class);
        $this->builder = new ButtonBuilder($this->permissions, $this->view);
    }

    public function test_it_can_generate_links_with_no_options(): void
    {
        $this->view->shouldReceive('make')->with('html.buttons.link', ['title' => 'Home', 'url' => 'home', 'icon' => '', 'iconClass' => '', 'size' => 'lg', 'type' => '', 'buttonClass' => ''])->once()->andReturn($this->view);
        $this->view->shouldReceive('render')->once()->andReturn('optionless button');

        $this->assertEquals('optionless button', $this->builder->link('home', 'Home'));
    }

    public function test_it_can_generate_links_with_icon_options(): void
    {
        $this->view->shouldReceive('make')->with('html.buttons.link', ['title' => 'Home', 'url' => 'home', 'icon' => 'setting', 'iconClass' => 'icon', 'size' => 'lg', 'type' => '', 'buttonClass' => ''])->once()->andReturn($this->view);
        $this->view->shouldReceive('render')->once()->andReturn('icon button');

        $this->assertEquals('icon button', $this->builder->link('home', 'Home', ['icon' => 'setting']));
    }

    public function test_permission_denied_links(): void
    {
        $this->permissions->shouldReceive('permits')->once()->with(['User' => 'read'])->andReturn(false);

        $this->assertNull($this->builder->link('users', 'Users', ['permissions' => ['User' => 'read']]));
    }

    public function testLinkWithPermissionsAllowed(): void
    {
        $this->permissions->shouldReceive('permits')->once()->with(['User' => 'write'])->andReturn(true);

        $this->view->shouldReceive('make')->with('html.buttons.link', ['title' => 'Edit user', 'url' => 'users/edit/1', 'icon' => '', 'iconClass' => '', 'size' => 'lg', 'type' => '', 'buttonClass' => ''])->once()->andReturn($this->view);
        $this->view->shouldReceive('render')->once()->andReturn('permitted button');

        $this->assertEquals('permitted button', $this->builder->link('users/edit/1', 'Edit user', ['permissions' => ['User' => 'write']]));
    }
}
