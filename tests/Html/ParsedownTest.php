<?php

namespace Tests\Html;

use Platform\Html\Parsedown;
use Tests\TestCase;

class ParsedownTest extends TestCase
{
    private Parsedown $parsedown;

    public function init(): void
    {
        $this->parsedown = new Parsedown;
    }

    public function test_parse(): void
    {
        $this->assertEquals('<p><strong>strong</strong> <em>italic</em></p>', $this->parsedown->parse('**strong** *italic*'));
    }

    public function test_parse_videos(): void
    {
        $youtubeMarkdown = $this->parsedown->parse('![](https://www.youtube.com/watch?v=AoPbuzopngc)');
        $vimeoMarkdown = $this->parsedown->parse('![600](https://vimeo.com/135510971)');

        $this->assertStringContainsString('iframe', $youtubeMarkdown);
        $this->assertStringContainsString('https://www.youtube.com/embed/AoPbuzopngc', $youtubeMarkdown);
        $this->assertStringContainsString('width="600" height="338"', $vimeoMarkdown);
    }

    public function test_it_creates_links_that_open_in_new_browser_windows(): void
    {
        $this->assertEquals('<p><img src="https://127.0.0.1/img.png" alt="image" data-redirector="https://127.0.0.1/img.png" class="markdown-img" /></p>', $this->parsedown->parse('![image](https://127.0.0.1/img.png)'));
    }

    public function test_it_embeds_youtube_and_vimeo_links(): void
    {
        $result1 = $this->parsedown->parse('![youtube](https://youtube.com/watch?v=ksjdflkjsdf)');
        $result2 = $this->parsedown->parse('![youtube](https://vimeo.com/lksjdflkjsdf/123456)');
        $result3 = $this->parsedown->parse('!(https://someurl.com)');

        // basically shouldn't do anything
        $this->parsedown->parse('![nothing] (http://theweb.com)');

        $this->assertStringContainsString('https://www.youtube.com/embed/ksjdflkjsdf', $result1);
        $this->assertStringContainsString('https://player.vimeo.com/video/123456', $result2);
        $this->assertStringContainsString('https://someurl.com', $result3);
    }

    public function test_parse_af_field(): void
    {
        $this->markTestSkipped('Somehow this has broken, its not matching af-fields at all');
        $expected = '<div class="not-escaped-html">Field content</div>';
        $payload = "\n!<af-field>".$expected."</af-field>\n";

        $this->assertEquals($expected, $this->parsedown->setMarkupEscaped(true)->text($payload));

        // Escaped html if it is not af-field
        $this->assertStringContainsString('&lt;div', $this->parsedown->text($expected));
    }

    public function test_links_with_a_hyphen_at_the_end(): void
    {
        $this->assertStringContainsString('/123-123-', $this->parsedown->parse('link: https://google.com/test-test/azS/*/-12-/123-123- -ok-'));
    }

    public function test_inline_urls_open_in_new_tab_if_external(): void
    {
        $this->assertStringContainsString('target="_blank" rel="noopener"', $this->parsedown->parse('https://google.com'));
    }

    public function test_inline_urls_open_in_same_tab_if_internal(): void
    {
        $this->assertStringNotContainsString('target="_blank" rel="noopener"', $this->parsedown->parse('http://localhost/path'));
    }
}
