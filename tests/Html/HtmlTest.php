<?php

namespace Tests\Html;

use Illuminate\Http\Request;
use Platform\Html\Html;
use Tests\TestCase;

class HtmlTest extends TestCase
{
    private Html $html;

    public function init(): void
    {
        $this->html = new Html(new Request);
    }

    public function test_select_with_array_options_with_html(): void
    {
        $simpleArray = ['option1' => '<edit-interface-text class="edit-interface-text">Option 1</edit-interface-text>', 'option2' => 'Option 2'];
        $result = (string) $this->html->select('simple', $simpleArray);
        $this->assertStringContainsString('<select', $result);
        $this->assertStringContainsString('<option value="option1">Option 1</option>', $result);
        $this->assertStringContainsString('<option value="option2">Option 2</option>', $result);
    }

    public function test_select_with_multidimensional_array_options_with_html(): void
    {
        $multiArray = [
            'group1' => ['option1' => '<edit-interface-text class="edit-interface-text">Option 1</edit-interface-text>', 'option2' => 'Option 2'],
            'group2' => ['option3' => '<strong>Option 3</strong>', 'option4' => '<b>Option 4</b>'],
        ];
        $result = (string) $this->html->select('multi', $multiArray);
        $this->assertStringContainsString('<select', $result);
        $this->assertStringContainsString('<optgroup label="group1">', $result);
        $this->assertStringContainsString('<option value="option1">Option 1</option>', $result);
        $this->assertStringContainsString('<option value="option2">Option 2</option>', $result);
        $this->assertStringContainsString('<optgroup label="group2">', $result);
        $this->assertStringContainsString('<option value="option3">Option 3</option>', $result);
        $this->assertStringContainsString('<option value="option4">Option 4</option>', $result);
    }

    public function test_radio_button_with_value_zero_is_not_force_checked(): void
    {
        $result = (string) $this->html->radio('radio', false, 0);

        $this->assertStringNotContainsString('checked', $result);
    }
}
