<?php

namespace Tests\Html;

use Collective\Html\FormBuilder;
use Mockery as m;
use Platform\Html\FieldBuilder;
use Tests\TestCase;

final class FieldBuilderTest extends TestCase
{
    private $formBuilder;

    private $fieldBuilder;

    public function init()
    {
        $this->formBuilder = m::spy(FormBuilder::class);
        $this->fieldBuilder = new FieldBuilder($this->formBuilder);
    }

    public function test_generation_of_custom_text_field(): void
    {
        $this->fieldBuilder->custom('text', 'testField', 'Test value', ['id' => 'testField', 'class' => 'control-field']);
        $this->formBuilder->shouldHaveReceived('text')->once();
    }

    public function test_generation_of_custom_textarea_field(): void
    {
        $this->fieldBuilder->custom('textarea', 'testField', 'Test value', ['id' => 'testField', 'class' => 'control-field']);
        $this->formBuilder->shouldHaveReceived('textarea')->once();
    }

    public function test_it_can_generate_an_unchecked_checkbox_field(): void
    {
        $this->fieldBuilder->custom('checkbox', 'testField', null, ['id' => 'testField', 'class' => 'control-field']);
        $this->formBuilder->shouldHaveReceived('checkbox')->once();
    }

    public function testGeneratingCustomCheckedCheckboxField(): void
    {
        $this->fieldBuilder->custom('checkbox', 'testField', '1', ['id' => 'testField', 'class' => 'control-field']);
    }

    public function test_it_can_generate_a_date_field(): void
    {
        $this->fieldBuilder->custom('date', 'testField', '2016-01-01', ['id' => 'testField', 'class' => 'control-field']);
        $this->formBuilder->shouldHaveReceived('text')->once();
    }

    public function test_it_can_generate_a_time_field(): void
    {
        $this->fieldBuilder->custom('time', 'testField', '22:19', ['id' => 'testField', 'class' => 'control-field']);
        $this->formBuilder->shouldHaveReceived('text')->once();
    }

    public function test_generation_of_custom_datetime(): void
    {
        $this->fieldBuilder->custom('datetime', 'testField', '2016-01-01 22:19', ['id' => 'testField', 'class' => 'control-field']);
        $this->formBuilder->shouldHaveReceived('text')->once();
    }

    public function test_generating_a_custom_select_field(): void
    {
        $options = ['1' => 'One', '2' => 'Two', '3' => 'Three'];

        $this->fieldBuilder->custom('select', 'testField', '2', ['id' => 'testField', 'class' => 'control-field', $options]);
        $this->formBuilder->shouldHaveReceived('select')->once();
    }
}
