<?php

namespace Tests\Html;

use Tests\TestCase;

final class SelectOptionsTest extends TestCase
{
    public function testItReturnsOptions(): void
    {
        $user1 = new FakeUser($id1 = 44, '<PERSON> & Sue');
        $user2 = new FakeUser($id2 = 55, 'Person');

        $result = for_select_sorted(collect([$user1, $user2]));

        $this->assertCount(2, $result);
        $this->assertEquals('<PERSON> & Sue', $result[$id1]);
        $this->assertEquals('Person', $result[$id2]);
    }

    public function testItCanSortOptions(): void
    {
        $user1 = new FakeUser($id1 = 33, 'Rafał P');
        $user2 = new FakeUser($id2 = 44, '<PERSON> Sue');
        $user3 = new FakeUser($id3 = 55, 'John & Doe');

        $result = for_select_sorted(collect([$user1, $user2, $user3]));
        $keys = array_keys($result);

        $this->assertCount(3, $result);
        $this->assertEquals('<PERSON> <PERSON>', $result[$keys[0]]);
        $this->assertEquals('<PERSON> Sue', $result[$keys[1]]);
        $this->assertEquals('<PERSON><PERSON>ł P', $result[$keys[2]]);
    }
}

class FakeUser
{
    public $id;

    public $name;

    public function __construct($id, $name)
    {
        $this->id = $id;
        $this->name = $name;
    }
}
