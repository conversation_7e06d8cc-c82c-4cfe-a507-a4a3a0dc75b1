<?php

namespace Tests\Menu\Context;

use Illuminate\Support\Collection;
use Platform\Authorisation\Permission;
use Platform\Menu\Context\ContextMenuService;
use Tests\TestCase;

final class ContextMenuServiceTest extends TestCase
{
    private array $permissionSet;

    private ContextMenuService $contextMenuService;

    public function init()
    {
        Permission::unguard();
        $this->contextMenuService = app(ContextMenuService::class);
        $this->permissionSet = [
            'ProgramManager' => [
                new Permission(['resource' => 'Chapters', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'EntriesAll', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'Users', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'Download', 'action' => 'view', 'mode' => 'allow']),
            ],
            'ChapterManager' => [
                new Permission(['resource' => 'Chapters', 'action' => 'create', 'mode' => 'deny']),
                new Permission(['resource' => 'EntriesAll', 'action' => 'view', 'mode' => 'allow']),
            ],
            'Entrant' => [
                new Permission(['resource' => 'EntriesOwner', 'action' => 'view', 'mode' => 'allow']),
            ],
            'Judge' => [
                new Permission(['resource' => 'ScoresOwner', 'action' => 'create', 'mode' => 'allow']),
            ],
            'Bookkeeper' => [
                new Permission(['resource' => 'Funding', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Grants', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Orders', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Orders', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'Payments', 'action' => 'create', 'mode' => 'allow']),
                new Permission(['resource' => 'Payments', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Payments', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'Payments', 'action' => 'delete', 'mode' => 'allow']),
            ],
            'Auditor' => [
                new Permission(['resource' => 'EntriesAll', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Audit', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Broadcasts', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'ContractAll', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Forms', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Funding', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Grants', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Notifications', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Orders', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Panels', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Roles', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Rounds', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'ScoreSets', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'ScoresAll', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'ScoringCriteria', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Seasons', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'Users', 'action' => 'view', 'mode' => 'allow']),

                new Permission(['resource' => 'EntriesOwner', 'action' => 'create', 'mode' => 'deny']),
                new Permission(['resource' => 'EntriesOwner', 'action' => 'view', 'mode' => 'deny']),
                new Permission(['resource' => 'EntriesOwner', 'action' => 'update', 'mode' => 'deny']),
                new Permission(['resource' => 'EntriesOwner', 'action' => 'delete', 'mode' => 'deny']),
                new Permission(['resource' => 'ScoresOwner', 'action' => 'create', 'mode' => 'deny']),
                new Permission(['resource' => 'ScoresOwner', 'action' => 'view', 'mode' => 'deny']),
                new Permission(['resource' => 'ScoresOwner', 'action' => 'update', 'mode' => 'deny']),
                new Permission(['resource' => 'ScoresOwner', 'action' => 'delete', 'mode' => 'deny']),
            ],
            'CustomEntrantJudge' => [
                new Permission(['resource' => 'EntriesOwner', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'ScoresOwner', 'action' => 'create', 'mode' => 'allow']),
            ],
            'CustomProgramManagerEntrant' => [
                new Permission(['resource' => 'Chapters', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'EntriesAll', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'Users', 'action' => 'update', 'mode' => 'allow']),
                new Permission(['resource' => 'Download', 'action' => 'view', 'mode' => 'allow']),
                new Permission(['resource' => 'EntriesOwner', 'action' => 'view', 'mode' => 'allow']),
            ],
        ];
    }

    public function testContexts(): void
    {
        $contexts = $this->contextMenuService->contexts();
        $contextNames = array_map(fn($context) => $context->name(), $contexts);

        $this->assertCount(4, $contexts);
        $this->assertContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
        $this->assertContains('guest', $contextNames);
    }

    public function testGetContextMenuItemsProgramManager(): void
    {
        $roles = collect([
            $this->createRole('ProgramManager'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsChapterManager(): void
    {
        $roles = collect([
            $this->createRole('ChapterManager', false, true),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsEntrant(): void
    {
        $roles = collect([
            $this->createRole('Entrant'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertNotContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsJudge(): void
    {
        $roles = collect([
            $this->createRole('Judge'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertNotContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsProgramManagerEntrant(): void
    {
        $roles = collect([
            $this->createRole('ProgramManager'),
            $this->createRole('Entrant'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsProgramManagerJudge(): void
    {
        $roles = collect([
            $this->createRole('ProgramManager'),
            $this->createRole('Judge'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsChapterManagerEntrant(): void
    {
        $roles = collect([
            $this->createRole('ChapterManager', false, true),
            $this->createRole('Entrant'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsChapterManagerJudge(): void
    {
        $roles = collect([
            $this->createRole('ChapterManager', false, true),
            $this->createRole('Judge'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsJudgeEnter(): void
    {
        $roles = collect([
            $this->createRole('Judge'),
            $this->createRole('Entrant'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertNotContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsCustomEntrantJudge(): void
    {
        $roles = collect([
            $this->createRole('CustomEntrantJudge'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertNotContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextMenuItemsCustomProgramManagerEntrant(): void
    {
        // This test should not have an "Enter" context even has an Entrant set of permission.
        // Because it will disregard other contexts if it's Manager.
        $roles = collect([
            $this->createRole('CustomProgramManagerEntrant'),
        ]);

        $contextNames = array_map(fn($context) => $context->name(), $this->contextMenuService->getContextMenuItems($roles));

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsProgramManager(): void
    {
        $permissions = $this->getPermissionSet('ProgramManager');

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsEntrant(): void
    {
        $permissions = $this->getPermissionSet('Entrant');

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertNotContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsJudge(): void
    {
        $permissions = $this->getPermissionSet('Judge');

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertNotContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsCustomEntrantJudge(): void
    {
        $permissions = $this->getPermissionSet('CustomEntrantJudge');

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertNotContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsCustomProgramManagerEntrant(): void
    {
        $permissions = $this->getPermissionSet('CustomProgramManagerEntrant');

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsEnterJudge(): void
    {
        $permissions = $this->getPermissionSet(['Entrant', 'Judge']);

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertNotContains('manage', $contextNames);
        $this->assertContains('enter', $contextNames);
        $this->assertContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsChapterManagerEnter(): void
    {
        $permissions = $this->getPermissionSet(['ChapterManager', 'Entrant']);

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    public function testGetContextItemsByPermissionsChapterManager(): void
    {
        $permissions = $this->getPermissionSet(['ChapterManager', 'Entrant']);

        $contextNames = array_map(
            fn($context) => $context->name(),
            $this->contextMenuService->getContextItemsByPermissions($permissions)
        );

        $this->assertContains('manage', $contextNames);
        $this->assertNotContains('enter', $contextNames);
        $this->assertNotContains('judge', $contextNames);
    }

    private function createRole($featureRole, $guest = false, bool $chapterLimited = false): object
    {
        $role = new class extends \stdClass {};
        $role->guest = $guest;
        $role->chapterLimited = $chapterLimited;
        $role->permissions = $this->getPermissionSet($featureRole);

        return $role;
    }

    private function getPermissionSet($featureRoles): Collection
    {
        $featureRoles = (array) $featureRoles;
        $permissions = collect();
        foreach ($featureRoles as $featureRole) {
            $permissions = $permissions->concat($this->permissionSet[$featureRole]);
        }

        return $permissions;
    }
}
