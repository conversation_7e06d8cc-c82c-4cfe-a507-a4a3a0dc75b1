<?php

use Platform\Menu\Context\Items\Enter;
use Platform\Menu\Context\Items\Judge;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Manager;
use Tests\Menu\Context\Menu\TestMainMenu;
use Tests\TestCase;

final class ManagerTest extends TestCase
{
    public function testGeneratedMenu(): void
    {
        $contexts = [
            new Manage,
            new Enter,
            new Judge,
        ];
        $manager = new Manager(new TestMainMenu, $contexts);
        $generatedMenu = $manager->generate();
        $menu = $generatedMenu->toArray();

        $this->assertEquals('test-main-menu', $menu['name']);
        $this->assertEquals('menu', $menu['type']);
        $this->assertCount(2, $menu['children']);

        $testMenu = $menu['children'][0];
        $this->assertEquals('test-menu', $testMenu['name']);
        $this->assertEquals('menu', $testMenu['type']);
        $this->assertCount(4, $testMenu['children']);

        $testCustomLink = $testMenu['children'][2];
        $this->assertEquals('test-custom-link', $testCustomLink['name']);
        $this->assertEquals('link', $testCustomLink['type']);
        $this->assertCount(1, $testCustomLink['contexts']);
        $this->assertContains('enter', $testCustomLink['contexts']);
        $this->assertNotContains('manage', $testCustomLink['contexts']);

        $testShortcutLink = $testMenu['children'][3];
        $this->assertEquals('test-shortcut-link', $testShortcutLink['name']);
        $this->assertEquals('shortcut_link', $testShortcutLink['type']);
        $this->assertCount(1, $testShortcutLink['contexts']);
        $this->assertContains('judge', $testShortcutLink['contexts']);
        $this->assertNotContains('manage', $testShortcutLink['contexts']);
        $this->assertNotContains('enter', $testShortcutLink['contexts']);

        $testSubMenu = $testMenu['children'][0];
        $this->assertEquals('test-sub-menu', $testSubMenu['name']);
        $this->assertEquals('menu', $testSubMenu['type']);
        $this->assertCount(2, $testSubMenu['children']);

        $testLinkC = $testSubMenu['children'][0];
        $this->assertEquals('test-link-c', $testLinkC['name']);
        $this->assertEquals('link', $testLinkC['type']);
        $this->assertCount(1, $testLinkC['contexts']);
        $this->assertContains('manage', $testLinkC['contexts']);
        $this->assertNotContains('judge', $testLinkC['contexts']);
        $this->assertNotContains('enter', $testLinkC['contexts']);

        $testLinkD = $testSubMenu['children'][1];
        $this->assertEquals('test-link-d', $testLinkD['name']);
        $this->assertEquals('link', $testLinkD['type']);
        $this->assertCount(1, $testLinkD['contexts']);
        $this->assertNotContains('manage', $testLinkD['contexts']);
        $this->assertNotContains('judge', $testLinkD['contexts']);
        $this->assertContains('enter', $testLinkD['contexts']);

        $testLinkA = $testMenu['children'][1];
        $this->assertEquals('test-link-a', $testLinkA['name']);
        $this->assertEquals('link', $testLinkA['type']);
        $this->assertCount(2, $testLinkA['contexts']);
        $this->assertContains('manage', $testLinkA['contexts']);
        $this->assertContains('judge', $testLinkA['contexts']);
        $this->assertNotContains('enter', $testLinkA['contexts']);

        $testMenuA = $menu['children'][1];
        $this->assertEquals('test-menu-a', $testMenuA['name']);
        $this->assertEquals('menu', $testMenuA['type']);
        $this->assertCount(1, $testMenuA['children']);

        $testLinkB = $testMenuA['children'][0];
        $this->assertEquals('test-link-b', $testLinkB['name']);
        $this->assertEquals('link', $testLinkB['type']);
        $this->assertCount(2, $testLinkB['contexts']);
        $this->assertContains('manage', $testLinkB['contexts']);
        $this->assertNotContains('judge', $testLinkB['contexts']);
        $this->assertContains('enter', $testLinkB['contexts']);
    }

    public function testGeneratedMenuHasJudgeOnly(): void
    {
        $contexts = [
            new Judge,
        ];
        $manager = new Manager(new TestMainMenu, $contexts);
        $generatedMenu = $manager->generate();
        $menu = $generatedMenu->toArray();

        $testMenu = $menu['children'][0];
        $this->assertEquals('test-menu', $testMenu['name']);
        $this->assertEquals('menu', $testMenu['type']);
        $this->assertCount(2, $testMenu['children']);

        $testLinkA = $testMenu['children'][0];
        $this->assertEquals('test-link-a', $testLinkA['name']);
        $this->assertEquals('link', $testLinkA['type']);
        $this->assertContains('manage', $testLinkA['contexts']);
    }

    public function testGeneratedMenuHasEnterOnly(): void
    {
        $contexts = [
            new Enter,
        ];
        $manager = new Manager(new TestMainMenu, $contexts);
        $generatedMenu = $manager->generate();
        $menu = $generatedMenu->toArray();

        $testMenu = $menu['children'][0];
        $testSubmenu = $testMenu['children'][0];

        $testLinkD = $testSubmenu['children'][0];
        $this->assertEquals('test-link-d', $testLinkD['name']);
        $this->assertEquals('link', $testLinkD['type']);
        $this->assertContains('enter', $testLinkD['contexts']);
    }

    public function testGeneratedMenuValidJson(): void
    {
        $contexts = [
            new Manage,
            new Enter,
            new Judge,
        ];
        $manager = new Manager(new TestMainMenu, $contexts);
        $generatedMenuJson = json_encode(collect($manager->generate()));
        $this->assertIsString($generatedMenuJson);
        $this->assertIsArray(json_decode($generatedMenuJson, true));
    }
}
