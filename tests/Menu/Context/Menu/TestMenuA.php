<?php

namespace Tests\Menu\Context\Menu;

use Platform\Menu\Context\Menu;
use Tests\Menu\Context\Menu\Links\TestLinkB;

class TestMenuA extends Menu
{
    public function menuItems(): array
    {
        return [
            new TestLinkB,
        ];
    }

    public function icon(): string
    {
        return '';
    }

    public function name(): string
    {
        return 'test-menu-a';
    }

    public function text(): string
    {
        return '';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
