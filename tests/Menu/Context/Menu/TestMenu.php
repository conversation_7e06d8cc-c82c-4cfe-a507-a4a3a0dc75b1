<?php

namespace Tests\Menu\Context\Menu;

use Platform\Menu\Context\CustomLink;
use Platform\Menu\Context\Items\Enter;
use Platform\Menu\Context\Menu;
use Tests\Menu\Context\Menu\Links\TestLinkA;
use Tests\Menu\Context\Menu\Links\TestShortCutLink;

class TestMenu extends Menu
{
    public function menuItems(): array
    {
        return [
            new TestSubMenu,
            new TestLinkA,
            new CustomLink(
                'test-custom-link',
                'Custom Link',
                '',
                '',
                '',
                true,
                [new Enter],
                [],
                0
            ),
            new TestShortCutLink,
        ];
    }

    public function icon(): string
    {
        return '';
    }

    public function name(): string
    {
        return 'test-menu';
    }

    public function text(): string
    {
        return '';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
