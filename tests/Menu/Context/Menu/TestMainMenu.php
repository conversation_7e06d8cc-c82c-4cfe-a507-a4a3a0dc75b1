<?php

namespace Tests\Menu\Context\Menu;

use Platform\Menu\Context\Menu;

class TestMainMenu extends Menu
{
    public function menuItems(): array
    {
        return [
            new TestMenu,
            new TestMenuA,
        ];
    }

    public function icon(): string
    {
        return '';
    }

    public function name(): string
    {
        return 'test-main-menu';
    }

    public function text(): string
    {
        return '';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
