<?php

namespace Tests\Menu\Context\Menu;

use Platform\Menu\Context\Menu;
use Tests\Menu\Context\Menu\Links\TestLinkC;
use Tests\Menu\Context\Menu\Links\TestLinkD;

class TestSubMenu extends Menu
{
    public function menuItems(): array
    {
        return [
            new TestLinkC,
            new TestLinkD,
        ];
    }

    public function icon(): string
    {
        return '';
    }

    public function name(): string
    {
        return 'test-sub-menu';
    }

    public function text(): string
    {
        return '';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
