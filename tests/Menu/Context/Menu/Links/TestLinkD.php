<?php

namespace Tests\Menu\Context\Menu\Links;

use Platform\Menu\Context\Items\Enter;
use Platform\Menu\Context\Link;

class TestLinkD extends Link
{
    public function name(): string
    {
        return 'test-link-d';
    }

    public function text(): string
    {
        return 'Test Link D';
    }

    public function link(): string
    {
        return 'route/d';
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return 'test';
    }

    public function applies(): bool
    {
        return true;
    }

    public function contexts(): array
    {
        return [new Enter];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
