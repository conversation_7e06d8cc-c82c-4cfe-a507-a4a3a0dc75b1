<?php

namespace Tests\Menu\Context\Menu\Links;

use Platform\Menu\Context\Items\Judge;
use Platform\Menu\Context\ShortcutLink;

class TestShortCutLink extends ShortcutLink
{
    public function name(): string
    {
        return 'test-shortcut-link';
    }

    public function text(): string
    {
        return 'Test shortcut link';
    }

    public function link(): string
    {
        return '';
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return 'test';
    }

    public function applies(): bool
    {
        return true;
    }

    public function contexts(): array
    {
        return [new Judge];
    }

    public function extras(): array
    {
        return ['class' => 'className'];
    }

    public function shortcutMenuName(): string
    {
        return 'goto-settings-menu';
    }

    public function position(): int
    {
        return 0;
    }
}
