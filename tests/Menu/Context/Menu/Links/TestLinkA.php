<?php

namespace Tests\Menu\Context\Menu\Links;

use Platform\Menu\Context\Items\Judge;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class TestLinkA extends Link
{
    public function name(): string
    {
        return 'test-link-a';
    }

    public function text(): string
    {
        return 'Test Link A';
    }

    public function link(): string
    {
        return 'route/a';
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return 'test';
    }

    public function applies(): bool
    {
        return true;
    }

    public function contexts(): array
    {
        return [new Manage, new Judge];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
