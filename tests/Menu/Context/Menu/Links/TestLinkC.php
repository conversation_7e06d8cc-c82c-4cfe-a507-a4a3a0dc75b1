<?php

namespace Tests\Menu\Context\Menu\Links;

use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class TestLinkC extends Link
{
    public function name(): string
    {
        return 'test-link-c';
    }

    public function text(): string
    {
        return 'Test Link C';
    }

    public function link(): string
    {
        return 'route/c';
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return 'test';
    }

    public function applies(): bool
    {
        return true;
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
