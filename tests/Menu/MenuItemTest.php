<?php

namespace Tests\Menu;

use Tests\TestCase;

final class MenuItemTest extends TestCase
{
    public function test_text_assignment_is_correct(): void
    {
        $menuItem = new MenuItemStub('item-name', 'text');

        $this->assertEquals('item-name', $menuItem->name);
        $this->assertEquals('text', $menuItem->text);
    }

    public function test_attributes_are_formatted(): void
    {
        $menuItem = new MenuItemStub('item-name', 'text', ['class' => 'a class', 'id' => 'name']);

        $this->assertStringContainsString('class="a class"', $attributes = $menuItem->attributes(['extra' => 'value']));
        $this->assertStringContainsString('id="name"', $attributes);
        $this->assertStringContainsString('extra="value"', $attributes);
    }
}
