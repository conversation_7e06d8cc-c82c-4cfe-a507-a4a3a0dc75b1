<?php

namespace Tests\Events;

use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Mockery as m;
use Platform\Authorisation\Consumer;
use Platform\Events\UserBroadcaster;
use Tests\TestCase;

final class UserBroadcasterTest extends TestCase
{
    public function test_it_returns_empty_channel_in_broadcast_on(): void
    {
        $consumer = m::mock(Consumer::class);
        $consumer->shouldReceive('globalId')->andReturn(null);

        $broadcast = new BroadcastClass($consumer);

        $this->assertEquals(['dev-null'], $broadcast->broadcastOn());
    }

    public function test_it_returns_non_empty_channel_in_broadcast_on(): void
    {
        $consumer = m::mock(Consumer::class);
        $consumer->shouldReceive('globalId')->andReturn('1234');
        $consumer->shouldReceive('globalAccountId')->andReturn('1234');

        $broadcast = new BroadcastClass($consumer);

        $this->assertNotEquals([], $broadcast->broadcastOn());
    }
}

class BroadcastClass implements ShouldBroadcastNow
{
    use UserBroadcaster;

    /** @var Consumer */
    private $consumer;

    public function __construct(Consumer $consumer)
    {
        $this->consumer = $consumer;
    }

    protected function consumer()
    {
        return $this->consumer;
    }
}
