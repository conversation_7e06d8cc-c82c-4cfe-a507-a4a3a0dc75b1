<?php

namespace Tests\Events;

use Mockery as m;
use Platform\Database\Eloquent\Model;
use Platform\Events\ModelWasCopied;
use Tests\TestCase;

final class ModelWasCopiedTest extends TestCase
{
    public function test_event_setup(): void
    {
        $original = m::mock(Model::class);
        $copy = m::mock(Model::class);

        $event = new ModelWasCopied($original, $copy);

        $this->assertEquals($original, $event->original);
        $this->assertEquals($copy, $event->copy);
    }
}
