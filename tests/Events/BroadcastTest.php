<?php

namespace Tests\Events;

use Mockery as m;
use Platform\Authorisation\Consumer;
use Platform\Events\Facades\Broadcast;
use Tests\TestCase;

final class BroadcastTest extends TestCase
{
    public function test_it_returns_a_null_channel_when_user_is_invalid(): void
    {
        $consumer = m::mock(Consumer::class);
        $consumer->shouldReceive('globalId')->andReturn(null);

        $this->assertNull(Broadcast::consumerChannel($consumer));
    }

    public function test_it_return_the_appropriate_user_channel(): void
    {
        $user = new \stdClass;
        $user->globalId = 'globalId';

        $consumer = m::mock(Consumer::class);
        $consumer->shouldReceive('globalId')->andReturn($user->globalId);
        $consumer->shouldReceive('globalAccountId')->andReturn(1);

        $this->assertSame('private-consumer-globalId-1', Broadcast::consumerChannel($consumer));
    }

    public function test_simple_message_return_value(): void
    {
        $this->assertIsString(Broadcast::simpleMessage());
    }

    public function test_global_channel_return_value(): void
    {
        $this->assertSame('private-global-globalId', Broadcast::globalChannel('globalId'));
    }
}
