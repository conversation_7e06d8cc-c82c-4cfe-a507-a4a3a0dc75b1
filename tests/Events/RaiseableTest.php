<?php

namespace Tests\Events;

use Platform\Events\Raiseable;
use Tests\TestCase;

final class RaiseableTest extends TestCase
{
    public function test_it_can_raise_events(): void
    {
        $model = new RaiseableModel;
        $model->raise(new EventStub);

        $this->assertCount(1, $model->releaseEvents());
    }

    public function test_it_can_raise_all_events(): void
    {
        $model = new RaiseableModel;
        $model->raiseAll([new EventStub, new EventStub]);

        $this->assertCount(2, $model->releaseEvents());
    }

    public function test_it_returns_all_pending_events(): void
    {
        $model = new RaiseableModel;
        $model->raiseAll([new EventStub, new EventStub]);

        $this->assertCount(2, $model->pendingEvents());
        $this->assertCount(2, $model->releaseEvents());
    }

    public function test_can_avoid_raising_repeated_events(): void
    {
        $model = new RaiseableModel;
        $model->raiseUnique(new EventStub);
        $model->raiseUnique(new EventStub);

        $this->assertCount(1, $model->pendingEvents());
        $this->assertCount(1, $model->releaseEvents());
    }
}

class RaiseableModel
{
    use Raiseable;
}
