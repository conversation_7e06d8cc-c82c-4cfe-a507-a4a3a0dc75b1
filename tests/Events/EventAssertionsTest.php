<?php

namespace Tests\Events;

use PHPUnit\Framework\ExpectationFailedException;
use Platform\Events\Raiseable;
use Platform\Test\EventAssertions;
use Tests\TestCase;

final class EventAssertionsTest extends TestCase
{
    use EventAssertions;

    public function test_it_asserts_raised_event_with_callback(): void
    {
        $object = new EventAssertionStub;
        $object->methodThatRaisesEvents();

        $this->assertRaised($object, FakedEventA::class);
        $this->assertNotRaised($object, FakedEventB::class);

        // Assert that event being raised + that given callback is truthful
        $this->assertRaised($object, FakedEventC::class, function ($event) {
            return $event->hello === 'world' && $event->foo === 'bar';
        });

        // Assert a failure case
        try {
            $this->assertRaised($object, FakedEventD::class, function ($ev) {
                return $ev->hello === 'this is not true' && $ev->foo === 'so false';
            });
            $this->fail();
        }

        // Assertion exception was thrown, just as <PERSON> <PERSON><PERSON> predicted
        catch (ExpectationFailedException $ex) {
            $this->assertRaised($object, FakedEventD::class);
        }
    }
}

class EventAssertionStub
{
    use Raiseable;

    public function methodThatRaisesEvents()
    {
        $this->raise(new FakedEventA());
        $this->raise(new FakedEventD('rubble', 'rubble'));
        $this->raise(new FakedEventC('world', 'bar'));
    }
}

class FakedEventA
{
}

class FakedEventB
{
}

class FakedEventC
{
    public $hello;

    public $foo;

    /**
     * FakedEventC constructor.
     */
    public function __construct(string $hello, string $foo)
    {
        $this->hello = $hello;
        $this->foo = $foo;
    }
}

class FakedEventD extends FakedEventC
{
}
