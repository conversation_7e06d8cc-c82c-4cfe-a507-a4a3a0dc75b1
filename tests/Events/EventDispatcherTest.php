<?php

namespace Tests\Events;

use Platform\Events\EventDispatcher;
use Tests\TestCase;

final class EventDispatcherTest extends TestCase
{
    public function test_it_can_fire_events(): void
    {
        $dispatcher = new EventDispatcherStub;
        $dispatcher->dispatchAll([new EventStub, new EventStub]);

        $this->assertCount(2, $dispatcher->events);
    }
}

class EventDispatcherStub
{
    use EventDispatcher {
        logEvent as originalLogEvent;
        fireEvent as originalFireEvent;
    }

    public $events = [];

    protected function logEvent($event)
    {
        $this->events[] = $event;

        $this->originalLogEvent($event);
    }

    protected function fireEvent($event)
    {
        $this->originalFireEvent($event);
    }
}
