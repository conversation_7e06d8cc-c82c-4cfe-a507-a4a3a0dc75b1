<?php

namespace Tests\Localisation;

use Platform\Localisation\MessageSelector;
use Tests\TestCase;

final class MessageSelectorTest extends TestCase
{
    public function testReturnMessageIfCustomLocaleNameIsProvided(): void
    {
        $selector = new MessageSelector();

        $message = 'There is one apple|There are two apples';

        $this->assertEquals('There is one apple', $selector->choose($message, 1, 'en_GB'));
        $this->assertEquals('There are two apples', $selector->choose($message, 2, 'en_GB'));
    }
}
