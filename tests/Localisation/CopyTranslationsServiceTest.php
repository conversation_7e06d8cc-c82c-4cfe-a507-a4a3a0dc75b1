<?php

namespace Tests\Localisation;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Translation\Translator;
use Mockery as m;
use Platform\Localisation\CopyTranslationsService;
use Tectonic\LaravelLocalisation\Database\TranslationService;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tectonic\Localisation\Contracts\Translatable;
use Tests\TestCase;

final class CopyTranslationsServiceTest extends TestCase
{
    public function test_it_can_copy_translations_between_objects(): void
    {
        $model1 = new Model;
        $model1->addTranslation('en_GB', 'title', 'value');
        $model1->addTranslation('en_GB', 'name', 'something else');

        $model2 = new Model;

        $translations = m::spy(TranslationService::class);

        $lang = m::mock(Translator::class);
        $lang->shouldReceive('get')->andReturn(' COPY');

        $engine = m::mock(Engine::class);
        $engine->shouldReceive('translate')->andReturn($model1);

        $service = new CopyTranslationsService($translations, $lang, $engine, $append = true);

        $service->copy($model1, $model2);

        $translations->shouldHaveReceived('sync')->once()->with($model2, ['title' => ['en_GB' => 'value COPY'], 'name' => ['en_GB' => 'something else']]);
    }

    public function test_it_can_copy_translations_from_resource(): void
    {
        $model1 = new Model;
        $model1->addTranslation('en_GB', 'title', $model1Name = 'VIP Judging 2022');
        $model1->addTranslation('en_GB', 'name', 'something else');
        $model1->addTranslation('fr_FR', 'title', 'Jugement VIP 2022');
        $model1->addTranslation('fr_FR', 'name', 'autre chose');

        $model2 = new Model;

        $translations = m::spy(TranslationService::class);

        $seasonName = 'Universal Awards';
        $modify = $model1->getTranslatableCopyAppendFieldsFromResource()['title']['modify'];

        $lang = m::mock(Translator::class);
        $lang->shouldReceive('get')->andReturn(" (copied from {$seasonName})");

        $engine = m::mock(Engine::class);
        $engine->shouldReceive('translate')->andReturn($model1);

        $service = new CopyTranslationsService($translations, $lang, $engine);

        $service->copy($model1, $model2, true, 'season', [
            'en_GB' => [
                'name' => $seasonName,
            ],
        ]);

        $translations->shouldHaveReceived('sync')->once()->with($model2, ['title' => ['en_GB' => "{$modify($model1Name)} (copied from {$seasonName})", 'fr_FR' => 'Jugement VIP'], 'name' => ['en_GB' => 'something else', 'fr_FR' => 'autre chose']]);
    }
}

class Model implements Translatable
{
    public $translated = [];

    /**
     * Returns the id for the record implementing the interface.
     *
     * @return int
     */
    public function getId()
    {
        return 1;
    }

    /**
     * Returns an array of field names that can be translated.
     *
     * @return array
     */
    public function getTranslatableFields()
    {
        return ['title', 'name', 'description'];
    }

    /**
     * Returns the name of the resource that this object represents.
     *
     * @return string
     */
    public function getResourceName()
    {
        return 'resource';
    }

    /**
     * Add a translation to the model or entity.
     *
     * @param  string  $language
     * @param  string  $key
     * @param  mixed  $value
     */
    public function addTranslation($language, $key, $value)
    {
        Arr::set($this->translated, "$language.$key", $value);
    }

    public function getTranslatableCopyAppendFields()
    {
        return ['title' => 'lang.title'];
    }

    public function getTranslatableCopyAppendFieldsFromResource(): array
    {
        return [
            'title' => [
                'key' => 'lang.title',
                'resource' => 'season',
                'resourceKey' => 'name',
                'modify' => fn($value) => Str::of(preg_replace('/\b20\d{2}\b\+?/', '', $value))->trim(),
            ],
        ];
    }
}
