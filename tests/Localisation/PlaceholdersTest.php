<?php

namespace Tests\Localisation;

use Platform\Localisation\Placeholders;
use Tests\TestCase;

final class PlaceholdersTest extends TestCase
{
    public function testToCurly(): void
    {
        $this->assertEquals(
            'Three %{rings} for the Elven %{king_plural} under the sky,',
            Placeholders::toCurly('Three :rings for the Elven :king+ under the sky,')
        );
    }

    public function testFromCurly(): void
    {
        $this->assertEquals(
            'Seven for the :dwarf lords in their :hall+ of stone,',
            Placeholders::fromCurly('Seven for the %{dwarf} lords in their %{hall_plural} of stone,')
        );
    }

    public function testAllToCurly(): void
    {
        $this->assertEquals(
            [
                'Nine for Mortal Men %{doomed} to die,',
                'One for the Dark Lord on his dark %{throne},',
                'In the Land of Mordor where the %{shadow_plural} lie,',
                'One ring to rule them all, one %{ring} to find them,',
                'One ring to bring them all and in the %{darkness} bind them',
                'In the Land of Mordor where the %{shadow_plural} lie.',
            ],
            Placeholders::allTo<PERSON>urly([
                'Nine for Mortal Men :doomed to die,',
                'One for the Dark Lord on his dark :throne,',
                'In the Land of Mordor where the :shadow+ lie,',
                'One ring to rule them all, one :ring to find them,',
                'One ring to bring them all and in the :darkness bind them',
                'In the Land of Mordor where the :shadow+ lie.',
            ])
        );
    }
}
