<?php

namespace Tests\Localisation;

use Platform\Localisation\Translation;
use Tests\TestCase;

final class TranslationTest extends TestCase
{
    public function test_it_trims_values(): void
    {
        $translation = new Translation;
        $translation->value = '   yo   ';

        $this->assertSame('yo', $translation->value);
    }

    public function test_it_handles_arrays(): void
    {
        $translation = new Translation;
        $translation->value = ['1', '2'];

        $this->assertSame(json_encode(['1', '2']), $translation->value);
    }
}
