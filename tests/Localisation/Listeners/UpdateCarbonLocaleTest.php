<?php

namespace Tests\Localisation\Listeners;

use Carbon\Carbon;
use Illuminate\Foundation\Events\LocaleUpdated;
use Illuminate\Support\Facades\Config;
use Platform\Localisation\Listeners\UpdateCarbonLocale;
use Tests\TestCase;

final class UpdateCarbonLocaleTest extends TestCase
{
    public function test_it_can_set_the_locale_correctly(): void
    {
        Config::set('lang.config.ar.carbon', 'ar');

        (new UpdateCarbonLocale)->handle(new LocaleUpdated('ar'));

        $this->assertSame('ar', Carbon::getLocale());
    }
}
