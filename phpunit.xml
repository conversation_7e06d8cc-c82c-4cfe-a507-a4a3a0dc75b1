<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" colors="true" beStrictAboutTestsThatDoNotTestAnything="false" processIsolation="false" stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.4/phpunit.xsd" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <coverage/>
  <testsuites>
    <testsuite name="all">
      <directory suffix="Test.php">./tests/</directory>
    </testsuite>
  </testsuites>
  <php>
    <env name="APP_ENV" value="testing"/>
    <env name="ENABLE_MIDDLEWARE" value="false"/>
    <env name="CACHE_DRIVER" value="array"/>
    <env name="WHITE_LABEL_DOMAINS" value="awardsplatform.com,grantplatform.com,staffexcellence.com"/>
    <env name="DB_DATABASE" value=":memory:"/>
    <env name="DB_DRIVER" value="sqlite"/>
    <env name="DB_CONNECTION" value="sqlite"/>
  </php>
  <source>
    <include>
      <directory suffix=".php">./src/</directory>
    </include>
    <exclude>
      <file>./src/Authorisation/Facade.php</file>
      <file>./src/Html/macros.php</file>
    </exclude>
  </source>
</phpunit>
