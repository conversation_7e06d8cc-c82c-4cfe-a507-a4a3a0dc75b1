<?xml version="1.0" encoding="UTF-8"?>
<project name="platform" default="build">

  <property environment="env"/>
  <property name="composer.path" value="composer" />
  <available property="composer.installed" file="${composer.path}" />

  <target name="build"
    depends="clean,prepare,phpunit,php-security"/>

  <target name="clean" description="Cleanup build artifacts">
    <delete dir="${basedir}/build/api"/>
    <delete dir="${basedir}/build/api"/>
    <delete dir="${basedir}/build/code-browser"/>
    <delete dir="${basedir}/build/coverage"/>
    <delete dir="${basedir}/build/logs"/>
    <delete dir="${basedir}/build/behat"/>
    <delete file="${basedir}/bootstrap/cache/compiled.php"/>
    <delete file="${basedir}/bootstrap/cache/services.json"/>
    <delete file="${basedir}/bootstrap/cache/services.php"/>
    <delete file="${basedir}/bootstrap/cache/config.php"/>
  </target>

  <target name="prepare" depends="composer-deps" description="Prepare for build">
    <mkdir dir="${basedir}/build/api"/>
    <mkdir dir="${basedir}/build/code-browser"/>
    <mkdir dir="${basedir}/build/coverage"/>
    <mkdir dir="${basedir}/build/logs"/>
    <mkdir dir="${basedir}/build/phpdox"/>
    <mkdir dir="${basedir}/build/behat"/>
  </target>

  <target name="phpcs-ci" description="Find coding standard violations using PHP_CodeSniffer creating a log file for the continuous integration server">
    <exec executable="./vendor/bin/phpcs" output="/dev/null">
      <arg value="--report=checkstyle" />
      <arg value="--report-file=${basedir}/build/logs/checkstyle.xml" />
      <arg value="--standard=PSR2" />
      <arg path="${basedir}/app" />
    </exec>
  </target>

  <target name="phpcpd" description="Find duplicate code using PHPCPD">
    <exec executable="./vendor/bin/phpcpd">
      <arg value="--log-pmd" />
      <arg value="${basedir}/build/logs/pmd-cpd.xml" />
      <arg path="${basedir}/app" />
    </exec>
  </target>

  <target name="phpunit" description="Run unit tests with PHPUnit">
    <exec executable="./vendor/bin/phpunit" failonerror="true">
      <arg value="--colors=always" />
      <arg value="-c" />
      <arg path="phpunit.xml" />
      <env key="APP_ENV" value="testing" />
    </exec>
  </target>

  <target name="phpunit-phpdbg" description="Run unit tests with PHPUnit">
    <exec executable="phpdbg" failonerror="true">
      <arg value="-rr" />
      <arg path="./vendor/bin/phpunit" />
      <arg value="--colors=always" />
      <arg value="-c" />
      <arg path="phpunit.xml" />
      <arg value="--log-junit" />
      <arg path="build/logs/junit.xml" />
      <env key="APP_ENV" value="testing" />
    </exec>
  </target>

  <target name="behat" description="Run behavioural tests with Behat">
    <exec executable="php" failonerror="true">
      <arg value="artisan" />
      <arg value="view:clear" />
      <env key="APP_ENV" value="testing" />
      <env key="DB_DRIVER" value="${DB_DRIVER}" />
      <env key="DB_DATABASE" value="${DB_DATABASE}" />
      <env key="DB_USERNAME" value="${DB_USERNAME}" />
      <env key="DB_HOST" value="${DB_HOST}" />
      <env key="DB_PASSWORD" value="${DB_PASSWORD}" />
      <env key="WHITE_LABEL_DOMAINS" value="awardforce.app" />
      <env key="PROVISIONING_DOMAIN" value="provisioning.awardforce.app" />
    </exec>
    <exec executable="php" failonerror="true">
      <arg value="./vendor/bin/behat" />
      <arg value="--colors" />
      <env key="APP_ENV" value="testing" />
    </exec>
  </target>

  <target name="phpcb" description="Aggregate tool output with PHP_CodeBrowser">
    <exec executable="./vendor/bin/phpcb">
      <arg value="--log" />
      <arg path="${basedir}/build/logs" />
      <arg value="--source" />
      <arg path="${basedir}/app" />
      <arg value="--output" />
      <arg path="${basedir}/build/code-browser" />
    </exec>
  </target>

  <target name="langmissing" description="Check for missing language strings">
    <exec executable="php" failonerror="true">
      <arg value="artisan" />
      <arg value="lang:missing" />
      <env key="APP_ENV" value="testing" />
      <env key="DB_DRIVER" value="${DB_DRIVER}" />
      <env key="DB_DATABASE" value="${DB_DATABASE}" />
      <env key="DB_USERNAME" value="${DB_USERNAME}" />
      <env key="DB_HOST" value="${DB_HOST}" />
      <env key="DB_PASSWORD" value="${DB_PASSWORD}" />
    </exec>
  </target>

  <target name="composer-install" unless="composer.installed">
    <get src="http://getcomposer.org/composer.phar" dest="${composer.path}" />
    <chmod file="${composer.path}" perm="775" />
  </target>

  <target name="composer-vendor-check">
    <condition property="composer.vendorexist">
      <available file="${basedir}/vendor" type="dir" />
    </condition>
  </target>

  <target name="composer-deps" description="Updating dependencies">
    <echo>Install dependencies</echo>
    <exec executable="${composer.path}" failonerror="true">
      <arg value="update" />
      <arg value="--no-scripts" />
      <arg value="--no-suggest" />
      <arg value="--no-progress" />
      <arg value="--no-interaction" />
    </exec>
  </target>

  <target name="php-security" description="Check PHP security">
    <exec executable="local-php-security-checker" failonerror="true">
      <arg value="security:check" />
    </exec>
  </target>
</project>
