<?php

namespace Platform\Strings;

class HtmlStripTags
{
    public static function stripValidTags(?string $string): ?string
    {
        if (empty($string)) {
            return $string;
        }

        $string = self::stripTagsWithoutAttributes($string);

        return self::stripTagsWithAttributes($string);
    }

    /**
     * Check for simple tags like:
     * - <DIV >
     * - </bold>
     * - <img src>  (also match attributes without=)
     */
    private static function stripTagsWithoutAttributes(string $string = ''): string
    {
        /*
         * Replaces valid opening and closing html tags.
         *
         * Examples:
         * "</sCrIpT>"               => "sCrIpT"
         * "<div id="foo">abc</div>" => "div"
         *
         * Note: this regex will detect tags with empty attribute names (thus the [\w\s]* part) e.g.
         * "<img src onerror>"
         */
        return preg_replace("/<\/?(".static::validTags().")(\s[^<>]*)?>/i", '', $string);
    }

    /**
     * Replaces tags with attributes like:
     * - <i data-attr="foo">
     * - <input type=text>
     * - <img src>
     * - <img src="image.jpg" focus>
     *
     * This is needed to detect tag like: <img src="http://a.com/a.jpg" alt="foo" />
     */
    private static function stripTagsWithAttributes(?string $string): string
    {
        return preg_replace(
            '/<(?:'.static::validTags().')\s+(?:[a-zA-Z-]+(?:\s*=\s*(?:"[^"]*"|\'[^\']*\'|[^\s>]*))?\s*)+>/',
            '',
            $string
        );
    }

    private static function validTags(): string
    {
        return collect(ValidTags::get())->implode('|');
    }
}
