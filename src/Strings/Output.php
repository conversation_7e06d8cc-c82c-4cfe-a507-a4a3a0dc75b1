<?php

namespace Platform\Strings;

use Facades\Platform\Html\Markdown;
use Illuminate\Contracts\Support\Htmlable;
use Stringable;

final class Output
{
    public function text(string|Stringable|null $content): string
    {
        return $this->escapeBrackets($content ?? '');
    }

    public function html(string|Htmlable|Stringable|null $content): string
    {
        if (is_null($content)) {
            return '';
        }

        if ($this->htmlable($content)) {
            return $this->escapeBrackets($content->toHtml() ?: '');
        }

        // This might seem odd, but due to legacy data, some of our data has markdown, other data is in html,
        // so we have to first convert from markdown to html, then do our sanitisation.
        return $this->escapeBrackets(
            app(Sanitiser::class)->filter($this->fromMarkdown((string) $content))
        );
    }

    public function attribute(?string $content): string
    {
        return htmlspecialchars($content, ENT_COMPAT);
    }

    private function fromMarkdown(?string $content): string
    {
        $content = trim($content);

        // Content inside <af-field> is already HTML, so we exclude it when checking if the rest is HTML or Markdown.
        $filteredContent = preg_replace('/<af-field>.*?<\/af-field>/si', '', $content);

        if (preg_match('/<[^<]+>/', $filteredContent) >= 1) {
            return $content;
        }

        return Markdown::parse($content);
    }

    /**
     * Due to our unique use of Vue and Blade, this can result in string output in our blade templates being
     * read and transpiled vue, allowing for double-bracket code injection. Therefore, all output should protect
     * against this potential vulnerability. Note, this is temporary, when we migrate to vue this can be removed
     * as the vulnerability will no longer be present. //- Kirk
     */
    public function escapeBrackets(?string $input): string
    {
        $output = $input;
        // Keep replacing until no more double brackets remain
        while (str_contains($output, '{{') || str_contains($output, '}}')) {
            $output = str_replace(['{{', '}}'], ['{ {', '} }'], $output);
        }

        return $output;
    }

    private function htmlable(mixed $content): bool
    {
        return $content instanceof Htmlable;
    }
}
