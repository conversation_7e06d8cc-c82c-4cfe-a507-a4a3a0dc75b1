<?php

namespace Platform\Strings;

use Symfony\Component\HtmlSanitizer\HtmlSanitizer;

final readonly class HtmlSanitiser implements Sanitiser
{
    public function __construct(private HtmlSanitizer $sanitiser) {}

    public function clean(string $input): string
    {
        return HtmlStripTags::stripValidTags($input);
    }

    /**
     * For this implementation, we do not require the tagsAllowed array, as it's configured in
     * the LibraryServiceProvider. However, this may change in the future, so we'll keep the parameter available
     * and implement if necessary, perhaps as part of a separate Sanitiser implementation.
     */
    public function filter(string $input, ?array $tagsAllowed = []): string
    {
        return $this->sanitiser->sanitize($input);
    }
}
