<?php

namespace Platform\Database;

use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Model;
use Platform\Search\SearchFilterCollection;

/**
 * Nearly all repositories will require the following methods. This is to ensure we're dealing with a
 * common interface for all our repositories. Each repository should implement its own interface that extends
 * this, and if there are any changes in the requirements, they can define them there.
 */
interface Repository
{
    /**
     * Delete a specific resource. Returns the resource that was deleted.
     *
     * @param  object  $resource
     * @param  bool  $permanent
     * @return resource
     */
    public function delete($resource, $permanent = false);

    /**
     * Delete all resources passed to the method.
     *
     * @return mixed
     */
    public function deleteAll(...$resources);

    /**
     * Returns the number of models in the repository.
     *
     * @return int
     */
    public function countAll();

    /**
     * Returns a collection of all records for this repository and the models or entities it respresents.
     *
     * @return Collection
     */
    public function getAll();

    /**
     * Create a resource based on the data provided.
     *
     * @param  array  $data  Optional
     * @return resource
     *
     * @deprecated
     */
    public function getNew(array $data = []);

    /**
     * Acts as a generic method for retrieving a record by a given field/value pair.
     *
     * @return mixed
     */
    public function getBy($field, $value);

    /**
     * Returns a single record based on id.
     *
     * @return null
     */
    public function getById($id);

    /**
     * Similar to getBy, but returns only a single record, rather than a collection of fields.
     *
     * @return mixed
     */
    public function getOneBy($field, $value);

    /**
     * Acts as a generic method for requiring a record by a given field/value pair.
     *
     * @return mixed
     */
    public function requireBy($field, $value);

    /**
     * @param  array  $data
     * @return resource
     */
    public function update($resource, $data = []);

    /**
     * Saves the provided resource.
     *
     * @return mixed
     */
    public function save($resource);

    /**
     * Prepares a query using the given filters. Does not get results.
     *
     * @return mixed
     */
    public function applyFilters(SearchFilterCollection $filters);

    /**
     * Retrieve a collection of results based on the search filters provided.
     *
     * @param  bool  $paginate
     * @return mixed
     */
    public function getByFilters(SearchFilterCollection $filters, $paginate = true);

    /**
     * Execute a search with a custom paginator setup. This allows us implementors to setup their own pagination
     * objects for complex queries, resulting in much faster aggregate counts against the database.
     *
     * @return mixed
     */
    public function searchWithPagination(SearchFilterCollection $filters, SearchFilterCollection $paginationFilters);

    /**
     * Retrieve a collection of resource ID's based on the search filters provided.
     *
     * @return mixed
     */
    public function getIdsByFilters(SearchFilterCollection $filters);

    /**
     * Return a model object based on its url slug.
     *
     * @param  string  $slug
     * @return Model
     */
    public function getBySlug($slug);

    /**
     * Save 1-n resources.
     *
     * @param  array  $resources
     * @return mixed
     */
    public function saveAll(...$resources);

    /**
     * Similar signature to saveAll, except here you can pass a collection or array of resources.
     *
     * @param  array|Collection  $resources
     * @return mixed
     */
    public function saveMany($resources);

    /**
     * Creates copies of the specified models.
     *
     * @return \Illuminate\Support\Collection
     */
    public function copyByIds(array $ids);

    /**
     * Returns a collection of models given an array of IDs.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getByIds(array $ids);

    /**
     * Returns an array of all of the existing model IDs.
     *
     * @return array
     */
    public function getIds();

    /**
     * Returns an array of all of the trashed existing model IDs.
     *
     * @return array
     */
    public function getTrashedIds();

    /**
     * Restores a number of resources based on their ids.
     *
     * @param  array  $resources
     * @return mixed
     */
    public function restore($resources);

    /**
     * Same as getByIds, but returns all trashed/deleted resources.
     *
     * @return mixed
     */
    public function getTrashedByIds(array $ids);

    /**
     * Returns the list of IDs filtered by only those that are actually valid.
     * Useful for filtering user input into only the legitimate IDs without needing validation or exception handling.
     *
     * @param  int  $seasonId
     * @return \Illuminate\Support\Collection
     */
    public function filterIds(array $ids, $seasonId = null);

    /**
     * Calculates the appropriate Local ID for the specified entry.
     *
     * @param  object  $model
     * @return int
     */
    public function calculateLocalId($model);

    /**
     * Deletes all of the records assigned to the specified season.
     * Note, this will only work for records that are linked via season.
     *
     * @param  int  $seasonId
     */
    public function deleteAllFromSeason($seasonId);

    /**
     * Returns all matching records, with trashed.
     *
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getByIdsWithTrashed(array $ids);

    /**
     * Returns true if any records exist.
     * This should be faster than `COUNT` as no data needs to be processed,
     * the database should return as soon as the first record is found.
     */
    public function hasAny(): bool;

    /**
     * Executes the callback with the model locked for editing for the duration of the callback,
     * and then returns the model for any further work (such as dispatching events).
     *
     * @throws \Throwable
     */
    public function getWithLock(int $id, callable $callback): Model;

    /**
     * Checks if all of the $ids are valid ids in the database.
     *
     * @param  array|int[]  $ids
     */
    public function hasAllIds(array $ids): bool;

    public function getAllWithTrashed(): Collection;

    /*
     * Permanently delete all from season
     */
    public function permanentlyDeleteAllFromSeason(?int $seasonId): void;

    /*
     * Permanently delete all
     */
    public function permanentlyDeleteAll(): void;

    public function transaction(\Closure $callback, int $attempts = 1): mixed;
}
