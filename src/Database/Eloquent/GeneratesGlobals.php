<?php

namespace Platform\Database\Eloquent;

use Illuminate\Support\Str;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

trait GeneratesGlobals
{
    public static function bootGeneratesGlobals()
    {
        static::creating(function (Model $model) {
            $model->id = Str::orderedUuid();
        });
    }

    public function setIdAttribute(UuidInterface $id)
    {
        $this->attributes['id'] = $id->toString();
    }

    public function getIdAttribute($id)
    {
        return Uuid::fromString($id);
    }
}
