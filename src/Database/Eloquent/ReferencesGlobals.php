<?php

namespace Platform\Database\Eloquent;

use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

trait ReferencesGlobals
{
    public function __set($key, $value)
    {
        if (in_array(Str::snake($key), $this->uuids())) {
            if (! ($value instanceof UuidInterface)) {
                throw new \InvalidArgumentException('UuidInterface expected.');
            }

            $this->attributes[Str::snake($key)] = $value->toString();
        } else {
            parent::__set($key, $value);
        }
    }

    public function __get($key)
    {
        if (in_array(Str::snake($key), $this->uuids())) {
            $value = Arr::get($this->attributes, Str::snake($key));

            return $value ? Uuid::fromString($value) : null;
        }

        return parent::__get($key);
    }

    /**
     * Return the columns that have a uuid.
     */
    abstract protected function uuids(): array;
}
