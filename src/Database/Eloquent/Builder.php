<?php
namespace Platform\Database\Eloquent;

use AwardForce\Modules\Awards\Data\Award;

class Builder extends \Illuminate\Database\Eloquent\Builder
{
    private bool $preventsLazyLoadingInColumnator = false;

    public function preventLazyLoadingInColumnator(bool $prevent = true)
    {
        $this->preventsLazyLoadingInColumnator = $prevent;
        
        return $this;
    }
    public function getModels($columns = ['*'])
    {
        return $this->hydrate(
            $this->query->get($columns)->all()
        )->all();
    }
    public function hydrate(array $items)
    {
        $instance = $this->newModelInstance();

        return $instance->newCollection(array_map(function ($item) use ($items, $instance) {
            $model = $instance->newFromBuilder($item);
 
            if (count($items) > 1) {
                $model->preventsLazyLoading =  $this->preventsLazyLoadingInColumnator;
            }

            return $model;
        }, $items));
    }
}
