<?php

namespace Platform\Database\Eloquent;

use Carbon\Carbon;
use Platform\Events\ModelWasArchived;
use Platform\Events\ModelWasUnarchived;

trait Archivisation
{
    /**
     * Scope a query to only include archived items.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeArchived($query)
    {
        return $query->whereNotNull($this->getArchivedAtColumn());
    }

    /**
     * Scope a query to only include current items.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function scopeCurrent($query)
    {
        return $query->whereNull($this->getArchivedAtColumn());
    }

    /**
     * Get the name of the "deleted at" column.
     *
     * @return string
     */
    protected function getArchivedAtColumn()
    {
        return defined('static::ARCHIVED_AT') ? static::ARCHIVED_AT : 'archived_at';
    }

    /**
     * Get the class name of the "archived" event.
     *
     * @return string
     */
    protected function getArchivedEvent()
    {
        return property_exists($this, 'archivedEvent') ? $this->archivedEvent : ModelWasArchived::class;
    }

    /**
     * Get the class name of the "unarchived" event.
     *
     * @return string
     */
    protected function getUnarchivedEvent()
    {
        return property_exists($this, 'unarchivedEvent') ? $this->unarchivedEvent : ModelWasUnarchived::class;
    }

    /**
     * Archive the model instance.
     *
     * @return bool
     */
    public function archive()
    {
        $this->{$this->getArchivedAtColumn()} = Carbon::now();

        $archivedEvent = $this->getArchivedEvent();

        $result = $this->save();
        $this->raise(new $archivedEvent($this));

        return $result;
    }

    /**
     * Unarchive the model instance.
     *
     * @return bool
     */
    public function unarchive()
    {
        $this->{$this->getArchivedAtColumn()} = null;

        $unarchivedEvent = $this->getUnarchivedEvent();

        $result = $this->save();
        $this->raise(new $unarchivedEvent($this));

        return $result;
    }

    /**
     * Determine if the model instance has been archived.
     *
     * @return bool
     */
    public function isArchived()
    {
        return ! is_null($this->{$this->getArchivedAtColumn()});
    }
}
