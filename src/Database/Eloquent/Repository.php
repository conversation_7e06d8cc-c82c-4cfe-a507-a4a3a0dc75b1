<?php

namespace Platform\Database\Eloquent;

use Illuminate\Database\Eloquent\Model as EloquentModel;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Request;
use Platform\Database\MagicRepositoryMethods;
use Platform\Search\SearchFilterCollection;
use Tectonic\Localisation\Contracts\Translatable;

abstract class Repository implements \Platform\Database\Repository
{
    use MagicRepositoryMethods;

    /**
     * Stores the model object for querying.
     *
     * @var Eloquent
     */
    protected $model;

    /**
     * Returns the number of models in the repository.
     *
     * @return int
     */
    public function countAll()
    {
        return $this->getQuery()->count();
    }

    /**
     * Returns a collection of all records for this repository and the models or entities it respresents.
     *
     * @return Collection
     */
    public function getAll()
    {
        return $this->getQuery()->get();
    }

    /**
     * Acts as a generic method for retrieving a record by a given field/value pair.
     *
     * @return mixed
     */
    public function getBy($field, $value)
    {
        return $this->getByQuery($field, $value)->get();
    }

    /**
     * Retrieves a single record based on the field and value provided.
     *
     * @return null
     */
    public function getOneBy($field, $value)
    {
        return $this->getByQuery($field, $value)->first();
    }

    /**
     * Return a single model object based on its url slug value.
     *
     * @param  string  $slug
     * @return null
     */
    public function getBySlug($slug)
    {
        return $this->getQuery()
            ->whereSlug($slug)
            ->whereNotNull('slug')      // prevent null match
            ->where('slug', '!=', '')   // prevent empty match
            ->first();
    }

    /**
     * Returns a collection of models given an array of slugs.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getBySlugs(array $slugs)
    {
        return $this->getQuery()
            ->whereIn('slug', $slugs)
            ->get();
    }

    /**
     * Creates a query object used for getBy and getOneBy methods. This is particularly handy for models
     * that have translatable fields. In short, it allows the developer to easily query for model objects
     * that may have fields that reside within the translations table.
     *
     * @return QueryBuilder
     */
    protected function getByQuery($field, $value)
    {
        $model = $this->model;
        $translatableFields = $this->getTranslatableFields($this->model);
        $query = $this->getQuery();

        // If the model is translatable, and the field exists within the array, as well as the model having a
        // translations relationship defined on the model, we can do some neat stuff querying for a field value.
        if (in_array($field, $translatableFields) && method_exists($model, 'translations')) {
            $query = $query->whereHas('translations', function ($query) use ($field, $value, $model) {
                $query->where('resource', '', class_basename($model));
                $query->where('field', '=', $field);
                $query->where('value', 'LIKE', "%{$value}%");
            });
        } else {
            $query = $query->where($field, '=', $value);
        }

        return $query;
    }

    /**
     * Returns a single record based on id.
     *
     * @return null
     */
    public function getById($id)
    {
        $model = $this->getBy('id', $id);

        return ! $model->isEmpty() ? $model[0] : null;
    }

    /**
     * Retrieve a collection of results based on the search filters provided.
     *
     * @param  bool  $paginate
     * @return mixed
     */
    public function getByFilters(SearchFilterCollection $filters, $paginate = true)
    {
        $query = $this->applyFilters($filters)
            ->preventLazyLoadingInColumnator();

        if ($paginate) {
            $perPage = min(Request::get('per_page', 10), 100);

            return $query->paginate($perPage);
        }

        return $query->get();
    }

    public function getByFiltersLimit(SearchFilterCollection $filters, int $limit = 100, int $offset = 0)
    {
        return $this->applyFilters($filters)
            ->preventLazyLoadingInColumnator()
            ->paginate($limit, page: $offset);
    }

    /**
     * Search for a collection of objects using a search filter collection, but paginate with a custom implementation.
     *
     * @return mixed
     */
    public function searchWithPagination(SearchFilterCollection $filters, SearchFilterCollection $paginationFilters)
    {
        $page = Paginator::resolveCurrentPage('page');
        $perPage = Request::get('per_page', 10);

        // This is the main query that returns the necessary items
        $query = $this->applyFilters($filters, true);
        $query->forPage($page, $perPage);

        $items = $query->get();

        // The next application of filters gets the count
        $total = $this->applyFilters($paginationFilters, true)->count();

        return new LengthAwarePaginator($items, $total, $perPage);
    }

    /**
     * Prepares a query using the given filters. Does not get results.
     *
     * @return mixed
     */
    public function applyFilters(SearchFilterCollection $filters, bool $respectFilterApplies = false)
    {
        $query = $this->getQuery();

        foreach ($filters as $filter) {
            if ($respectFilterApplies && ! $filter->applies()) {
                continue;
            }

            $query = $filter->applyToEloquent($query);
        }

        return $query;
    }

    /**
     * Retrieve a collection of resource ID's based on the search filters provided.
     *
     * @return mixed
     */
    public function getIdsByFilters(SearchFilterCollection $filters)
    {
        $query = $this->applyFilters($filters);

        return $query->pluck('id');
    }

    /**
     * Save 1-n resources.
     *
     * @param  array  $resources
     * @return mixed
     *
     * @throws \Exception
     */
    public function saveAll(...$resources)
    {
        if (count($resources) == 0) {
            throw new \Exception('You must provide at least one $resource argument.');
        }

        foreach ($resources as $resource) {
            $this->save($resource);
        }
    }

    /**
     * Similar signature to saveAll, except here you can pass a collection or array of resources.
     *
     * @param  array|Collection  $resources
     * @return mixed
     *
     * @codeCoverageIgnore
     */
    public function saveMany($resources)
    {
        DB::transaction(function () use ($resources) {
            foreach ($resources as $resource) {
                $this->save($resource);
            }
        });
    }

    /**
     * Searches for a resource with the field and value provided. If no resource is found that matches
     * the value, then it will throw a ModelNotFoundException.
     *
     * @param  string  $field
     * @param  string  $value
     * @return Eloquent
     *
     * @throws ModelNotFoundException
     */
    public function requireBy($field, $value)
    {
        $result = $this->getBy($field, $value);

        if ($result->isEmpty()) {
            $exception = with(new ModelNotFoundException)->setModel(get_class($this->model));

            throw $exception;
        }

        return $result->first();
    }

    /**
     * Returns the model that is being used by the repository.
     *
     * @return Model
     */
    public function getModel()
    {
        return $this->model;
    }

    /**
     * @return \Illuminate\Database\Connection
     *
     * @codeCoverageIgnore
     */
    protected function getConnection()
    {
        return $this->getModel()->getConnection();
    }

    /**
     * Create a resource based on the data provided.
     *
     * @return resource
     *
     * @deprecated
     */
    public function getNew(array $data = [])
    {
        $model = $this->model->newInstance($data);

        return $model;
    }

    /**
     * Returns a new query object that can be used.
     *
     * @return mixed
     */
    abstract protected function getQuery();

    /**
     * Sets the model to be used by the repository.
     */
    public function setModel($model)
    {
        $this->model = $model;
    }

    /**
     * Delete a specific resource. Returns the resource that was deleted.
     *
     * @param  object  $resource
     * @param  bool  $permanent
     * @return object
     */
    public function delete($resource, $permanent = false)
    {
        if ($permanent) {
            $resource->forceDelete();
        } else {
            $resource->delete();
        }

        return $resource;
    }

    /**
     * Delete all resources passed to the method.
     *
     * @param  mixed  ...$resources
     * @return mixed
     */
    public function deleteAll(...$resources)
    {
        $resources = Arr::flatten($resources);

        foreach ($resources as $resource) {
            if ($resource instanceof Collection) {
                return $this->deleteAll($resource->all());
            }

            if (! is_object($resource)) {
                $resource = $this->getById($resource);
            }

            if ($resource) {
                $this->delete($resource);
            }
        }
    }

    /**
     * Update a resource based on the id and data provided.
     *
     * @param  object  $resource
     * @param  array  $data
     * @return object
     */
    public function update($resource, $data = [])
    {
        if (is_array($data) && count($data) > 0) {
            $resource->fill($data);
        }

        $this->save($resource);

        return $resource;
    }

    /**
     * Saves the resource provided to the database.
     *
     *
     * @return resource
     */
    public function save($resource)
    {
        $attributes = $resource->getDirty();

        if (! empty($attributes) || ! $resource->exists) {
            return $resource->save();
        }

        if ($resource->timestamps === true) {
            return $resource->touch();
        }
    }

    /**
     * Determines the translatable fields available on the model assigned to the repository.
     *
     * @param  Model|EloquentModel  $model
     * @return bool
     */
    protected function getTranslatableFields($model)
    {
        return $model instanceof Translatable ? $model->getTranslatableFields() : [];
    }

    /**
     * Creates copies of the specified models.
     *
     * @return \Illuminate\Support\Collection
     */
    public function copyByIds(array $ids)
    {
        $models = $this->getByIds($ids);

        return $models->map(function (EloquentModel $model) {
            $copy = $model->copy();
            $this->save($copy);

            return $copy;
        });
    }

    /**
     * Returns a collection of models given an array of IDs.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getByIds(array $ids)
    {
        return $this->getQuery()->whereIn('id', $ids)->get();
    }

    /**
     * Returns an array of all of the existing model IDs.
     *
     * @return array
     */
    public function getIds()
    {
        return $this->getQuery()->pluck('id')->toArray();
    }

    /**
     * Returns an array of all of the trashed existing model IDs.
     *
     * @return array
     */
    public function getTrashedIds()
    {
        return $this->getQuery()->onlyTrashed()->pluck('id')->toArray();
    }

    /**
     * Restores a number of resources based on their ids.
     *
     * @param  array  $resources
     * @return mixed
     */
    public function restore($resources)
    {
        foreach ($resources as $resource) {
            $resource->restore();

            if (isset($resource->undeletedEvent)) {
                $event = $resource->undeletedEvent;
                $resource->raise(new $event($resource));
            }
        }
    }

    /**
     * Same as getByIds, but returns all trashed/deleted resources.
     *
     * @return mixed
     */
    public function getTrashedByIds(array $ids)
    {
        return $this->getQuery()->onlyTrashed()->whereIn('id', $ids)->get();
    }

    /**
     * Returns the list of IDs filtered by only those that are actually valid.
     * Useful for filtering user input into only the legitimate IDs without needing validation or exception handling.
     *
     * @param  int  $seasonId
     * @return \Illuminate\Support\Collection
     */
    public function filterIds(array $ids, $seasonId = null)
    {
        $query = $this->getQuery();

        if ($seasonId && method_exists($this->model, 'season')) {
            $query->whereSeasonId($seasonId);
        }

        return $query->whereIn('id', $ids)->pluck('id')->toArray();
    }

    /**
     * Deletes the records identified by the array of IDs from the database,
     * as well as matching records from the pivot tables.
     * ['pivot_table' => 'pivot_key']
     *
     * @param  array  $ids
     *
     * @codeCoverageIgnore
     */
    protected function deleteWithPivots($ids, array $pivots)
    {
        if (! count($ids)) {
            return;
        }

        $this->getQuery()
            ->whereIn('id', $ids)
            ->delete();

        foreach ($pivots as $table => $key) {
            $this->getConnection()
                ->table($table)
                ->whereIn($key, $ids)
                ->delete();
        }
    }

    /**
     * Calculates the appropriate Local ID for the specified entry.
     *
     * @param  object  $model
     * @return int
     */
    public function calculateLocalId($model)
    {
        if ($model->localId) {
            return $model->localId;
        }

        $query = $this->getQuery()
            ->whereNotNull('local_id');

        if ($this->softDeletable($model)) {
            $query = $query->withTrashed();
        }

        return $query->max('local_id') + 1;
    }

    /**
     * Deletes all of the records assigned to the specified season.
     * Note, this will only work for records that are linked via season.
     *
     * @param  int  $seasonId
     */
    public function deleteAllFromSeason($seasonId)
    {
        $this->getQuery()->whereSeasonId($seasonId)->delete();
    }

    /**
     * Returns all matching records, with trashed.
     *
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getByIdsWithTrashed(array $ids)
    {
        return $this->getQuery()
            ->withTrashed()
            ->whereIn('id', $ids)
            ->get();
    }

    /**
     * Returns true if the model is soft deletable.
     */
    private function softDeletable(Model $model): bool
    {
        return in_array(SoftDeletes::class, class_uses($model));
    }

    /**
     * Returns true if any records exist.
     * This should be faster than `COUNT` as no data needs to be processed,
     * the database should return as soon as the first record is found.
     */
    public function hasAny(): bool
    {
        return $this->getQuery()->exists();
    }

    /**
     * Executes the callback with the model locked for editing for the duration of the callback,
     * and then returns the model for any further work (such as dispatching events).
     *
     * @throws \Throwable
     */
    public function getWithLock(int $id, callable $callback): Model
    {
        return $this->getConnection()->transaction(function () use ($id, $callback) {
            $model = $this->getQuery()->lockForUpdate()->find($id);
            $callback($model);

            return $model;
        });
    }

    /**
     * Checks if all of the $ids are valid ids in the database.
     *
     * @param  array|int[]  $ids
     */
    public function hasAllIds(array $ids): bool
    {
        $count = $this->getQuery()->whereIn('id', $ids)->count();

        return $count == count($ids);
    }

    public function getAllWithTrashed(): Collection
    {
        return $this->getQuery()->withTrashed()->get();
    }

    public function permanentlyDeleteAllFromSeason(?int $seasonId): void
    {
        $this->getQuery()
            ->whereSeasonId($seasonId)
            ->forceDelete();
    }

    public function permanentlyDeleteAll(array $ids = []): void
    {
        throw_if(! $this->isSafe(), new UnsafeQueryException);

        $this->getQuery()
            ->when($ids, fn($query) => $query->whereIn('id', $ids))
            ->forceDelete();
    }

    protected function isSafe(): bool
    {
        return false;
    }

    public function transaction(\Closure $callback, int $attempts = 1): mixed
    {
        return $this->getConnection()->transaction($callback, $attempts);
    }
}
