<?php

namespace Platform\Database\Eloquent;

use Illuminate\Contracts\Pagination\Paginator;
use Platform\Database\Eloquent\Enums\TrashedMode;
use Platform\Database\NoRecordFound;
use Stringable;

interface BuilderRepository
{
    /**
     * Specify the fields to be retrieved from the data store.
     */
    public function fields(array $fields): static;

    /**
     * Executes the query and returns a Paginator for the result set.
     */
    public function paginate(...$arguments): Paginator;

    /**
     * Executes the query and retrieves all objects for the query.
     */
    public function get(): \Illuminate\Support\Collection;

    /**
     * Executes the query and returns a count of the matched objects.
     */
    public function count(): int;

    /**
     * Executes the query and returns the first object or null if nothing found.
     */
    public function first(): mixed;

    /**
     * Same as first(), but will throw an exception if no record was found.
     *
     * @throws NoRecordFound
     */
    public function require(): mixed;

    /**
     * Executes the query and indicates if a result exists.
     */
    public function exists(): bool;

    /**
     * Executes the query and returns the number of rows affected.
     */
    public function remove(): int;

    /**
     * Set the relationships that should be eager loaded.
     */
    public function with($relations, $callback = null): static;

    /**
     * Add a sort clause to the query.
     */
    public function sort($column, $direction = 'asc'): static;

    /**
     * Add a "group by" clause to the query
     */
    public function groupBy(...$groups): static;

    /**
     * Set whether to include trashed records in the query.
     */
    public function trashed(TrashedMode $mode): static;

    public function pluck($column, $key = null): \Illuminate\Support\Collection;

    public function just($value, $key = null): array;

    public function primary(int|string|Stringable ...$primary): static;
}
