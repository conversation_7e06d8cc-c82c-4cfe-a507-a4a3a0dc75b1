<?php

namespace Platform\Database\Eloquent;

trait Archivable
{
    /**
     * Returns all archived resources given an array of IDs.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getArchivedByIds(array $ids)
    {
        return $this->getQuery()->archived()->whereIn('id', $ids)->get();
    }

    /**
     * Archives a number of resources.
     *
     * @param  Collection|array  $resources
     * @return void
     */
    public function archive($resources)
    {
        foreach ($resources as $resource) {
            $resource->archive();
        }
    }

    /**
     * Unarchives a number of resources.
     *
     * @param  Collection|array  $resources
     * @return void
     */
    public function unarchive($resources)
    {
        foreach ($resources as $resource) {
            $resource->unarchive();
        }
    }
}
