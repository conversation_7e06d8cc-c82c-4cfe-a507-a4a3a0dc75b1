<?php

namespace Platform\Database\Eloquent;

use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Traits\Conditionable;
use Platform\Database\Eloquent\Enums\TrashedMode;
use Platform\Database\NoRecordFound;
use Stringable;

trait HasQueryBuilder
{
    use Conditionable;

    private ?Builder $query;

    protected function query(): Builder
    {
        return $this->query ??= $this->getQuery();
    }

    public function fields(array $fields): static
    {
        $this->query()->addSelect(Arr::map($fields, function ($field) {
            // The following regular expression checks to see if there are any characters within the field definition
            // that represents a value that is not a pure column select clause, such as aggregate functions: COUNT(*).
            // This allows us to automatically append DB::raw calls to field select clauses, thereby encapsulating
            // those calls within the application architecture, rather than leaking out into the codebase. //- Kirk
            if (preg_match('/^[a-z_.\s]+$/i', $field)) {
                return $field;
            }

            return DB::raw($field);
        }));

        return $this;
    }

    public function paginate(...$arguments): Paginator
    {
        return $this->validateSelect()->execute('paginate', $arguments);
    }

    public function get(): \Illuminate\Support\Collection
    {
        return $this->validateSelect()->execute('get');
    }

    public function count(): int
    {
        return $this->execute('count');
    }

    public function first(): ?Model
    {
        return $this->validateSelect()->execute('first');
    }

    public function require(): Model
    {
        if (! ($result = $this->first())) {
            throw new NoRecordFound;
        }

        return $result;
    }

    public function exists(): bool
    {
        return $this->execute('exists');
    }

    public function remove(): int
    {
        return $this->execute('delete');
    }

    public function with($relations, $callback = null): static
    {
        $this->query()->with($relations, $callback);

        return $this;
    }

    public function sort($column, $direction = 'asc'): static
    {
        $this->query()->orderBy($column, $direction);

        return $this;
    }

    public function groupBy(...$groups): static
    {
        $this->query()->groupBy($groups);

        return $this;
    }

    public function pluck($column, $key = null): \Illuminate\Support\Collection
    {
        return $this->execute('pluck', [$column, $key]);
    }

    public function just($value, $key = null): array
    {
        return $this->pluck($value, $key)->toArray();
    }

    public function trashed(TrashedMode $mode): static
    {
        match ($mode) {
            TrashedMode::All => $this->query()->withTrashed(),
            TrashedMode::None => $this->query()->withoutTrashed(),
            TrashedMode::Only => $this->query()->onlyTrashed(),
        };

        return $this;
    }

    public function primary(int|string|Stringable ...$primary): static
    {
        $this->query()->whereIn($this->model->getQualifiedKeyName(), $primary);

        return $this;
    }

    public function find(int|string|Stringable $id): ?Model
    {
        return $this->validateSelect()->execute('find', [$id]);
    }

    protected function execute(string $method, ?array $arguments = []): mixed
    {
        return tap($this->query()->{$method}(...$arguments), fn() => $this->reset());
    }

    /**
     * Validates the incoming select query, by ensuring that a select clause is provided. No select clause - no query!
     *
     * @throws InvalidQuery
     */
    private function validateSelect(): static
    {
        if (empty($this->query()->getQuery()->getColumns())) {
            throw new InvalidQuery('No select clause provided. Provide a select clause defining the required fields to be retrieved from the database.');
        }

        return $this;
    }

    private function reset(): void
    {
        $this->query = null;
    }
}
