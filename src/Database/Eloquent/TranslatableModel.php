<?php

namespace Platform\Database\Eloquent;

use Illuminate\Support\Facades\Config;
use Platform\Authorisation\Consumer;
use Platform\Localisation\Translation;
use Tectonic\Localisation\Contracts\Translatable;

trait TranslatableModel
{
    /**
     * Translated language strings.
     *
     * @var array
     */
    public $translated = [];

    /**
     * Defines the translations relationship for a given model.
     *
     * @return Relation
     *
     * @codeCoverageIgnore
     */
    public function translations()
    {
        return $this->hasMany(Config::get('localisation.model'), 'foreign_id')->where('resource', '=', class_basename($this));
    }

    /**
     * Retrieve a registration translation for a given key and language.
     *
     * @param  string  $key
     * @param  string  $lang
     * @return mixed
     */
    public function getTranslation($key, $lang)
    {
        $translation = array_get($this->translated, "$lang.$key");

        return is_string($translation) ? trim($translation) : $translation;
    }

    /**
     * Returns true if the model has a translation value for the selected key (in the language selected by customer).
     * Alternatively, if a fallback is provided, it will look for translation in fallback language if the first
     * language is missing the translation.
     *
     * @param  string  $key
     * @param  string  $lang
     * @return bool
     */
    public function hasTranslation($key, $lang = null, ?string $fallbackLang = null)
    {
        $lang = $lang ?: $this->languageCode();

        if (isset($this->translated[$lang][$key]) && ! empty($this->translated[$lang][$key])) {
            return true;
        }

        if (! $fallbackLang) {
            return false;
        }

        return isset($this->translated[$fallbackLang][$key]) && ! empty($this->translated[$fallbackLang][$key]);
    }

    /**
     * Add a translation for a given language, field and value for the current model.
     *
     * @param  string  $language
     * @param  string  $field
     * @param  string  $value
     * @return \Tectonic\LaravelLocalisation\Database\Translation
     *
     * @throws \Exception
     *
     * @codeCoverageIgnore
     */
    public function saveTranslation($language, $field, $value, int $accountId)
    {
        if (! $this->id) {
            throw new \Exception('Addition of translations can only be done once a model has been saved.');
        }

        $translation = new Translation(compact('language', 'field', 'value'));
        $translation->account_id = $accountId;
        $translation->resource = class_basename($this);
        $translation->foreign_id = $this->id;

        $this->translations()->save($translation);

        // Make use of the Translations setter
        if (method_exists($this, $method = 'setTranslated'.ucfirst($field).'Property')) {
            $this->$method($language, $field, $value);
        } else {
            // Now we cache the value locally
            $this->translated[$language][$field] = $value;
        }
    }

    /**
     * Returns the language code for the consumer.
     *
     * @return mixed
     */
    protected function languageCode()
    {
        return $this->consumer()->strictLanguage()->code;
    }

    /**
     * Return a consumer object.
     */
    abstract protected function consumer(): Consumer;

    /**
     * Checks to see if a given lang string is set.
     *
     * @param  string  $key
     * @return bool
     */
    public function __isset($key)
    {
        if ($this instanceof Translatable && in_array($key, $this->getTranslatableFields())) {
            return $this->hasTranslation($key, $this->languageCode());
        }

        return parent::__isset($key);
    }
}
