<?php

namespace Platform;

use Illuminate\Support\AggregateServiceProvider;
use Illuminate\Translation\Translator;
use Platform\Html\ButtonBuilder;
use Platform\Html\FormError;
use Platform\Kessel\Middleware as KesselMiddleware;
use Platform\Localisation\MessageSelector;
use Platform\Menu\Manager;

class PlatformServiceProvider extends AggregateServiceProvider
{
    protected $providers = [
        \Platform\Bus\BusServiceProvider::class,
        \Platform\Database\QueryServiceProvider::class,
        \Platform\Language\LanguageServiceProvider::class,
        \Platform\Menu\MenuServiceProvider::class,
    ];

    public function register()
    {
        parent::register();

        $this->registerMenu();
        $this->registerButtons();
        $this->registerFormError();
        $this->registerKessel();
        $this->registerSanitiser();
    }

    public function boot()
    {
        require_once __DIR__.'/macros.php';
        require __DIR__.'/validators.php';
        require_once __DIR__.'/Html/macros.php';

        $this->publishes([
            __DIR__.'/../config/assets.php' => $this->config_path('assets.php'),
            __DIR__.'/../config/kessel.php' => $this->config_path('kessel.php'),
            __DIR__.'/../config/domains.php' => $this->config_path('domains.php'),
            __DIR__.'/../config/features.php' => $this->config_path('features.php'),
            __DIR__.'/../config/payments.php' => $this->config_path('payments.php'),
            __DIR__.'/../config/products.php' => $this->config_path('products.php'),
            __DIR__.'/../resources/views' => base_path('resources/views/vendor/platform'),
        ]);

        $this->loadViewsFrom(__DIR__.'/../resources/views', 'platform');

        $this->bootTranslator();

        $this->configProducts();
    }

    private function registerMenu()
    {
        $this->app->singleton(Manager::class);
    }

    private function registerButtons()
    {
        $this->app->singleton('button', ButtonBuilder::class);
    }

    private function registerFormError()
    {
        $this->app->singleton('formerror', FormError::class);
    }

    /**
     * Implementation of config path that doesn't require a helper.
     */
    private function config_path(string $path): string
    {
        return $this->app->make('path.config').($path ? DIRECTORY_SEPARATOR.$path : $path);
    }

    private function registerKessel()
    {
        $this->app->bind(KesselMiddleware::class, function ($app) {
            return new KesselMiddleware($app['config']['kessel']);
        });
    }

    private function bootTranslator()
    {
        $this->app->make(Translator::class)->setSelector(new MessageSelector);
    }

    private function configProducts()
    {
        foreach (['awards', 'grants'] as $vertical) {
            $products = array_keys(array_filter(config('products.available'), function ($product) use ($vertical) {
                return in_array($vertical, $product['verticals']);
            }));

            config(['products.verticals.'.$vertical => $products]);
        }
    }

    private function registerSanitiser(): void
    {
        $this->app->scoped('html.purifier', static fn() => static::ezyangHtmlPurifier());
    }

    /**
     * @deprecated The entire Ezyang/HtmlPurifier package will be removed, once we switch to Symfony's HTML sanitizer.
     */
    private static function ezyangHtmlPurifier(): \HTMLPurifier
    {
        $config = \HTMLPurifier_Config::createDefault();

        static::ezyangAllowIframes($config);

        return new \HTMLPurifier($config);
    }

    private static function ezyangAllowIframes(\HTMLPurifier_Config $config)
    {
        $config->set('HTML.Doctype', 'HTML 4.01 Transitional');
        $config->set('HTML.Allowed', 'iframe[src|width|height|class|frameborder|allowfullscreen|aria-label],h1,h2,h3,blockquote,b,strong,i,em,u,a[href|class|title|data-redirector],ul,ol,li,p[style],br,span[style],img[width|height|alt|src]');

        $config->set('HTML.SafeIframe', true);
        $config->set(
            'URI.SafeIframeRegexp',
            '%^(https?:)?//(www\.youtube(?:-nocookie)?\.com/embed/|player\.vimeo\.com/video/)%'
        );

        $def = $config->getHTMLDefinition(true);

        $def->addAttribute('iframe', 'allowfullscreen', 'Bool');
        $def->addAttribute('iframe', 'aria-label', 'Text');
        $def->addAttribute('a', 'data-redirector', 'Text');
    }
}
