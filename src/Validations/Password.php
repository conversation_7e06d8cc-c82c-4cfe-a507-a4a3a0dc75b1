<?php

namespace Platform\Validations;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Str;

class Password implements Rule
{
    protected $attribute;

    /**
     * The minimum size of the password.
     *
     * @var int
     */
    protected $min = 12;

    /**
     * Create a new rule instance.
     *
     * @param  int|null  $min
     */
    public function __construct(int $min = 1)
    {
        $this->min = max($this->min, $min);
    }

    private function validateLetters($value): bool
    {
        return (bool) preg_match('/\pL/u', $value);
    }

    private function validateNumbers($value): bool
    {
        return (bool) preg_match('/\pN/', $value);
    }

    private function validateCaseDiff($value): bool
    {
        return (bool) preg_match('/(\p{Ll}+.*\p{Lu})|(\p{Lu}+.*\p{Ll})/u', $value);
    }

    private function validateSymbols($value): bool
    {
        return (bool) preg_match('/\p{S}|\p{P}/', $value);
    }

    private function checkMin($value): bool
    {
        return Str::length($value) >= $this->min;
    }

    /**
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $this->attribute = $attribute;

        $value = (string) $value;

        return $this->checkMin($value)
            && $this->validateLetters($value)
            && $this->validateNumbers($value)
            && $this->validateCaseDiff($value)
            && $this->validateSymbols($value);
    }

    /**
     * @return array|string
     */
    public function message()
    {
        return trans('validation.password_complexity', ['attribute' => $this->attribute]);
    }
}
