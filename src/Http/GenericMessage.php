<?php

namespace Platform\Http;

use Illuminate\Http\RedirectResponse;

class GenericMessage
{
    /**
     * Message to be used for the page once redirected.
     *
     * @var string
     */
    private $message;

    /**
     * The title for the page. This is not required, but is nice.
     *
     * @var string
     */
    private $title;

    /**
     * If the message page should redirect the user to a forwarding URL once
     * complete, then this should be provided.
     *
     * @var string
     */
    private $url;

    /**
     * The button text to be used for the button if a forwarding url is provided.
     *
     * @var string
     */
    private $buttonText;

    /**
     * The message service requires only one piece of information to function - the message.
     *
     * @param  string  $message
     */
    public function __construct($message)
    {
        $this->message = $message;
        $this->buttonText = trans('buttons.continue');
    }

    /**
     * Provides the title to be used for the message page.
     *
     * @param  string  $title
     * @return GenericMessage
     */
    public function withTitle($title)
    {
        $this->title = $title;

        return $this;
    }

    /**
     * Defines the text to be used for the button if a forwarding url is provided.
     *
     * @param  string  $text
     * @return GenericMessage
     */
    public function withButtonText($text)
    {
        $this->buttonText = $text;

        return $this;
    }

    /**
     * The URL to forward the user to after they've landed on the message page.
     *
     * @param  string  $url
     * @return GenericMessage
     */
    public function forwardTo($url)
    {
        $this->url = $url;

        return $this;
    }

    /**
     * Creates the response object that is required to be returned from a piece of
     * middleware or a controller action.
     *
     * @return RedirectResponse
     */
    public function response()
    {
        $response = redirect()
            ->route('message')
            ->with('message', $this->message)
            ->with('title', $this->title)
            ->with('url', $this->url)
            ->with('buttonText', $this->buttonText);

        return $response;
    }
}
