<?php

namespace Platform\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class TrimInput
{
    /**
     * Trims all string input in the request object, ignoring any fields with the term `password` as a precaution.
     *
     * If this is being expanded for custom sanitisation behaviours per route, you should review:
     *   https://github.com/tectonic/platform/pull/3/commits/d3ce9bbfff13f1662ff06a7ba50b4c507ec6523e
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $input = $request->all();

        $subset = array_filter($input, function ($value, $key) {
            return ! str_contains($key, 'password');
        }, ARRAY_FILTER_USE_BOTH);

        $request->merge($this->sanitiseArray($subset));

        return $next($request);
    }

    private function sanitiseArray(array $array): array
    {
        return array_map(function ($value) {
            if (is_array($value)) {
                return $this->sanitiseArray($value);
            }

            return is_string($value) ? trim($value) : $value;
        }, $array);
    }
}
