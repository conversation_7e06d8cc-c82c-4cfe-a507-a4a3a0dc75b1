<?php

namespace Platform\Http\Middleware;

use Closure;

/**
 * If the environment is set to production, and the request is non-secure, then redirect the user
 * as part of a new secure request to the application.
 */
class RedirectToHttps
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (! app()->environment('local') && $request->header('x-forwarded-proto') == 'http') {
            return redirect()->secure(current_url());
        }

        return $next($request);
    }
}
