<?php

namespace Platform\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class Remembrance
{
    public const FILTER_RESET = 'ftreset';

    public function __construct(private Redirector $redirector)
    {
    }

    public function handle(Request $request, Closure $next)
    {
        if ($request->wantsJson()) {
            return $next($request);
        }

        // Session and Parameters
        $session = $request->session();
        $key = self::key($request->path());
        $parameters = $request->except('_pjax');

        // Check for the reset token
        if ($parameters[self::FILTER_RESET] ?? false) {
            $session->forget($key);
            $session->reflash();

            return $this->redirector->to($request->path());
        }

        // Redirect to saved url, if not parameters
        if (! count($parameters) && $session->has($key)) {
            $session->reflash();

            return $this->redirector->to($session->get($key));
        }

        // Save url if we have parameters
        if (count($parameters)) {
            $session->put($key, $this->cleanUrl($request));
        }

        return $next($request);
    }

    /**
     * @param  string  $url
     * @return string|null
     */
    public static function forUrl($url)
    {
        $path = Str::replaceFirst('/', '', parse_url($url, PHP_URL_PATH));

        return Session::get(self::key($path));
    }

    protected static function key(string $path): string
    {
        return __CLASS__.'|'.$path;
    }

    /**
     * Strip out any system parameters.
     *
     * @return string
     */
    protected function cleanUrl(Request $request)
    {
        return str_replace(['_pjax=%23pjaxContainer&', '_pjax=%23pjaxContainer'], '', current_url());
    }

    public static function removeFilters(string $url, array $filters): string
    {
        parse_str(($parsedUrl = parse_url($url))['query'] ?? '', $parsedQuery);

        foreach ($filters as $filter) {
            unset($parsedQuery[$filter]);
        }

        session()->forget(self::key($path = $parsedUrl['path']));

        $query = http_build_query($parsedQuery);

        return $path.($query ? '?'.$query : '');
    }
}
