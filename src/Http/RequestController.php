<?php

namespace Platform\Http;

use Illuminate\Http\Request;

trait RequestController
{
    /**
     * Return the controller that will be used to manage the request.
     *
     * @return string
     */
    protected function controller(Request $request)
    {
        return $this->uses($request)[0];
    }

    /**
     * Return the controller action that will be used for the request.
     *
     * @return mixed
     */
    protected function action(Request $request)
    {
        return $this->uses($request)[1];
    }

    /**
     * Helper method to retrieve the controller and action for a request.
     *
     * @return array
     */
    protected function uses(Request $request)
    {
        return explode('@', $request->route()->getAction()['uses']);
    }
}
