<?php

namespace Platform\Http;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Facades\Request;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Contracts\View\View;

/**
 * @codeCoverageIgnore
 */
trait Respondable
{
    protected Application|Factory|View $layout;

    /**
     * Respond with the $data array for JSON, a partial of the view for PJAX requests,
     * or the full layout render if it's a full page request.
     *
     * @param string $view
     * @param array|Arrayable  $data
     * @return array|void
     */
    public function respond($view, $data = [])
    {
        if (Request::wantsJson()) {
            return $this->formatJson($data);
        }

        $this->layout->content = view($view, $data);
    }

    /**
     * Determines whether or not the request is a PJAX request.
     *
     */
    public function isPjax(): bool
    {
        return Request::header('X-PJAX') === 'true';
    }

    /**
     * Returns true if the request is for the full page.
     *
     */
    public function isFullPage(): bool
    {
        return ! Request::wantsJson() && ! $this->isPjax();
    }

    /**
     * Format an array or collection of variables. Basically, we want to ensure
     * an array response for the controller action for json requests.
     *
     */
    public function formatJson(array|Arrayable $var): array
    {
        if (is_array($var)) {
            foreach ($var as &$value) {
                if ($value instanceof Arrayable) {
                    $value = $value->toArray();
                }
            }
            return $var;
        }

        return $var->toArray();
    }

    /**
     * Pulled from Laravel 4. Laravel 5 completely does away with any ability to support PJAX
     * applications - we need this method as it was.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function callAction($method, $parameters)
    {
        $this->setupLayout();

        $response = parent::callAction($method, array_values($parameters));

        if (is_null($response) && ! is_null($this->layout)) {
            $response = $this->layout;
        }

        return $response;
    }

    /**
     * Setup the layout that may be required for the view.
     */
    protected function setupLayout()
    {
        if ($this->isFullPage()) {
            $this->layout = view('layouts.fullpage');
        } elseif ($this->isPjax()) {
            $this->layout = view('layouts.pjax');
        }
    }
}
