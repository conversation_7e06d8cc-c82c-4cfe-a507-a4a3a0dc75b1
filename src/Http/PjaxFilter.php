<?php

namespace Platform\Http;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

/**
 * Class PjaxFilter
 *
 * The PJAX filter has one task only - to check to see if the request is pjax, and if so - make
 * sure that the URL provided that should be added to the pushstate, is the right one.
 */
class PjaxFilter
{
    use Respondable;

    /** @var string */
    protected static $version;

    public function handle(Request $request, \Closure $next)
    {
        $response = $next($request);

        if (! $this->isPjax()) {
            return $response;
        }

        $response->header('X-PJAX-URL', $request->getRequestUri());
        $response->header('X-PJAX-VERSION', Session::get('X-PJAX-VERSION', self::$version));

        return $response;
    }

    public static function setVersion(?string $version)
    {
        self::$version = $version;
    }
}
