<?php

namespace Platform\Http;

use Carbon\Carbon;
use Illuminate\Support\Facades\Session;

abstract class Controller extends \Illuminate\Routing\Controller
{
    use \Platform\Http\Respondable;

    /**
     * Pulled from Laravel 4. Laravel 5 completely does away with any ability to support PJAX
     * applications - we need this method as it was.
     *
     * @param  string  $method
     * @param  array  $parameters
     * @return mixed
     */
    public function callAction($method, $parameters)
    {
        $this->setupLayout();

        $response = parent::callAction($method, array_values($parameters));

        if (is_null($response) && ! is_null($this->layout)) {
            $response = $this->layout;
        }

        return $response;
    }

    /**
     * Calling that method inside child controller
     * will force page reload on next request
     */
    protected function pjaxReload()
    {
        Session::flash('X-PJAX-VERSION', Carbon::now()->timestamp);
    }
}
