<?php

namespace Platform\Http;

use Illuminate\Support\Facades\Request;

trait Messaging
{
    /**
     * Allows for controllers and other HTTP layer classes respond with
     * a flashed message and type. This is a helper method to ensure
     * said variables are always available when rendering on the subsequent
     * request.
     *
     * @param  string  $message
     * @param  string  $type
     * @param  string  $route
     * @param  int  $jsonStatus
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function withMessage($message, $type, $route = null, $jsonStatus = 400)
    {
        if (Request::wantsJson()) {
            return response()->json([
                'type' => $type,
                'message' => $message,
            ], $jsonStatus);
        }

        if (! is_null($route)) {
            $redirect = is_array($route) ? redirect()->route($route[0], $route[1]) : redirect()->route($route);
        } else {
            $redirect = redirect()->back();
        }

        return $redirect->with('type', $type)->with('message', $message);
    }
}
