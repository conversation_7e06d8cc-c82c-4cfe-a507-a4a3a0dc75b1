<?php

namespace Platform\Kessel;

trait RegionalGateway
{
    private $region;

    public function forRegion(string $region, ?bool $uat = false)
    {
        return (new self(Hyperdrive::forAF($region, $uat)))->setRegion($region);
    }

    public function setRegion(string $region)
    {
        $this->region = $region;

        return $this;
    }

    public function region(): string
    {
        return $this->region;
    }
}
