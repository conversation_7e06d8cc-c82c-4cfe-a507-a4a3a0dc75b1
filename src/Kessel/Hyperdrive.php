<?php

namespace Platform\Kessel;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Promise\Utils;
use Platform\Authorisation\Signatory;
use Platform\Kessel\Data\RegionalResource;
use Platform\Kessel\Data\RegionalResourceCollection;
use Platform\Kessel\Endpoints\AwardForce;
use Platform\Kessel\Endpoints\BillingPortal;
use Platform\Kessel\Endpoints\MyAwardForce;
use Platform\Kessel\Exceptions\ResourceNotFound;
use Platform\Kessel\Exceptions\ValidationFailed;
use Psr\Http\Message\ResponseInterface;
use Throwable;

class Hyperdrive
{
    const HEADER_KEY = 'X-KESSEL-KEY';

    const HEADER_SIGNATURE = 'X-KESSEL-SIGNATURE';

    const HEADER_ORIGIN = 'X-KESSEL-ORIGIN';

    /** @var Client */
    private $client;

    /** @var Signatory */
    private $signatory;

    /** @var string */
    private $endpoint;

    public function __construct()
    {
        $this->client = app(Client::class);
        $this->signatory = app(config('kessel.system_consumer'));
    }

    /**
     * Generate a Hyperdrive specific to an AF region
     *
     * @return static
     */
    public static function forAF(string $region, ?bool $uat = false)
    {
        $hyperdrive = new static;

        $hyperdrive->endpoint = (new AwardForce($region, $uat))->value();

        return $hyperdrive;
    }

    /**
     * Generate a Hyperdrive specific to My Award Force
     *
     * @return static
     */
    public static function forMyAF()
    {
        $hyperdrive = new static;

        $hyperdrive->endpoint = (new MyAwardForce)->value();

        return $hyperdrive;
    }

    /**
     * Generate a Hyperdrive specific to the Billing Portal
     *
     * @return static
     */
    public static function forBillingPortal()
    {
        $hyperdrive = new static;

        $hyperdrive->endpoint = (new BillingPortal)->value();

        return $hyperdrive;
    }

    /**
     * List or show resource(s)
     *
     * @throws \GuzzleHttp\Exception\ClientException
     * @throws \Platform\Kessel\Exceptions\ValidationFailed
     * @throws \Platform\Kessel\Exceptions\ResourceNotFound
     */
    public function get(string $resource, array $parameters = []): array
    {
        return parse_response($this->request('get', $resource, $parameters));
    }

    /**
     * Perform concurrent requests to multiple AF regions and resources based on the provided map.
     *
     * @throws Throwable
     */
    public function getPoolForAf(RegionalResourceCollection $regionalResourceCollection): mixed
    {
        $promises = array_map(fn(RegionalResource $resource) => $this->client->getAsync(
            $this->forAF($resource->region())->buildUrl($resource->resource()),
            $this->requestOptions('get', [])
        ), $regionalResourceCollection->toArray());

        return Utils::settle(Utils::unwrap($promises))->wait();
    }

    /**
     * Create new resource
     *
     * @throws \GuzzleHttp\Exception\ClientException
     * @throws \Platform\Kessel\Exceptions\ValidationFailed
     * @throws \Platform\Kessel\Exceptions\ResourceNotFound
     */
    public function create(string $resource, array $data = []): array
    {
        return parse_response($this->request('post', $resource, $data));
    }

    /**
     * Update existing resource
     *
     * @throws \GuzzleHttp\Exception\ClientException
     * @throws \Platform\Kessel\Exceptions\ValidationFailed
     * @throws \Platform\Kessel\Exceptions\ResourceNotFound
     */
    public function update(string $resource, array $data = [])
    {
        $this->request('put', $resource, $data);
    }

    /**
     * Update existing resource with a response
     *
     * @throws \GuzzleHttp\Exception\ClientException
     * @throws \Platform\Kessel\Exceptions\ValidationFailed
     * @throws \Platform\Kessel\Exceptions\ResourceNotFound
     */
    public function updateWithResponse(string $resource, array $data = [])
    {
        return parse_response($this->request('put', $resource, $data));
    }

    /**
     * Delete specific resource
     *
     * @throws \GuzzleHttp\Exception\ClientException
     * @throws \Platform\Kessel\Exceptions\ValidationFailed
     * @throws \Platform\Kessel\Exceptions\ResourceNotFound
     */
    public function delete(string $resource)
    {
        $this->request('delete', $resource);
    }

    /**
     * Delete resources in bulk
     *
     * @throws \GuzzleHttp\Exception\ClientException
     * @throws \Platform\Kessel\Exceptions\ValidationFailed
     * @throws \Platform\Kessel\Exceptions\ResourceNotFound
     */
    public function deleteBulk(string $resource, array $data)
    {
        $this->request('delete', $resource, $data);
    }

    /**
     * Ping the requested endpoint.
     *
     * @throws \GuzzleHttp\Exception\ClientException
     * @throws \Platform\Kessel\Exceptions\ValidationFailed
     * @throws \Platform\Kessel\Exceptions\ResourceNotFound
     */
    public function ping(): ResponseInterface
    {
        return $this->request('get', 'ping');
    }

    /**
     * @throws ClientException
     * @throws ResourceNotFound
     * @throws ValidationFailed
     */
    private function request(string $action, string $resource, array $data = []): ResponseInterface
    {
        try {
            return $this->client->request($action, $this->buildUrl($resource), $this->requestOptions($action, $data));
        } catch (ClientException $exception) {
            switch ($exception->getCode()) {
                case 422:
                    throw new ValidationFailed($exception);
                case 404:
                    throw new ResourceNotFound($exception);
                default:
                    throw $exception;
            }
        }
    }

    private function requestOptions(string $action, array $data): array
    {
        $dataKey = ($action == 'get') ? 'query' : 'json';

        return array_filter([
            $dataKey => $data,
            'headers' => [
                'Accept' => 'application/json',
                self::HEADER_KEY => config('kessel.key'),
                self::HEADER_SIGNATURE => (string) $this->signatory->signature(),
                self::HEADER_ORIGIN => config('kessel.origin'),
            ],
        ]);
    }

    private function buildUrl(string $resource): string
    {
        return str_finish($this->endpoint, '/').$resource;
    }
}
