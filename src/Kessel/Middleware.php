<?php

namespace Platform\Kessel;

use Closure;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpKernel\Exception\HttpException;

class Middleware
{
    /**
     * @var array
     */
    private $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     *
     * @throws \Symfony\Component\HttpKernel\Exception\HttpException
     */
    public function handle($request, Closure $next)
    {
        Log::info('KESSEL ROUTE: '.$request->url());
        Log::info('KESSEL PAYLOAD: '.json_encode($request->except('password')));

        if ($this->invalidToken($request)) {
            throw new HttpException(401, 'Unauthorised');
        }

        if ($this->invalidSignature($request)) {
            throw new HttpException(400, 'Invalid signature');
        }

        return $next($request);
    }

    /**
     * @param  \Illuminate\Http\Request  $request
     */
    protected function invalidToken($request): bool
    {
        return $request->header(Hyperdrive::HEADER_KEY) !== $this->config['key'];
    }

    /**
     * @param  \Illuminate\Http\Request  $request
     */
    private function invalidSignature($request): bool
    {
        return ! $request->header(Hyperdrive::HEADER_SIGNATURE);
    }
}
