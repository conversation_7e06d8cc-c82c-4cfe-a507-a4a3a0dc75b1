<?php

namespace Platform\Kessel\Data;

use Assert\Assertion;

class RegionalResource
{
    public function __construct(protected string $region, protected string $resource)
    {
        Assertion::inArray($region, config('awardforce.regions'));
    }

    public function region(): string
    {
        return $this->region;
    }

    public function resource(): string
    {
        return $this->resource;
    }
}
