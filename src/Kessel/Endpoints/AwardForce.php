<?php

namespace Platform\Kessel\Endpoints;

use Assert\Assertion;

class AwardForce implements Endpoint
{
    /**
     * @var string
     */
    private $region;

    const AU = 'au';

    const EU = 'eu';

    const US = 'us';

    public function __construct(string $region, private ?bool $uat = false)
    {
        Assertion::inArray($region, config('awardforce.regions'));

        $this->region = $region;
    }

    /**
     * Return endpoint value
     */
    public function value(): string
    {
        return $this->uat
            ? config("kessel.endpoints.af_{$this->region}_uat")
            : config("kessel.endpoints.af_{$this->region}");
    }
}
