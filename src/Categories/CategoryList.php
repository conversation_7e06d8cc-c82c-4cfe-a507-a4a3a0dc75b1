<?php

namespace Platform\Categories;

use Illuminate\Support\Collection;

class CategoryList extends Collection
{
    const NOPARENT = '-';

    public function groupByParent(Collection $allCategories): self
    {
        return $this
            ->map(function ($category) use ($allCategories) {
                if ($category->parentId && $allCategories->contains('id', $category->parentId)) {
                    return $category;
                }
                $category->parentId = 0;

                return $category;
            })
            ->groupBy('parentId');
    }

    public function keyByParent(Collection $allCategories): self
    {
        return $this->keyBy(function (Collection $categories, $parent) use ($allCategories) {
            return ($parent && isset($allCategories[$parent])) ? $this->parentName($parent, $allCategories) : self::NOPARENT;
        });
    }

    public function transformForParents(bool $parents, array $parentIds): self
    {
        return $this->when(! $parents, function (CategoryList $categories) use ($parentIds) {
            return $categories->transform(function (Collection $categories, $group) use ($parentIds) {
                if ($group == CategoryList::NOPARENT) {
                    return $categories->reject(fn($category) => in_array($category->id, $parentIds));
                }

                return $categories;
            });
        });
    }

    private function parentName(int $parent, Collection $categories): string
    {
        $name = [];

        while ($parent) {
            $name[] = $this->categoryName($categories[$parent]);
            $parent = $categories[$parent]->parentId;
        }

        return implode(': ', array_reverse($name));
    }

    /**
     * @return string
     */
    protected function categoryName($category)
    {
        $translation = $category->name;

        return (! is_null($translation) && $translation !== '') ? $translation : 'NTA';
    }

    /**
     * @param  Collection  $categories
     */
    public function pluckNames(bool $divisions, array $identifier): Collection
    {
        if (! $divisions) {
            return $this->mapWithoutDivision($identifier);
        }

        return $this->map(fn(CategoryList $categories) => $categories->mapWithKeys(fn($category) => $category->divisions <= 1 ?
                    [call_user_func($identifier, $category) => $this->categoryName($category)] :
                    $this->transformWithDivision($category, $identifier)
        )->sortBy(fn($name, $slug) => strtolower($name))
        );
    }

    private function mapWithoutDivision(array $identifier): self
    {
        return $this->map(fn(CategoryList $categories) => $categories->mapWithKeys(fn($category) => [call_user_func($identifier, $category) => $this->categoryName($category)])
            ->sort()
        );
    }

    private function transformWithDivision($category, $identifier): self
    {
        return self::times($category->divisions)
            ->mapWithKeys(function ($division) use ($category, $identifier) {
                return [
                    call_user_func($identifier, $category).'.'
                    .$division => trans('category.search.division', [
                        'category' => $this->categoryName($category),
                        'division' => $division,
                    ]),
                ];
            })->put(call_user_func($identifier, $category), $this->categoryName($category));
    }
}
