<?php

namespace Platform\Categories;

use Illuminate\Support\Collection;
use Platform\Database\Repository;
use Tectonic\LaravelLocalisation\Translator\Engine;

class CategoryListGenerator
{
    /** @var Repository */
    protected $repository;

    /** @var Engine */
    protected $translator;

    protected $missingCache = [];

    public function __construct(Repository $repository, Engine $translator)
    {
        $this->repository = $repository;
        $this->translator = $translator;
    }

    /**
     * @param  bool  $divisions  Include category divisions in the list?
     * @param  bool  $parents  Include parent categories?
     */
    public function generate(Collection $categories, bool $divisions = false, bool $parents = true): Collection
    {
        $categories = new CategoryList($categories);
        $allCategories = $this->translator->translate($this->loadAllCategories($categories))->keyBy('id');

        $this->missingCache = [];

        $parentIds = $categories->groupBy('parentId')->keys()->filter()->toArray();

        // Generate list
        $categories = $categories->groupByParent($allCategories)
            ->keyByParent($allCategories)
            ->transformForParents($parents, $parentIds)
            ->pluckNames($divisions, [$this, 'identifier'])
            ->sortBy(fn(Collection $categories, $parent) => $parent);

        // Ensure categories with no parent are at the start
        $noParent = $categories->pull(CategoryList::NOPARENT, new Collection);
        $categories->prepend($noParent, CategoryList::NOPARENT);

        return $categories;
    }

    /**
     * @return mixed
     */
    public function identifier($category)
    {
        return $category->id;
    }

    /**
     * @return Collection
     */
    private function loadAllCategories(Collection $categories)
    {
        $missing = $categories->pluck('parentId')->filter()->unique()
            ->diff($categories->just('id'))->all();

        // We don't want to continue the recursion if the missing list is empty or the same as the previous one
        if (! $missing || $missing === $this->missingCache) {
            return $categories;
        }
        $this->missingCache = $missing;

        $categories = $categories->merge($this->repository->getByIds($missing));

        return $this->loadAllCategories($categories);
    }

    /**
     * @param  bool  $divisions  Include category divisions in the list?
     * @param  bool  $parents  Include parent categories?
     * @return string
     */
    public function toJson(Collection $categories, bool $divisions = false, bool $parents = true)
    {
        $categoryList = $this->generate($categories, $divisions, $parents);

        $noParent = $this->mapCategories($categoryList[CategoryList::NOPARENT]);

        unset($categoryList[CategoryList::NOPARENT]);

        $parent = $categoryList->map(function ($childCategories, $category) {
            return ['name' => $category, 'children' => $this->mapCategories($childCategories)];
        })->values()->toArray();

        return json_encode(array_merge($noParent, $parent));
    }

    /**
     * @return array
     */
    private function mapCategories(Collection $categories)
    {
        return $categories->map(function ($category, $id) {
            return ['id' => $id, 'name' => $category];
        })->values()->toArray();
    }
}
