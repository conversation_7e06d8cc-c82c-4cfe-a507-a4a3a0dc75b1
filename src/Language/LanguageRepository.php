<?php

namespace Platform\Language;

interface LanguageRepository
{
    /**
     * Return all of the languages available on the system.
     *
     * @return array
     */
    public function getAll();

    /**
     * Retrieves a language based on its language code.
     *
     * @param  string  $code
     * @return mixed
     */
    public function getByCode($code);

    /**
     * Retrieve a collection of languages based on language codes
     *
     * @return mixed
     */
    public function getByCodes(array $codes);
}
