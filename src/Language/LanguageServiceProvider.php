<?php

namespace Platform\Language;

use Illuminate\Support\ServiceProvider;

class LanguageServiceProvider extends ServiceProvider
{
    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->registerLanguages();
        $this->registerLanguageConfig();
    }

    private function registerLanguages()
    {
        $languages = $this->app['config']->get('languages', []);

        $this->app->singleton(LanguageRepository::class, function ($app) use ($languages) {
            return new ConfigLanguageRepository($languages);
        });

        Language::setLanguages($languages);
    }

    private function registerLanguageConfig()
    {
        $this->app->singleton('languageconfig', LanguageConfig::class);
    }
}
