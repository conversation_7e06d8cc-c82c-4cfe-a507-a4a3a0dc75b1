<?php

namespace Platform\Language;

class Language
{
    /**
     * Stores the languages that are supported by the system.
     *
     * @var array
     */
    private static $languages;

    /**
     * The language code.
     *
     * @var string
     */
    public $code;

    /**
     * The full, readable version of the language.
     *
     * @var string
     */
    public $language;

    /**
     * Create a new language instance, based on the language code provided.
     *
     * @param  string  $code
     *
     * @throws UnsupportedLanguage
     */
    public function __construct($code)
    {
        if (! array_key_exists($code, static::$languages)) {
            throw new UnsupportedLanguage($code);
        }

        $this->code = $code;
        $this->language = static::$languages[$code];
    }

    /**
     * Determines whether one language instance is equal to another.
     */
    public function equals(Language $language): bool
    {
        return $language == $this;
    }

    /**
     * Returns the code for the language object.
     */
    public function code(): string
    {
        return $this->code;
    }

    /**
     * Returns the full string of the language.
     */
    public function language(): string
    {
        return $this->language;
    }

    /**
     * Set the available languages for future use.
     */
    public static function setLanguages(array $languages)
    {
        static::$languages = $languages;
    }
}
