<?php

namespace Platform\Language;

use Illuminate\Contracts\Config\Repository as Config;

class LanguageConfig
{
    /** @var Config */
    private $config;

    /** @var array */
    private $services = [
        'funcaptcha',
        'recaptcha',
        'carbon',
        'moment',
        'select2',
    ];

    public function __construct(Config $config)
    {
        $this->config = $config;
    }

    /**
     * @param  string  $languageCode
     * @return bool
     */
    public function rtl($languageCode)
    {
        if ($this->config->has('lang.config.'.$languageCode.'.rtl')) {
            return $this->config->get('lang.config.'.$languageCode.'.rtl');
        }

        return false;
    }

    /**
     * @param  string  $languageCode
     * @return string
     */
    public function system($languageCode)
    {
        if ($this->config->has('lang.config.'.$languageCode.'.system')) {
            return $this->config->get('lang.config.'.$languageCode.'.system');
        }

        return $languageCode.'.utf8';
    }

    /**
     * @param  string  $service
     * @param  array  $arguments
     * @return string|null
     */
    public function __call($service, $arguments)
    {
        $languageCode = $arguments[0] ?? '';

        if (! in_array($service, $this->services) || ! $languageCode) {
            return null;
        }

        if ($this->config->has('lang.config.'.$languageCode.'.'.$service)) {
            return $this->config->get('lang.config.'.$languageCode.'.'.$service);
        }

        return $this->config->get('lang.'.$languageCode);
    }
}
