<?php

namespace Platform\Language;

use Illuminate\Support\Collection;

class ConfigLanguageRepository implements LanguageRepository
{
    /**
     * Stores an array of all supported languages on the system.
     *
     * @var array
     */
    private $languages;

    public function __construct(array $languages)
    {
        $this->languages = $this->hydrateCollection($languages);
    }

    /**
     * Return all available languages.
     *
     * @return array
     */
    public function getAll()
    {
        return $this->languages;
    }

    /**
     * Retrieves a language based on its language code.
     *
     * @param  string  $code
     * @return Language
     */
    public function getByCode($code)
    {
        return new Language($code);
    }

    /**
     * Hydrates a collection object with a list of language objects.
     *
     * @param  array  $languages  key-value pair array of language codes to language names.
     * @return Collection
     */
    private function hydrateCollection(array $languages)
    {
        $items = [];

        foreach ($languages as $code => $language) {
            $items[] = new Language($code);
        }

        return new Collection($items);
    }

    /**
     * Retrieve a collection of languages based on language codes
     *
     * @return mixed
     */
    public function getByCodes(array $codes)
    {
        return new Collection(array_map(function ($code) {
            return $this->getByCode($code);
        }, $codes));
    }
}
