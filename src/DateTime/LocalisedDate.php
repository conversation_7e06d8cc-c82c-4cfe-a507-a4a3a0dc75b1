<?php

namespace Platform\DateTime;

use Carbon\Carbon;
use Carbon\CarbonInterface;

class LocalisedDate extends BaseDateTime
{
    public static function createFromTimestamp(string $timestamp, string $locale): LocalisedDate
    {
        return new self(
            Carbon::createFromTimestamp($timestamp),
            $locale
        );
    }

    public static function create(CarbonInterface $dateTime, string $locale, ?string $timezone = null): LocalisedDate
    {
        return new self(
            $dateTime,
            $locale,
            $timezone
        );
    }

    public function format(?string $format = null): string
    {
        if (! empty($format)) {
            return $this->dateTime->isoFormat($format);
        }

        $dateFormat = config('lang.moment-formats.'.$this->locale.'.date');

        return $this->dateTime->isoFormat($dateFormat);
    }

    public function formatLocalisedDate(): string
    {
        $dateFormat = config('lang.moment-formats.'.($this->locale).'.date');

        if (! empty($timezone = $this->timezone)) {
            $this->dateTime->setTimezone($timezone);
        }

        return $this->dateTime->isoFormat($dateFormat);
    }
}
