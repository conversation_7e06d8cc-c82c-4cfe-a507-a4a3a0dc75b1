<?php

namespace Platform\DateTime;

use Carbon\Carbon;
use Carbon\CarbonInterface;

class LocalisedDateTime extends BaseDateTime
{
    public static function create(CarbonInterface $dateTime, string $locale, ?string $timezone = null): LocalisedDateTime
    {
        return new self(
            $dateTime,
            $locale,
            $timezone
        );
    }

    public static function createFromTimestamp(string $timestamp, string $locale): LocalisedDateTime
    {
        return new self(
            Carbon::createFromTimestamp($timestamp),
            $locale
        );
    }

    public function format(?string $format = null): string
    {
        if (! empty($format)) {
            return $this->dateTime->isoFormat($format);
        }

        $dateFormat = config('lang.moment-formats.'.$this->locale.'.date');
        $timeFormat = config('lang.moment-formats.'.$this->locale.'.time');

        return $this->dateTime->isoFormat($dateFormat.' '.$timeFormat);
    }
}
