<?php

namespace Platform\Bus\Commands;

use Platform\Events\EventDispatcher;

class ArchiveModelsCommandHandler
{
    use EventDispatcher;

    public function handle(ArchiveModelsCommand $command)
    {
        $toArchive = $command->repository->getByIds(array_unique($command->ids));

        $command->repository->archive($toArchive);

        $events = $toArchive->reduce(function ($events, $model) {
            return array_merge($events, $model->releaseEvents());
        }, []);

        $this->dispatch($events);
    }
}
