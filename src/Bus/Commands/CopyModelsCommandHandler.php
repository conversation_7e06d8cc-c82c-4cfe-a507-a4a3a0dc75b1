<?php

namespace Platform\Bus\Commands;

use Platform\Events\EventDispatcher;

class CopyModelsCommandHandler
{
    use EventDispatcher;

    public function handle(CopyModelsCommand $command)
    {
        $models = $command->repository->copyByIds($command->ids);

        $events = $models->reduce(function ($events, $model) {
            return array_merge($events, $model->releaseEvents());
        }, []);

        $this->dispatch($events);
    }
}
