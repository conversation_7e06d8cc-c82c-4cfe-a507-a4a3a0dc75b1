<?php

namespace Platform\Bus\Commands;

use Platform\Events\EventDispatcher;

class DeleteModelsCommandHandler
{
    use EventDispatcher;

    /**
     * Deletes the selected chapters.
     */
    public function handle(DeleteModelsCommand $command)
    {
        $ids = array_unique($command->ids);

        foreach ($ids as $id) {
            $model = $command->repository->getById($id);

            if ($model) {
                $command->repository->delete($model);

                $this->dispatch($model->releaseEvents());
            }
        }
    }
}
