<?php

namespace Platform\Bus\Commands;

use Platform\Events\EventDispatcher;

class UndeleteModelsCommandHandler
{
    use EventDispatcher;

    public function handle(UndeleteModelsCommand $command)
    {
        $toRestore = $command->repository->getTrashedByIds($command->selected);

        $command->repository->restore($toRestore);

        $events = $toRestore->reduce(function ($events, $model) {
            return array_merge($events, $model->releaseEvents());
        }, []);

        $this->dispatch($events);
    }
}
