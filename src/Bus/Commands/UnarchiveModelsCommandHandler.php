<?php

namespace Platform\Bus\Commands;

use Platform\Events\EventDispatcher;

class UnarchiveModelsCommandHandler
{
    use EventDispatcher;

    public function handle(UnarchiveModelsCommand $command)
    {
        $toUnarchive = $command->repository->getArchivedByIds(array_unique($command->ids));

        $command->repository->unarchive($toUnarchive);

        $events = $toUnarchive->reduce(function ($events, $model) {
            return array_merge($events, $model->releaseEvents());
        }, []);

        $this->dispatch($events);
    }
}
