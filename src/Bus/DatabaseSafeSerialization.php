<?php

namespace Platform\Bus;

use Platform\Database\Database;
use Platform\Database\Selector;

trait DatabaseSafeSerialization
{
    public ?string $database = null;

    protected function setDatabase(): void
    {
        $this->database = app(Selector::class)->current()->name();
    }

    protected function restoreDatabase(string $database): void
    {
        app(Selector::class)->select(new Database($database));
    }
}
