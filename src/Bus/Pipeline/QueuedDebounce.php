<?php

namespace Platform\Bus\Pipeline;

use Closure;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\Log;
use Platform\Bus\Debounce;

class QueuedDebounce
{
    use DispatchesJobs;
    use QueuedOrSync;

    public function handle($command, Closure $next)
    {
        if ($this->isSync($command) || ! uses_trait($command, Debounce::class)) {
            return $next($command);
        }

        $outdated = $command->debounceOutdated();
        $overdue = $command->debounceOverdue();

        if ($outdated && ! $overdue) {
            return Log::info('Debounce skipping outdated: '.get_class($command), [
                'debounce_count' => $command->incrementDebounceCount(),
            ]);
        }

        if ($command->debounceLocked()) {
            Log::info('Debounce delaying locked: '.get_class($command));
            if ($job = $command->retryDebounce()) {
                $this->dispatch($job);
            }

            return null;
        }

        $command->lockDebounce();
        $command->forgetDebounceCount();

        if (! $outdated) {
            $command->forgetDebouncePayload();
        }

        if ($overdue) {
            Log::info('Debounce overdue, processing: '.get_class($command));
        }

        try {
            return $next($command);
        } catch (\Throwable $exception) {
            throw $exception;
        } finally {
            $command->unlockDebounce();
        }
    }
}
