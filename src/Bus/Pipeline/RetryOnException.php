<?php

namespace Platform\Bus\Pipeline;

trait RetryOnException
{
    /**
     * @var int
     */
    private $retryAttempts = 0;

    public $delay;

    /**
     * Returns the number of retries the command is allowed to attempt
     */
    abstract protected function allowedRetries(): int;

    /**
     * Delay in seconds for the retry attempt, defaulting to between 1 - 5 minutes.
     * Can be overridden by a command that requires a different delay.
     */
    protected function retryDelay(): int
    {
        return random_int(60, 300);
    }

    public function remainingAttempts(): int
    {
        $remaining = $this->allowedRetries() - $this->retryAttempts;

        return $remaining > 0 ? $remaining : 0;
    }

    /**
     * @return RetryOnException
     */
    public function retry(): self
    {
        $this->retryAttempts++;
        $this->delay = $this->retryDelay();

        return $this;
    }
}
