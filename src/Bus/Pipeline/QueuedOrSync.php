<?php

namespace Platform\Bus\Pipeline;

use Illuminate\Contracts\Queue\ShouldQueue;

trait QueuedOrSync
{
    /**
     * Returns true if the command ShouldQueue and the queue driver isn't sync.
     *
     * @param  mixed  $command
     */
    protected function isQueued($command): bool
    {
        return config('queue.default') != 'sync' && $command instanceof ShouldQueue;
    }

    /**
     * Returns true if the command is running synchronously, either due to lack of ShouldQueue or the sync driver.
     *
     * @param  mixed  $command
     */
    protected function isSync($command): bool
    {
        return ! $this->isQueued($command);
    }
}
