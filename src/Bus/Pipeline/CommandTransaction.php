<?php

namespace Platform\Bus\Pipeline;

use Closure;
use Exception;
use Illuminate\Support\Facades\DB;

class CommandTransaction
{
    /**
     * @param  mixed  $command
     * @return mixed
     *
     * @throws Exception
     */
    public function handle($command, Closure $next)
    {
        if ($command instanceof DatabaseTransactions) {
            return DB::transaction(function () use ($command, $next) {
                return $next($command);
            });
        }

        return $next($command);
    }
}
