<?php

namespace Platform\Bus\Pipeline;

use Closure;
use Platform\Events\EventDispatcher;

class QueuedJobStatus
{
    use EventDispatcher;
    use QueuedOrSync;

    /**
     * @param  mixed  $command
     * @return mixed
     */
    public function handle($command, Closure $next)
    {
        if ($this->isSync($command)) {
            return $next($command);
        }

        if (method_exists($command, 'setProcessing')) {
            $command->setProcessing();
        }

        $response = $next($command);

        if (method_exists($command, 'setFinished')) {
            $command->setFinished();
        }

        return $response;
    }
}
