<?php

namespace Platform\Bus\Pipeline;

use Closure;
use Illuminate\Contracts\Bus\Dispatcher;
use Psr\Log\LoggerInterface as Log;

class QueuedRetryOnException
{
    use QueuedOrSync;

    /**
     * @var Log
     */
    private $log;

    /**
     * @var Dispatcher
     */
    private $dispatcher;

    public function __construct(\Psr\Log\LoggerInterface $log, Dispatcher $dispatcher)
    {
        $this->log = $log;
        $this->dispatcher = $dispatcher;
    }

    /**
     * @param  mixed  $command
     * @return mixed
     */
    public function handle($command, Closure $next)
    {
        if ($this->shouldRetry($command)) {
            return $this->attemptCommand($command, $next);
        }

        return $next($command);
    }

    /**
     * @param  mixed  $command
     * @return mixed
     *
     * @throws \Throwable
     */
    private function attemptCommand($command, Closure $next)
    {
        try {
            return $next($command);
        } catch (\Throwable $throwable) {
            if (! $command->remainingAttempts()) {
                throw $throwable;
            }

            $this->log->notice('Command '.class_basename($command).' failed, retrying...', [
                'command' => get_class($command),
                'remainingAttempts' => $command->remainingAttempts(),
                'throwable' => $throwable->getMessage(),
            ]);

            // Retry command, delayed for 5 minutes
            $this->dispatcher->dispatch($command->retry());
        }
    }

    private function shouldRetry($command): bool
    {
        return $this->isQueued($command)
            && in_array(RetryOnException::class, class_uses($command));
    }
}
