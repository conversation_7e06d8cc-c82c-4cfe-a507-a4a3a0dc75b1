<?php

namespace Platform\Bus\Pipeline;

use Illuminate\Contracts\Container\Container;

class CommandVerifier
{
    /**
     * @var Container
     */
    protected $app;

    public function __construct(Container $app)
    {
        $this->app = $app;
    }

    public function handle($command, $next)
    {
        $commandName = get_class($command);
        $verifierName = $commandName.'Verifier';

        if (class_exists($verifierName)) {
            $verifier = $this->app->make($verifierName);

            $verifier->verify($command);
        }

        return $next($command);
    }
}
