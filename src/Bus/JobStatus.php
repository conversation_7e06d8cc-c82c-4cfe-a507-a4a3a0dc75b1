<?php

namespace Platform\Bus;

use Illuminate\Support\Collection;
use Platform\Authorisation\Consumer;
use Platform\Events\EventDispatcher;
use Platform\Events\QueuedCommandHasFinished;

trait JobStatus
{
    use Debounce;
    use EventDispatcher;

    public function queueJobStatus()
    {
        $this->setQueued();
    }

    public function setQueued()
    {
        $this->updateJobStatusResource(function ($resource) {
            $resource->setJobStatus('queued');
        });
    }

    public function setProcessing()
    {
        $this->updateJobStatusResource(function ($resource) {
            $resource->setJobStatus('processing');
        });
    }

    public function setFinished()
    {
        $this->updateJobStatusResource(function ($resource) {
            if (method_exists($resource, 'fresh')) {
                $resource = $resource->fresh();
            }

            if (! $resource || $resource->getJobStatus() == 'queued') {
                return;
            }

            $resource->setJobStatus(null);

            if (! $resource instanceof JobStatusEvents) {
                $this->dispatch(new QueuedCommandHasFinished($this, $resource, $this->consumer()));
            }
        });
    }

    private function updateJobStatusResource(callable $callback)
    {
        $resource = $this->jobStatusResource();

        if (! $resource instanceof Collection) {
            $resource = new Collection(is_array($resource) ? $resource : [$resource]);
        }

        $resource->each($callback);
        $this->dispatch($resource->releaseEvents());
    }

    /**
     * Return the consumer that the command is for.
     *
     * @return Consumer
     */
    abstract public function consumer();

    /**
     * Return the JobStatus resource class, either a Model using JobStatusModel
     * or the CachedJobStatus class.
     *
     * @return mixed
     */
    abstract protected function jobStatusResource();
}
