<?php

namespace Platform\Bus;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\Queue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Platform\Bus\Pipeline\Accountable;

abstract class QueuedJob implements Accountable, ShouldQueue
{
    use Queueable;
    use SerializesModels;

    /**
     * @var mixed
     */
    public $dispatchedBy;

    /**
     * @return mixed
     */
    public function queue(Queue $queue)
    {
        if ($this->synchronous()) {
            return $this->pushToQueue($queue);
        }

        foreach (get_class_methods($this) as $method) {
            if ($this->startsWithQueue($method)) {
                $this->$method();
            }
        }

        return $this->pushToQueue($queue);
    }

    /**
     * Whenever a queued job is used, the job should specify who it was dispatched by.
     *
     * @return mixed
     */
    abstract protected function queueDispatchedBy();

    /**
     * @return mixed
     */
    private function pushToQueue(Queue $queue)
    {
        if (isset($this->delay)) {
            return $queue->later($this->delay, $this, '', $this->queue ?? null);
        }

        return $queue->push($this, '', $this->queue ?? null);
    }

    /**
     * Checks if the current queue processing environment is synchronous.
     */
    private function synchronous(): bool
    {
        return env('QUEUE_DRIVER') == 'sync';
    }

    /**
     * Checks if the method name starts with `queue` but isn't only `queue`
     */
    private function startsWithQueue(string $method): bool
    {
        return $method != 'queue' && starts_with($method, 'queue');
    }
}
