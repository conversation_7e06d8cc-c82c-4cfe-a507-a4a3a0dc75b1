<?php

namespace Platform\Bus;

use Illuminate\Support\Facades\Cache;
use Platform\Bus\Status\Messenger;

class CachedJobStatus implements Messenger
{
    /**
     * @var string
     */
    private $key;

    /**
     * @var string
     */
    private $rawKey;

    /**
     * @var int
     */
    private $accountId;

    /**
     * @var string
     */
    private $finishMessage;

    /**
     * @param  string  $key
     */
    public function __construct($key, int $accountId)
    {
        $this->rawKey = $key;
        $this->accountId = $accountId;

        $this->generateKey($key);
    }

    /**
     * @param  string  $status
     */
    public function setJobStatus($status)
    {
        if ($status) {
            Cache::put($this->key, $status, now()->addHours(6));
        } else {
            Cache::forget($this->key);
        }
    }

    /**
     * @return string
     */
    public function getJobStatus()
    {
        return Cache::get($this->key);
    }

    /**
     * @return bool
     */
    public function hasFinishedProcessing()
    {
        return ! $this->getJobStatus();
    }

    /**
     * @return string
     */
    public function getBroadcastKey()
    {
        return $this->rawKey;
    }

    public function getBroadcastRefreshToken()
    {
        return 'broadcast-'.md5($this->key);
    }

    public function setFinishMessage(string $message)
    {
        $this->finishMessage = $message;
    }

    /**
     * @return string|null
     */
    public function finishMessage()
    {
        return $this->finishMessage;
    }

    private function generateKey(string $key)
    {
        $this->key = $this->accountId.'.jobstatus.'.$key;
    }
}
