<?php

namespace Platform\Bus;

trait JobStatusModel
{
    public ?string $jobStatus = '';

    public function setJobStatus(?string $status)
    {
        if (empty($this->deletedAt)) {
            $this->jobStatus = $status;

            $this->save();
        }
    }

    /**
     * @return string
     */
    public function getJobStatus()
    {
        return $this->jobStatus;
    }

    /**
     * @return bool
     */
    public function hasFinishedProcessing()
    {
        return ! $this->jobStatus;
    }

    /**
     * @return string
     */
    public function getBroadcastKey()
    {
        return strtolower(class_basename($this));
    }

    /**
     * @return string
     */
    public function getBroadcastName()
    {
        if (method_exists($this, 'getTranslatableFields')) {
            $fields = $this->getTranslatableFields();

            if (in_array('title', $fields)) {
                return $this->title;
            }

            if (in_array('name', $fields)) {
                return $this->name;
            }
        }

        return object_get($this, 'title') ?: object_get($this, 'slug');
    }

    public function getBroadcastRefreshToken()
    {
        return 'broadcast-'.md5($this->getBroadcastKey().'-'.$this->getKey());
    }
}
