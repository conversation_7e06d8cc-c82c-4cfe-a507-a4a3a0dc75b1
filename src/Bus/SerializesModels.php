<?php

namespace Platform\Bus;

trait SerializesModels
{
    use DatabaseSafeSerialization;
    use \Illuminate\Queue\SerializesModels {
        __unserialize as parentUnserialize;
        __serialize as parentSerialize;
    }

    public function __serialize()
    {
        $this->setDatabase();

        return $this->parentSerialize();
    }

    public function __unserialize(array $values)
    {
        $this->restoreDatabase($values['database']);
        $this->parentUnserialize($values);
    }
}
