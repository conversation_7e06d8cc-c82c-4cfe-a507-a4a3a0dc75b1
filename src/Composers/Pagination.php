<?php

namespace Platform\Composers;

use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;

class Pagination
{
    /**
     * @var Request
     */
    protected $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public function compose(View $view)
    {
        $view->paginator->appends($this->request->except(['_pjax']))
            ->setPath(prefix($this->request->path(), '/'));

        [$start, $end] = $this->paginatorLimits(
            $view->paginator->currentPage(),
            $view->paginator->lastPage()
        );

        $view->with('start', $start);
        $view->with('end', $end);
    }

    /**
     * Calculates the first and last page to display in the paginator, limiting the total amount of pages to 10.
     *
     * @return array
     */
    protected function paginatorLimits($current, $last)
    {
        if ($current < 7) {
            $end = min($last, 10);
        } elseif ($last - $current > 4) {
            $end = $current + 4;
        } else {
            $end = $last;
        }

        $start = max($end - 9, 1);

        return [$start, $end];
    }
}
