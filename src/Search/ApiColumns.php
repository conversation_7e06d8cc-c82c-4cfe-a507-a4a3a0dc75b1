<?php

namespace Platform\Search;

trait ApiColumns
{
    /**
     * @var ApiVisibility
     */
    private $apiVisibility;

    /**
     * @return self
     */
    public function setApiVisibility(ApiVisibility $apiVisibility)
    {
        $this->apiVisibility = $apiVisibility;

        return $this;
    }

    public function columns(Defaults $view): Columns
    {
        if ($this->apiVisibility) {
            return $this->apiColumns($this->apiVisibility);
        }

        return parent::columns($view);
    }
}
