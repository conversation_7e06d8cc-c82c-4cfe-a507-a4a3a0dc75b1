<?php

namespace Platform\Search\View;

use Illuminate\Http\Request;
use Platform\Search\Defaults;
use Platform\Search\Services\Factory;
use Platform\View\View;

class Columnator extends View
{
    /** @var Request */
    private $request;

    /** @var Factory */
    private $columnatorFactory;

    public function __construct(Request $request, Factory $columnatorFactory)
    {
        $this->request = $request;
        $this->columnatorFactory = $columnatorFactory;
    }

    /**
     * @return string
     */
    public function area()
    {
        return $this->request->get('area');
    }

    /**
     * If true, then "Edit text" Vue component is allowed to be returned as column title.
     * If false, plain text will be returned.
     *
     * @return bool
     */
    public function allowHtml()
    {
        return (bool) $this->request->get('html', true);
    }

    /**
     * @return \Platform\Search\Columnator
     */
    public function columnator()
    {
        return $this->columnatorFactory->forArea($this->area, $this->request->query('filters', []));
    }

    public function columns(): array
    {
        return $this->columnator->columns(new Defaults('search'))->map(function ($column) {
            return $column->name();
        })->values()->toArray();
    }

    public function availableColumns(): array
    {
        return $this->columnator->availableColumns()->filter(function ($column) {
            return ! $column->fixed() && $column->visible();
        })->keyBy(function ($column) {
            return $column->name();
        })->map(function ($column) {
            return $this->allowHtml() ? $column->title() : strip_tags($column->title());
        })->sort()->toArray();
    }

    public function fixedColumns(): array
    {
        return $this->columnator->availableColumns()->filter(function ($column) {
            return $column->fixed();
        })->toArray();
    }
}
