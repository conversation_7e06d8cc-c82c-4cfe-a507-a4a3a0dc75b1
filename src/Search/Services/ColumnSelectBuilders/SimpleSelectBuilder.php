<?php

namespace Platform\Search\Services\ColumnSelectBuilders;

use Illuminate\Database\Query\Expression;
use Illuminate\Support\Facades\DB;
use Platform\Search\Column;

/**
 * unnamed simple Selector
 */
class SimpleSelectBuilder implements SelectBuilder
{
    private $data;

    /**
     * data is a simple Column object
     *
     * @param  mixed  $data
     */
    public function add($data)
    {
        $this->data = $data;
    }

    /**
     * apply this selector to the query
     */
    public function apply($query)
    {
        if ($this->data instanceof Column) {
            // Not all columns represent a field, so should be ignored.
            if ($this->data->field() !== null) {
                $this->addSelectIfNotExists($query, $this->data->field());
            }
        } else {
            $this->addSelectIfNotExists($query, $this->data);
        }
    }

    private function addSelectIfNotExists($query, $selects)
    {
        if (! is_array($selects)) {
            $selects = [$selects];
        }
        foreach ($selects as $select) {
            if (! $this->selectExists($query, $select)) {
                $query->addSelect($select);
            }
        }
    }

    private function selectExists($query, $select)
    {
        if ($select instanceof Expression) {
            foreach ($query->getQuery()->columns ?? [] as $column) {
                if ($column instanceof Expression && $select->getValue(DB::connection()->getQueryGrammar()) === $column->getValue(DB::connection()->getQueryGrammar())) {
                    return true;
                }
            }
        } else {
            return in_array($select, $query->getQuery()->columns ?? []);
        }
    }
}
