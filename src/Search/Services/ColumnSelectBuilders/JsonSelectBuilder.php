<?php

namespace Platform\Search\Services\ColumnSelectBuilders;

/**
 * this is aggregating selector
 * ( i.e. it wrapps several columns into one )
 */
class JsonSelectBuilder implements SelectBuilder
{
    private $data = [];

    /**
     * add a key => value pair to the json column
     */
    public function add(string $key, string $value): void
    {
        $this->data[] = $key;
        $this->data[] = $value;
    }

    /**
     * apply selector to query by forming it with aggregating functiom
     */
    public function apply($query, $name): void
    {
        $query->selectRaw('CAST(JSON_OBJECT('.implode(',', $this->data).") AS JSON) AS '$name'");
    }
}
