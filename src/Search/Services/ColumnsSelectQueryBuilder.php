<?php

namespace Platform\Search\Services;

use Platform\Search\Columns\HasSelectStrategy;
use Platform\Search\Services\ColumnSelectBuilders\SelectBuilder;
use Platform\Search\Services\ColumnSelectBuilders\SimpleSelectBuilder;
use Platform\Search\Services\ColumnSelectStrategies\SimpleSelectStrategy;

class ColumnsSelectQueryBuilder
{
    private $anonymous = [];

    private $named = [];

    /**
     * ColumnsSelectQueryBuilder constructor.
     *
     * @param  array  $anonymous
     */
    private function __construct(array $columns)
    {
        foreach ($columns as $key => $column) {
            $strategy = $column instanceof HasSelectStrategy ? $column->selectStrategy() : new SimpleSelectStrategy($column);
            $strategy->applyTo($this);
        }
    }

    public static function forColumns(?array $columns = []): ColumnsSelectQueryBuilder
    {
        return new static($columns);
    }

    /**
     * get a Selector object or a named singleton
     */
    public function get(?string $name = null, string $class = SimpleSelectBuilder::class): SelectBuilder
    {
        if ($name === null) {
            return $this->anonymous[] = new $class();
        }

        if ($this->named[$name] ?? 0) {
            return $this->named[$name];
        }

        return $this->named[$name] = new $class();
    }

    /**
     * apply all selectors to the query
     *
     * @return mixed
     */
    public function applyTo($query)
    {
        $selects = array_merge($this->anonymous, $this->named);
        foreach ($selects as $name => $select) {
            $select->apply($query, $name);
        }

        return $query;
    }
}
