<?php

namespace Platform\Search\Services;

use Carbon\Carbon;
use Platform\Search\ApiVisibility;
use Platform\Search\Columnator;
use Platform\Search\Defaults;
use Platform\Search\Filters\Dates\DateFilterType;
use Platform\Search\Filters\SearchFilterValidation;
use Symfony\Component\HttpKernel\Exception\HttpException;

trait SearchFilterValidator
{
    /**
     * Validates if filters are allowd for the API endpoint
     */
    protected function validateFiltersAllowed(array $requestQueryParameters, ApiVisibility $apiVisibility)
    {
        if ($apiVisibility->matches(new ApiVisibility('view')) && ! empty($requestQueryParameters)) {
            throw new HttpException(400, 'Filters not allowed for single resource endpoints.');
        }
    }

    /**
     * Validates filter names and values
     *
     * @return array
     */
    protected function validateFilterNamesAndValues(Columnator $columnator, array $requestFilters, ApiVisibility $scope)
    {
        $invalidFilterNames = [];
        $invalidFilterValueMessages = [];

        $filters = $columnator->filters(new Defaults('search'))->filter(function ($filter) {
            return $filter instanceof SearchFilterValidation;
        });

        foreach ($requestFilters as $requestFilterName => $requestFilterValues) {
            if (! is_array($requestFilterValues)) {
                $requestFilterValues = [$requestFilterValues];
            }

            $filterNameValid = false;

            foreach ($requestFilterValues as $requestFilterValue) {
                foreach ($filters as $filter) {
                    if ($filter->validateFilterName($requestFilterName) && $filter->validateApiScope($scope)) {
                        $filterNameValid = true;
                        // Only validate value if name is valid
                        try {
                            $filter->validateFilterValue($requestFilterName, $requestFilterValue);
                        } catch (HttpException $e) {
                            $invalidFilterValueMessages[$requestFilterName] = $e->getMessage();
                        }
                    }
                }
            }

            if (! $filterNameValid) {
                array_push($invalidFilterNames, $requestFilterName);
            }
        }

        return $this->generateFilterNamesAndValuesValidationResponse($invalidFilterNames, $invalidFilterValueMessages);
    }

    /**
     * Generates error response for filter name and value validation
     *
     * @return array
     */
    private function generateFilterNamesAndValuesValidationResponse(array $invalidFilterNames, array $invalidFilterValueMessages)
    {
        $errors = [];

        if (! empty($invalidFilterNames)) {
            $errors['invalid_filter_names'] = implode(', ', $invalidFilterNames);
        }

        if (! empty($invalidFilterValueMessages)) {
            $errors['invalid_filter_values'] = $invalidFilterValueMessages;
        }

        return ! empty($errors) ? response()->json([
            'message' => 'Invalid filter names and / or values.',
            'errors' => $errors,
            'status_code' => 400,
        ], 400) : null;
    }

    /**
     * Validates if filter value exists in array of valid values
     */
    protected function validateValueInArray(string $filterName, string $filterValue, array $options)
    {
        if (! in_array($filterValue, $options)) {
            throw new HttpException(400, 'Value must be one of the following: ['.implode(', ', $options)."]. You provided [{$filterValue}].");
        }
    }

    /**
     * Validate if filter value is a number
     */
    protected function validateValueIsNumeric(string $filterName, string $filterValue)
    {
        if (! is_numeric($filterValue)) {
            throw new HttpException(400, "Value must be a number. You provided [{$filterValue}].");
        }
    }

    /**
     * Validate if filter value is between two values
     */
    protected function validateValueIsBetween(string $filterName, int $filterValue, int $minValue, int $maxValue)
    {
        if ($filterValue < $minValue || $filterValue > $maxValue) {
            throw new HttpException(400, "Value must be between {$minValue} and {$maxValue}. You provided [{$filterValue}].");
        }
    }

    /**
     * Validate if filter value is between two values
     */
    protected function validateValueIsPositiveNumber(string $filterName, int $filterValue)
    {
        if ($filterValue < 1) {
            throw new HttpException(400, "Value must be a positive number. You provided [{$filterValue}].");
        }
    }

    /**
     * Validate if filter value is a valid slug
     */
    protected function validateValueIsSlug(string $filterName, string $filterValue)
    {
        if (! validate_slug($filterValue)) {
            throw new HttpException(400, "Value is not a valid slug. Must contain only letters and be exactly 8 characters long. You provided [{$filterValue}].");
        }
    }

    protected function validateValueDatesAreZuluDate(string $filterName, string $filterValue, DateFilterType $dateFilter)
    {
        $format = 'Y-m-d\TH:i:s\Z';
        $dates = explode(',', trim($filterValue, '[]'));
        throw_if($dateFilter === DateFilterType::Between && count($dates) != 2, new HttpException(400, "Value must be filled with two dates. You provided: $filterValue"));

        try {
            foreach ($dates as $stringDate) {
                Carbon::createFromFormat($format, $stringDate);
            }
        } catch (\Exception $e) {
            $filterValueString = implode(',', $dates);
            throw new HttpException(400, "Value is not a valid date. Must be in the format $format. You provided [$filterValueString].");
        }
    }
}
