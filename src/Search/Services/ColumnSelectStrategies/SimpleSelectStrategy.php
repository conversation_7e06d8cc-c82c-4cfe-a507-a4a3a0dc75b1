<?php

namespace Platform\Search\Services\ColumnSelectStrategies;

use Platform\Search\Services\ColumnsSelectQueryBuilder;

class SimpleSelectStrategy extends SelectStrategy
{
    private $data;

    /**
     * SimpleSelectStrategy constructor.
     */
    public function __construct($data)
    {
        $this->data = $data;
    }

    public function applyTo(ColumnsSelectQueryBuilder $builder)
    {
        $builder->get()->add($this->data);
    }
}
