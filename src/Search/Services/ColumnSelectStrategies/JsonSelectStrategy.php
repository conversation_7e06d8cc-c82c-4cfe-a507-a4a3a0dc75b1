<?php

namespace Platform\Search\Services\ColumnSelectStrategies;

use Platform\Search\Services\ColumnSelectBuilders\JsonSelectBuilder;
use Platform\Search\Services\ColumnSelectBuilders\SelectBuilder;

abstract class JsonSelectStrategy extends AggregatedSelectStrategy
{
    protected function getBuilderClass(): string
    {
        return JsonSelectBuilder::class;
    }

    protected function addToBuilder(SelectBuilder $builder, $data)
    {
        foreach ($data as $slug => $select) {
            $builder->add($slug, $select);
        }
    }
}
