<?php

namespace Platform\Search\Services\ColumnSelectStrategies;

use Platform\Search\Services\ColumnSelectBuilders\SelectBuilder;
use Platform\Search\Services\ColumnsSelectQueryBuilder;

abstract class AggregatedSelectStrategy extends SelectStrategy
{
    public function applyTo(ColumnsSelectQueryBuilder $builder)
    {
        foreach ($this->getNames() as $name) {
            $this->addToBuilder(
                $builder->get($name, $this->getBuilderClass()),
                $this->getData($name)
            );
        }
    }

    abstract protected function getBuilderClass(): string;

    abstract protected function getData($name);

    abstract protected function getNames(): array;

    abstract protected function addToBuilder(SelectBuilder $builder, $data);
}
