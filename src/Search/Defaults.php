<?php

namespace Platform\Search;

use Assert\Assertion;

class Defaults
{
    /**
     * @var string
     */
    private $view;

    public function __construct(string $view)
    {
        Assertion::inArray($view, ['search', 'export', 'none', 'all']);

        $this->view = $view;
    }

    /**
     * Returns true if the the comparing view matches this one. A match in this case occurs when:
     *
     * - Both views have the same value or
     * - One view has 'all' and the other has 'export' or 'search'
     *
     * @return bool
     */
    public function matches(Defaults $that)
    {
        return $this->view == $that->view ||
            ($this->view == 'all' && $that->view != 'none') ||
            ($this->view != 'none' && $that->view == 'all');
    }
}
