<?php

namespace Platform\Search;

use Illuminate\Contracts\Pagination\Paginator as PaginatorContract;
use Illuminate\Support\Collection;
use Platform\Search\Enhancers\ExportEnhancer;
use Platform\Search\Services\SearchFilterValidator;
use Tectonic\LaravelLocalisation\Facades\Translator;

class ColumnatorSearch
{
    use SearchFilterValidator;

    protected bool $useEnhancers = true;

    protected int $exportLimit = 500;

    public function __construct(protected Columnator $columnator) {}

    public function setExportLimit(int $exportLimit)
    {
        $this->exportLimit = $exportLimit;
    }

    /**
     * For list view.
     *
     * @return Collection
     */
    public function search(bool $paginate = true): Collection|PaginatorContract
    {
        $results = $this->getResults($paginate, $view = new Defaults('search'));

        if (! $this->useEnhancers) {
            return $results;
        }

        return $this->enhanceResults($results, $view);
    }

    public function searchLimit(int $limit = 100, int $offset = 0): Collection|PaginatorContract
    {
        $results = $this->getResultsLimit($view = new Defaults('search'), $limit, $offset);

        if (! $this->useEnhancers) {
            return $results;
        }

        return $this->enhanceResults($results, $view);
    }

    /**
     * When $resultLimit is present, we still parse chunks of export limit but only until $totalCount < $resultLimit
     *
     * @return \Generator
     */
    public function export($offset = 0, $resultLimit = null)
    {
        $filters = $this->columnator->filters($view = new Defaults('export'));
        $totalCount = 0;
        while ((is_null($resultLimit) || $totalCount < $resultLimit) &&
            $count = count($results = $this->columnator->repository()->getByFiltersForExport($filters, $offset, $this->exportLimit))
        ) {
            $totalCount += $count;
            $offset += $this->exportLimit;
            yield Translator::translate($this->enhanceResultsForExport($results, $view));
        }
    }

    /**
     * @return Collection
     */
    public function fullSearch(bool $paginate = true)
    {
        $results = $this->getResults($paginate, $view = new Defaults('export'));

        if (! $this->useEnhancers) {
            return $results;
        }

        return $this->enhanceResults($results, $view);
    }

    /**
     * If false, no enhancers will be used in the search.
     */
    public function useEnhancers(bool $useEnhancers = true): ColumnatorSearch
    {
        $this->useEnhancers = $useEnhancers;

        return $this;
    }

    private function enhanceResultsForExport($results, Defaults $view)
    {
        foreach ($this->columnator->enhancers($view) as $enhancer) {
            $enhancer instanceof ExportEnhancer ? $enhancer->enhanceForExport($results) : $enhancer->enhance($results);
        }

        return $results;
    }

    /**
     * Run search results through every search enhancer configured for this columnator;
     *
     * @return Collection
     */
    private function enhanceResults($results, Defaults $view)
    {
        foreach ($this->columnator->enhancers($view) as $enhancer) {
            $enhancer->enhance($results);
        }

        return $results;
    }

    /**
     * Gets List / Detailed view API search results
     *
     * @return mixed
     */
    public function apiSearch(array $requestQueryParameters, ApiVisibility $apiVisibility, bool $paginate)
    {
        // Validate filters
        if ($apiFilterErrors = $this->validateApiFilters($requestQueryParameters, $apiVisibility)) {
            return $apiFilterErrors;
        }

        // Search
        $searchResults = $this->search();

        // Return empty results
        if ($searchResults->isEmpty()) {
            return $paginate ? new LengthAwareApiPaginator([], 0, 1) : null;
        }

        // Translate results
        $searchResults = $searchResults->setCollection(translate(collect($searchResults->items())));

        // Grab API columns
        $columns = $this->columnator->apiColumns($apiVisibility);

        // Transform results
        $mappedSearchResults = $searchResults->map(function ($resource) use ($columns) {
            return $this->mapApiField($resource, $columns);
        });

        // Return paginated collection for list view and single entity for detailed view
        if ($paginate) {
            return (new LengthAwareApiPaginator(
                $mappedSearchResults,
                $searchResults->total(),
                $searchResults->perPage(),
                $searchResults->currentPage(),
                $searchResults->getOptions()
            ))->appends($requestQueryParameters);
        }

        return $mappedSearchResults->first();
    }

    /**
     * Validate if filters allowed and if filter names and values are valid
     *
     * @return array|null
     */
    private function validateApiFilters(array $requestQueryParameters, ApiVisibility $apiVisibility)
    {
        $this->validateFiltersAllowed($requestQueryParameters, $apiVisibility);

        return $this->validateFilterNamesAndValues($this->columnator, $requestQueryParameters, $apiVisibility) ?? null;
    }

    /**
     * Map API field into custom structure
     *
     * @return array
     */
    private function mapApiField($resource, $columns)
    {
        $result = [];

        foreach ($columns as $column) {
            $result[$column->apiName()] = $column->apiValue($resource);
        }

        ksort($result);

        return $result;
    }

    /**
     * Returns the columnator input, AKA filters
     *
     * @return array
     */
    public function input()
    {
        return $this->columnator->input();
    }

    protected function getResults($paginate, $view)
    {
        return $this->columnator->repository()
            ->getByFilters($this->columnator->filters($view), $paginate);
    }

    protected function getResultsLimit($view, int $limit, int $offset)
    {
        return $this->columnator->repository()
            ->getByFiltersLimit($this->columnator->filters($view), $limit, $offset);
    }
}
