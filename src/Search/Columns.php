<?php

namespace Platform\Search;

use Illuminate\Support\Collection;
use Platform\Search\Columns\HasEagerLoadedRelations;
use Platform\Search\Filters\IncludeFilter;

class Columns extends Collection
{
    /**
     * Return a collection of columns based on the settings array provided.
     *
     * The array should follow the proposed format:
     *
     *   [
     *      ['column' => 'column name']
     *   ]
     *
     * @return Columns
     */
    public function fromSettings(array $settings)
    {
        return (new static(array_map(function ($setting) {
            return $this->first(function (Column $column) use ($setting) {
                return $column->name() == $setting;
            });
        }, $settings)))
            // the filter call ensures that fields that were saved in settings but deleted,
            // are ignored when loading columns from those settings.
            ->filter();
    }

    /**
     * Returns all dependencies for all columns in the collection.
     */
    public function dependencies(): Collection
    {
        return $this->flatMap(function (Column $column) {
            return $column->dependencies();
        })->unique();
    }

    /**
     * Returns all fixed columns from the collection.
     *
     * @param  bool  $fixed
     * @return Columns
     */
    public function fixed($fixed = true)
    {
        return $this->filter(function (Column $column) use ($fixed) {
            return ! $fixed ? ! $column->fixed() : $column->fixed();
        });
    }

    /**
     * Returns all columns that are not fixed.
     *
     * @return Columns
     */
    public function dynamic()
    {
        return $this->fixed(false);
    }

    /**
     * Returns all registered columns that are only of the type FieldColumn.
     *
     * @param  string  $foreignId
     */
    public function fieldColumns($foreignId): Columns
    {
        return $this->filter(function ($column) use ($foreignId) {
            return $column instanceof FieldColumn && $column->foreignId() == $foreignId;
        });
    }

    /**
     * @param  array|string  $names
     * @return Columns
     */
    public function translatedColumns($names)
    {
        return $this->filter(function (Column $column) use ($names) {
            return $column instanceof TranslatedColumn && in_array($column->name(), (array) $names);
        });
    }

    public function resolveRelations(): Collection
    {
        return $this->filter(fn (Column $column) => $column instanceof HasEagerLoadedRelations && $column->relations()->isNotEmpty())
            ->map(fn (HasEagerLoadedRelations $column) => $column->relations())
            ->toBase()
            ->flatten();
    }
}
