<?php

namespace Platform\Search\Validators;

use Platform\Search\Services\Factory;

class ValidColumns
{
    private $factory;

    public function __construct(Factory $factory)
    {
        $this->factory = $factory;
    }

    /**
     * @return bool
     */
    public function validColumns($attribute, $columns, $parameters, $validator)
    {
        $columns = is_array($columns) ? $columns : explode(',', $columns);
        $area = $parameters[0] ?? null;

        if (! $area) {
            return false;
        }

        $allColumns = $this->factory->forArea($area, $validator->getValue('filters') ?? [])
            ->availableColumns()
            ->map->name()
            ->toArray();

        return ! array_diff($columns, $allColumns);
    }
}
