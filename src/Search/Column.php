<?php

namespace Platform\Search;

use Illuminate\Support\Collection;

interface Column
{
    /**
     * Title for the column - used for headers.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function title();

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string;

    /**
     * Returns the column's search filter dependencies.
     */
    public function dependencies(): Collection;

    /**
     * Returns the name of the field in the query that should be present.
     *
     * @return string|null
     */
    public function field();

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record);

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record);

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults;

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool;

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool;

    /**
     * Give columns with particularly important information a higher visibility priority.
     */
    public function priority(): int;

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool;
}
