<?php

namespace Platform\Search\Columns;

use HTML;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Archived extends Generic
{
    use DateColumn;

    /**
     * Should work for most columnators. Otherwise use the default constructor.
     *
     * @return Archived
     */
    public static function forResource(string $resource, $dateLocale = 'international')
    {
        $column = new self(trans('search.columns.archived'), "{$resource}.archived", "{$resource}.archived_at");
        $column->setDateLocale($dateLocale);

        return $column;
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        if ($record->archivedAt) {
            return HTML::localisedDateTime($record->archivedAt, $this->getDateLocale());
        }
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        if ($record->archivedAt) {
            return HTML::relativeTime($record->archivedAt);
        }
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('none');
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }
}
