<?php

namespace Platform\Search\Columns;

use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;

abstract class Generic implements Column
{
    /** @var string */
    private $title;

    /** @var string */
    private $name;

    /** @var string */
    private $field;

    public function __construct(string $title, string $name, string $field)
    {
        $this->title = $title;
        $this->name = $name;
        $this->field = $field;
    }

    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        return $this->title;
    }

    /**
     * Name of the column. This should be globally unique.
     */
    public function name(): string
    {
        return $this->name;
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return collect();
    }

    /**
     * Returns the name of the field in the query that should be present
     *
     * @return string|null
     */
    public function field()
    {
        return $this->field;
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Return the priority of the column.
     */
    public function priority(): int
    {
        return 100;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return false;
    }
}
