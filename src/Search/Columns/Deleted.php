<?php

namespace Platform\Search\Columns;

use HTML;
use Illuminate\Support\HtmlString;
use Platform\Search\Defaults;

class Deleted extends Generic
{
    use DateColumn;

    /**
     * Should work for most columnators. Otherwise use the default constructor.
     *
     * @return Deleted
     */
    public static function forResource(string $resource, $dateLocale = 'international')
    {
        $column = new self(trans('search.columns.deleted'), "{$resource}.deleted", "{$resource}.deleted_at");
        $column->setDateLocale($dateLocale);

        return $column;
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return HTML::localisedDateTime($record->deletedAt, $this->getDateLocale());
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        return HTML::relativeTime($record->deletedAt);
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('export');
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }
}
