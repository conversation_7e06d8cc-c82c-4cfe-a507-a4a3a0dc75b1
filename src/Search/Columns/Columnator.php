<?php

namespace Platform\Search\Columns;

use HTML;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Columnator implements Column
{
    /** @var string */
    private $area;

    /** @var string */
    private $view;

    public function __construct(string $area, string $view = 'search.columnator.button')
    {
        $this->area = $area;
        $this->view = $view;
    }

    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        return new HtmlString(view($this->view, ['area' => $this->area])->render());
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'columnator';
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return new Collection;
    }

    /**
     * Returns the name of the field in the query that should be present. This is useful for specifying the exact field. In some cases,
     * columns do not reference a specific table field, and may need some additional filtering or work, so a return value is not always
     * necessary. If nothing is returned, it will be ignored for the query. If null is returned, it is expected the the value method
     * will still return an appropriate value.
     *
     * @return string|null
     */
    public function field()
    {
        return null;
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return null;
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        return '';
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('search');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return true;
    }

    /**
     * Return the column visibility priority. Give columns with particularly important information a higher
     * visibility priority. A higher visibility priority makes the column stay visible while columns with
     * lower visibility priority get hidden when they do not fit into the available horizontal space.
     */
    public function priority(): int
    {
        return 1;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return false;
    }
}
