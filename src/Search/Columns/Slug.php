<?php

namespace Platform\Search\Columns;

use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Defaults;

class Slug extends Generic implements ApiColumn
{
    /**
     * Should work for most columnators. Otherwise use the default constructor.
     *
     * @return Slug
     */
    public static function forResource(string $resource)
    {
        return new self(trans('search.columns.slug'), "{$resource}.slug", "{$resource}.slug");
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return $record->slug;
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record)
    {
        return $this->value($record);
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('export');
    }

    /**
     * Returns attribute name for API output
     *
     * @return string
     */
    public function apiName()
    {
        return 'slug';
    }

    /**
     * Returns value formatted for API output
     *
     * @return string
     */
    public function apiValue($record)
    {
        return $record->slug ? (string) $record->slug : null;
    }

    /**
     * Returns a ApiVisibility value object that consists of a value representing the views that the column is available on.
     */
    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
