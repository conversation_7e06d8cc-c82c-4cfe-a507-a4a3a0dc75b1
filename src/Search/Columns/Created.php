<?php

namespace Platform\Search\Columns;

use HTML;
use Illuminate\Support\HtmlString;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Created extends Generic implements ApiColumn
{
    use DateColumn;

    /**
     * Should work for most columnators. Otherwise use the default constructor.
     *
     * @return Created
     */
    public static function forResource(string $resource, $dateLocale = 'international')
    {
        $column = new self(trans('search.columns.created'), "{$resource}.created", "{$resource}.created_at");
        $column->setDateLocale($dateLocale);

        return $column;
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return HTML::localisedDateTime($record->createdAt, $this->getDateLocale());
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        return HTML::relativeTime($record->createdAt);
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('export');
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return true;
    }

    /**
     * Returns attribute name for API output
     *
     * @return string
     */
    public function apiName()
    {
        return 'created';
    }

    /**
     * Returns value formatted for API output
     *
     * @return string
     */
    public function apiValue($record)
    {
        return $record->createdAt ? HTML::isoZuluDateTime((string) $record->createdAt) : null;
    }

    /**
     * Returns a ApiVisibility value object that consists of a value representing the views that the column is available on.
     */
    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
