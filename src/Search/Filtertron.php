<?php

namespace Platform\Search;

use Illuminate\Http\Request;

/**
 * Class Filtertron
 *
 * Manages the URL and query parameters specific to search filtering, providing the
 * methods and functionality to easily add, or remove query parameters based on the
 * links required around the page/screen.
 */
class Filtertron
{
    /**
     * @var Request
     */
    private $request;

    /**
     * Stores the query string parameters for the request.
     *
     * @var array
     */
    private $params;

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->params = $this->request->query();

        // Remove the pjax container setting which is part of most requests
        $this->without('_pjax');
    }

    /**
     * Will return a new full URL string without the parameter key provided, in effect
     * removing the query parameter from the URL string.
     *
     * @param  string  $key
     * @return $this
     */
    public function without($key)
    {
        if (isset($this->params[$key])) {
            unset($this->params[$key]);
        }

        return $this;
    }

    /**-
     * Opposite to without - adds a key and value to the query string parameters.
     *
     * @param string $key
     * @param string $value
     * @return $this
     */
    public function with($key, $value)
    {
        $this->params[$key] = $value;

        return $this;
    }

    /**
     * Returns the new required URL based on the current request URL and any query parameters
     * that may have been changed during the lifecycle of this object.
     *
     * @return string
     */
    public function url()
    {
        return implode('', ['/', $this->request->path(), '?', http_build_query($this->params, '', '&')]);
    }

    /**
     * Returns the current set of params for the URL string.
     *
     * @return array
     */
    public function params()
    {
        return $this->params;
    }

    /**
     * Renders out the URL strings based on
     *
     * @return string
     */
    public function __toString()
    {
        return $this->url();
    }

    /**
     * Returns a "sorting" html link based on filtertron inputs and components.
     */
    public static function sorter(Request $request, string $field, string $name, array $params = []): string
    {
        $dir = $request->get('dir', 'asc');
        $sortDir = $dir == 'asc' ? 'desc' : 'asc';
        $sortClass = $request->get('order') == $field ? ' sort-'.$request->get('dir') : '';
        $dataSort = $request->get('order') == $field ? $dir.'ending' : 'none';
        $pressed = $sortClass !== '' ? 'true' : 'false';

        $sortUrl = (new static($request))
            ->with('order', $field)
            ->with('dir', $sortDir)
            ->with('page', 1);

        foreach ($params as $param => $value) {
            $sortUrl->with($param, $value);
        }

        return '<a href="'.$sortUrl.'" role="button" data-order="'.$field.'" class="sortable'.$sortClass.'" data-sort="'.$dataSort.'" aria-pressed="'.$pressed.'">'.$name.'</a>';
    }
}
