<?php

namespace Platform\Search;

interface ApiColumn
{
    /**
     * Returns attribute name for API output
     *
     * @return string
     */
    public function apiName();

    /**
     * Returns value formatted for API output
     *
     * @return string
     */
    public function apiValue($record);

    /**
     * Returns a ApiVisibility value object that consists of a value representing the views that the column is available on.
     */
    public function apiVisibility(): ApiVisibility;
}
