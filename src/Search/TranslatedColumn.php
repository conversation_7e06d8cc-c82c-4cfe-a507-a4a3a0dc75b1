<?php

namespace Platform\Search;

use Facades\Platform\Strings\Output;
use Illuminate\Database\Query\Expression;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Platform\Search\Filters\TranslatedColumnSearchFilter;

abstract class TranslatedColumn implements Column
{
    /**
     * All translated fields have a dependency on this filter.
     */
    public function dependencies(): Collection
    {
        return collect(TranslatedColumnSearchFilter::class);
    }

    /**
     * The field name is the field the value relates to in the translations table, such as "title" or "description".
     */
    abstract public function fieldName(): string;

    /**
     * The column from the base query used to join translations.
     */
    public function joinColumn(): string
    {
        return 'id';
    }

    /**
     * Returns the name of the field in the query that should be present. This is useful for specifying the exact field. In some cases,
     * columns do not reference a specific table field, and may need some additional filtering or work, so a return value is not always
     * necessary. If nothing is returned, it will be ignored for the query. If null is returned, it is expected the the value method
     * will still return an appropriate value.
     *
     * @return string|null
     */
    public function field()
    {
        return DB::raw("{$this->fieldValue()} as {$this->columnName()}");
    }

    private function fieldValue(): string
    {
        if (! $this->fallbackLanguage()) {
            return "{$this->alias()}.value";
        }

        return "coalesce(NULLIF({$this->alias()}.value, ''), {$this->alias()}_fallback.value)";
    }

    /**
     * Creates a field alias which is used for retrieval as well as the translated search filter.
     *
     * @return string
     */
    public function alias()
    {
        return 'tf_'.Str::slug($this->name(), '_').'_table';
    }

    /**
     * Simple helper method to retrieve the value. HTML output is left to child classes.
     *
     * @return mixed
     */
    public function value($record)
    {
        return Output::text(($record->{$this->columnName()}));
    }

    /**
     * All translated fields are visible.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * No translated fields are fixed.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * By default, translated fields are not sortable, but can be enabled when given enough thought and due diligence.
     */
    public function sortable(): bool
    {
        return false;
    }

    /**
     * If fallback language is set, the resulting value will use the fallback language value if the original is null or ''
     */
    public function fallbackLanguage(): ?string
    {
        return null;
    }

    /**
     * If this returns a non-null value, the TranslatedColumnSearchFilter will use it to restrict the language
     */
    public function defaultLanguage(): ?string
    {
        return null;
    }

    public function columnName(): string
    {
        return "{$this->alias()}_{$this->fieldName()}";
    }

    public function translatedField(): Expression
    {
        return new TranslatedColumnExpression($this->fieldValue());
    }
}
