<?php

namespace Platform\Search;

/**
 * @deprecated
 */
interface SearchInterface
{
    /**
     * All search implementations need to have a fromInput method. This is a method that executes
     * the criteria registered based on the input array provided. This will usually come from
     * an end user.
     *
     * @param  bool  $paginate
     * @return mixed
     */
    public function fromInput(array $input = [], $paginate = true);
}
