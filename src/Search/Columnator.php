<?php

namespace Platform\Search;

use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Enhancers\SearchEnhancerCollection;

abstract class Columnator
{
    /** @var array */
    protected $columns;

    /** @var array */
    protected $input;

    public function __construct(array $columns, array $input)
    {
        $this->columns = $columns;
        $this->input = $input;
    }

    /**
     * Return a collection of Columns, based on the view required.
     */
    public function columns(Defaults $view): Columns
    {
        if ($this->columns) {
            $columns = $this->availableColumns()->fromSettings($this->columns);
        } else {
            $columns = $this->defaultColumns($view);
        }

        return $columns->unique($this->uniqueness())
            ->filter(fn($column) => $column->visible());
    }

    /**
     * Returns all columns for search, but ensures that fixed columns are included.
     *
     * @return Columns
     */
    public function searchColumns()
    {
        return $this->availableColumns()
            ->fixed()
            ->merge($this->columns(new Defaults('search')))
            ->unique($this->uniqueness());
    }

    /**
     * Returns all available columns for the columnator that are not fixed, ie. Dynamic.
     *
     * @return Columns
     */
    public function dynamic()
    {
        return $this->availableColumns()->dynamic();
    }

    /**
     * All available columns for the area.
     */
    public function availableColumns(): Columns
    {
        return $this->baseColumns()->merge($this->fieldColumns());
    }

    /**
     * Return the resource that this columnator represents (such as entries, or users).
     *
     * @return string|null
     */
    abstract public function resource();

    /**
     * All available dependencies for the area.
     */
    abstract public function availableDependencies(Defaults $view): Dependencies;

    /**
     * Returns the filters that need to be used for the search requirements.
     */
    public function filters(Defaults $view): SearchFilterCollection
    {
        $columns = $this->columns($view);
        $filters = $this->availableDependencies($view)
            ->filters($columns)
            ->merge($columns->resolveRelations());

        return new SearchFilterCollection($filters);
    }

    /**
     * Enhancers that must be executed based on column configuration.
     */
    public function enhancers(Defaults $view): SearchEnhancerCollection
    {
        $enhancers = $this->availableDependencies($view)->enhancers($this->columns($view));

        return new SearchEnhancerCollection($enhancers);
    }

    /**
     * Return the default columns for the columnator, to be used when no saved settings are available.
     */
    public function defaultColumns(Defaults $defaults): Columns
    {
        return $this->availableColumns()->filter(function (Column $column) use ($defaults) {
            return $column->default()->matches($defaults);
        });
    }

    /**
     * Columns that are defined and configured by custom fields within the Fields module.
     *
     * @return Collection
     */
    protected function fieldColumns()
    {
        return collect();
    }

    /**
     * Base columns are those that are static and essentially not columns based off any custom
     * field configuration. For example, a user's first and last name are base columns.
     *
     * @return mixed
     */
    abstract protected function baseColumns();

    /**
     * Each columnator must have a unique key that can be used to identify it when loading from settings.etc.
     */
    abstract public static function key(): string;

    /**
     * All columnators have an export view as well. The export key represents a value that can be used to search
     * for any saved exports for this particular columnator.
     */
    abstract public static function exportKey(): string;

    /**
     * Returns the name of the columnator in the user's required language.
     */
    public static function name(): string
    {
        return trans('exports.areas.'.static::key());
    }

    /**
     * Returns the export name of the columnator in the user's required language.
     */
    public static function exportName(): string
    {
        return trans('exports.areas.'.static::exportKey());
    }

    /**
     * Returns true if the columnator in question is responsible for the area asked.
     *
     * @return bool
     */
    public static function is(string $area)
    {
        return static::key() == $area or static::exportKey() == $area;
    }

    /**
     * Returns an anonymous function which is used for uniqueness checks.
     *
     * @return \Closure
     */
    private function uniqueness()
    {
        return function ($column) {
            return $column->name();
        };
    }

    /**
     * Return the repository to be used for future queries.
     */
    abstract public function repository(): Repository;

    /**
     * Returns all columns for api, but ensures that fixed columns are included.
     *
     * @return Columns
     */
    public function apiColumns(ApiVisibility $apiVisibility)
    {
        $columns = $this->availableColumns()->filter(function ($column) use ($apiVisibility) {
            return $column instanceof ApiColumn && $column->apiVisibility()->matches($apiVisibility);
        });

        return $columns->unique($this->uniqueness());
    }

    /**
     * Returns the columnator input, AKA filters
     *
     * @return array
     */
    public function input()
    {
        return $this->input;
    }

    public function columnators(): Collection
    {
        return collect([$this]);
    }
}
