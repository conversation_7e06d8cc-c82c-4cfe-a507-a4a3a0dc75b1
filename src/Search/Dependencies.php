<?php

namespace Platform\Search;

use Illuminate\Support\Collection;
use Platform\Search\Enhancers\SearchEnhancer;
use Platform\Search\Filters\ColumnatorFilter;

class Dependencies extends Collection
{
    /**
     * @param  Dependency  $dependency
     * @return self
     */
    public function add($dependency)
    {
        $this->items[] = $dependency;

        return $this;
    }

    public function filters(Columns $columns)
    {
        return $this->getByType(ColumnatorFilter::class, $columns->dependencies()->all());
    }

    public function enhancers(Columns $columns)
    {
        return $this->getByType(SearchEnhancer::class, $columns->dependencies()->all());
    }

    private function getByType(string $type, array $columnDependencies = [])
    {
        return $this->filter(function (Dependency $dependency) use ($type, $columnDependencies) {
            return $dependency instanceof $type && ($dependency->applies() || in_array(get_class($dependency), $columnDependencies));
        });
    }
}
