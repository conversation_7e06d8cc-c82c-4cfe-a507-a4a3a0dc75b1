<?php

namespace Platform\Search\Filters;

use Platform\Search\ApiVisibility;
use Platform\Search\Services\SearchFilterValidator;

/**
 * The following search filter allows for the user to filter by results that have been
 * marked as archived. They can include archived results as part of their search query,
 * or filter for ONLY archived records.
 */
class ArchivedFilter implements ColumnatorFilter, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;

    protected $input;

    public function __construct(array $input = [])
    {
        $this->input = $input;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        // Archived records should also show up when viewing deleted records.
        if (isset($this->input['trashed']) && $this->input['trashed'] === 'only') {
            return $query;
        }

        if (isset($this->input['archived'])) {
            switch ($this->input['archived']) {
                case 'only':
                    return $query->archived();
                case 'none':
                    return $query->current();
                case 'all':
                    return $query;
            }
        }

        return $query->current();
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, ['archived']);
    }

    /**
     * Checks the validity of the passed in filter name and value
     */
    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $validFilterValues = ['archived' => ['only', 'none', 'all']];
        $this->validateValueInArray($filterName, $filterValue, $validFilterValues[$filterName]);
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }
}
