<?php

namespace Platform\Search\Filters;

use Illuminate\Database\Eloquent\Builder;

class UnionFilter implements ColumnatorFilter, SearchFilter
{
    /** @var Builder */
    private $unionQuery;

    public function __construct(Builder $unionQuery)
    {
        $this->unionQuery = $unionQuery;
    }

    public function applyToEloquent($query)
    {
        return $query->union($this->unionQuery);
    }

    public function applies(): bool
    {
        return true;
    }
}
