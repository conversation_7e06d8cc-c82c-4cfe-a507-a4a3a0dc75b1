<?php

namespace Platform\Search\Filters;

/**
 * @codeCoverageIgnore
 */
class IncludeFilter implements SearchFilter, ColumnatorFilter, RelationshipFilter
{
    /**
     * Stores the relationships that should be included as part of the search results.
     *
     * @var array
     */
    protected $relationships;

    /**
     * @param  array|string  $relationships
     */
    public function __construct($relationships)
    {
        $this->relationships = is_string($relationships) ? func_get_args() : $relationships;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @param  object  $query
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        return $query->with($this->relationships);
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }
}
