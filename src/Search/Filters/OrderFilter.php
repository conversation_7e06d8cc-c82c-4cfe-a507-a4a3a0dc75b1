<?php

namespace Platform\Search\Filters;

use Illuminate\Database\Query\Expression;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Columns;
use Platform\Search\Services\SearchFilterValidator;

class OrderFilter implements ColumnatorFilter, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;

    /**
     * The default field to be used for sorting.
     *
     * @var string
     */
    protected $defaultField = '';

    /**
     * The default field for the search query to order by.
     *
     * @var string
     */
    protected $field = null;

    /**
     * Order direction.
     *
     * @var string
     */
    protected $direction = null;

    /**
     * Unique order by column.
     */
    protected ?string $uniqueColumn = null;

    /**
     * Default order direction.
     */
    protected string $defaultDirection = 'desc';

    /**
     * An array of key => value pairs.
     *
     * @var array
     */
    private $map = ['id' => 'id'];

    /**
     * @param  string  $field
     * @param  string  $direction
     */
    public function __construct($field, $direction)
    {
        $this->field = $field;
        $this->direction = $direction;
    }

    /**
     * Create a new order filter from a syntax-friendly static method.
     *
     * @param  string  $field
     * @param  string  $direction
     * @return static
     */
    public static function byFieldAndDirection($field, $direction)
    {
        return new static($field, $direction);
    }

    /**
     * We can also filter from a more generic, unknown array. Here, the OrderFilter will be a bit smarter
     * and apply ordering conditions based on the input available.
     *
     * @return OrderFilter
     */
    public static function byInput(array $input = [], ?string $default = null)
    {
        $field = Arr::get($input, 'order');
        $direction = Arr::get($input, 'dir') ?: 'desc'; // null or empty => desc

        $filter = static::byFieldAndDirection($field, $direction);
        $filter->setDefaultField($default);

        return $filter;
    }

    /**
     * Sets up a new order filter using the columns to be used for search, and sets the field map.
     *
     * @return OrderFilter
     */
    public static function fromColumns(Columns $columns, array $input = [], ?string $default = null, string $defaultDirection = 'desc')
    {
        $input = static::updateApiOrderFilterValue($columns, $input);

        $map = $columns->filter(fn($column) => $column->sortable())
            ->mapWithkeys(fn($column) => [$column->name() => static::generateMapValue($column)])
            ->toArray();

        $filter = static::byInput($input);
        $filter->setFieldMap($map);
        $filter->setDefaultDirection($defaultDirection);

        // TODO: move this check once all uses of OrderFilter are via this method
        if (array_key_exists($default, $map)) {
            $filter->setDefaultField($default);
        }

        return $filter;
    }

    /**
     * Update API order filter values to include table alias
     *
     * @return mixed
     */
    protected static function updateApiOrderFilterValue($columns, $input)
    {
        if ($order = Arr::get($input, 'order')) {
            foreach ($columns as $column) {
                if ($column instanceof ApiColumn && $order == $column->apiName()) {
                    $input['order'] = $column->name();
                    break;
                }
            }
        }

        return $input;
    }

    protected static function generateMapValue($column)
    {
        return static::fieldOrder($column->field());
    }

    /**
     * If you need custom fields or different names for fields to be ordered by, then set a field map.
     */
    public function setFieldMap(array $map)
    {
        $this->map = $map;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (! is_array($sortField = $this->sortField())) {
            $sortField = [$sortField];
        }

        $fields = [];

        foreach (array_filter($sortField) as $field) {
            if ($field instanceof Expression) {
                $field = last(explode(' ', $field->getValue(DB::connection()->getQueryGrammar())));
            }

            $fields[] = $field;

            $query->orderByRaw("{$field} {$this->sortDirection()}");
        }

        $defaultField = $this->defaultField;
        $mapDefaultField = $this->map[$defaultField] ?? null;

        // If the default field is not in the list of fields to order by and ordering is not applied yet, then add it.
        if ($defaultField && $mapDefaultField && empty($fields) && ! in_array($mapDefaultField, $fields)) {
            $query->orderBy($mapDefaultField, $this->defaultDirection);
        }

        // If a unique column is set and not present in the fields already used for ordering, then order by that as well.
        if ($this->uniqueColumn && ! in_array($this->uniqueColumn, array_merge($fields, [$defaultField]))) {
            $query->orderBy($this->uniqueColumn);
        }

        return $query;
    }

    /**
     * Returns the required sort direction.
     *
     * @return string
     *
     * @throws \Exception
     */
    protected function sortDirection()
    {
        $validDirections = ['ASC', 'DESC'];
        $direction = strtoupper($this->direction);

        if (! in_array($direction, $validDirections)) {
            throw new \Exception("$direction is not a valid direction for SQL querying. Use either ASC or DESC.");
        }

        return $direction;
    }

    /**
     * Set the default field for ordering.
     */
    public function setDefaultField($field)
    {
        if (! is_null($field)) {
            $this->defaultField = $field;
        }
    }

    /**
     * If no custom field has been specified, use the default.
     *
     * @return string|array
     */
    protected function sortField()
    {
        $field = $this->field ?: $this->defaultField;

        if (isset($this->map[$field])) {
            return $this->map[$field];
        }

        return $this->map[$this->defaultField] ?? $this->defaultField;
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }

    /**
     * If the field provided is a column whereby the value is a database expression, then we want to work with its
     * value directly, and retrieve the actual alias of the field, by exploding on ' AS '.
     *
     * @return mixed
     */
    protected static function fieldOrder($field)
    {
        if ($field instanceof Expression) {
            $fieldParts = explode(' as ', strtolower($field->getValue(DB::connection()->getQueryGrammar())));

            return array_pop($fieldParts);
        }

        return $field;
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, ['order', 'dir']);
    }

    /**
     * Checks the validity of the passed in filter name and value
     */
    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $validFilterValues = [
            'dir' => ['asc', 'desc'],
        ];

        if (array_key_exists($filterName, $validFilterValues)) {
            $this->validateValueInArray($filterName, $filterValue, $validFilterValues[$filterName]);
        }
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }

    public function uniqueColumn(?string $column): static
    {
        $this->uniqueColumn = $column;

        return $this;
    }

    protected function setDefaultDirection(string $defaultDirection)
    {
        $this->defaultDirection = $defaultDirection;
    }
}
