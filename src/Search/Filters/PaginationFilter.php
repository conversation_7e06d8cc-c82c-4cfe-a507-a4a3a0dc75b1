<?php

namespace Platform\Search\Filters;

use Illuminate\Support\Arr;
use Platform\Search\ApiVisibility;
use Platform\Search\Services\SearchFilterValidator;

class PaginationFilter implements ColumnatorFilter, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;

    public function __construct(private array $input, private int $defaultPerPage = 10)
    {

    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        request()->merge([
            'per_page' => min((int) Arr::get($this->input, 'per_page', $this->defaultPerPage),100),
            'page' => max((int) Arr::get($this->input, 'page', 1), 1),
        ]);

        return $query;
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, ['per_page', 'page']);
    }

    /**
     * Checks the validity of the passed in filter name and value
     */
    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $this->validateValueIsNumeric($filterName, $filterValue);

        if ($filterName === 'per_page') {
            $this->validateValueIsBetween($filterName, (int) $filterValue, 1, 100);
        }

        if ($filterName === 'page') {
            $this->validateValueIsPositiveNumber($filterName, (int) $filterValue);
        }
    }

    /**
     * @return bool
     */
    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }
}
