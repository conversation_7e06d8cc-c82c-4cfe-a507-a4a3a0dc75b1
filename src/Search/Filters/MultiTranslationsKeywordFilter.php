<?php

namespace Platform\Search\Filters;

use DB;

class MultiTranslationsKeywordFilter implements ColumnatorFilter, SearchFilter
{
    /**
     * @var string
     */
    private $resource;

    /**
     * @var array
     */
    private $fields;

    /**
     * @var null|string
     */
    private $keyword;

    /**
     * MultiTranslationsKeywordFilter constructor.
     *
     * @param  string  $resource
     * @param  array  $fields
     * @param  null|string  $keyword
     */
    public function __construct($resource, $fields = [], $keyword = null)
    {
        $this->resource = $resource;
        $this->fields = $fields;
        $this->keyword = $keyword;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (! empty($this->keyword) && ! empty($this->fields)) {
            $this->addJoin($query);
            $query->where("{$this->alias()}.value", 'LIKE', "%{$this->keyword}%");
        }

        return $query;
    }

    /**
     * Alters the query object, adding the translations join and filtering by the relevant search query.
     *
     * @param  Query  $query
     * @return Query
     */
    private function addJoin($query)
    {
        $alias = $this->alias();
        $from = $query->getQuery()->from;

        $query->join(DB::raw("translations $alias"), function ($join) use ($from, $alias) {
            $join->on("$alias.foreign_id", '=', "$from.id")
                ->where("$alias.resource", '=', $this->resource)
                ->whereIn("$alias.field", $this->fields);
        });

        return $query;
    }

    /**
     * Creates the required table alias.
     *
     * @return string
     */
    private function alias()
    {
        return 'k';
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }
}
