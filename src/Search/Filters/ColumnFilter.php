<?php

namespace Platform\Search\Filters;

use Illuminate\Database\Query\Expression;
use Illuminate\Support\Arr;
use Platform\Search\Services\ColumnsSelectQueryBuilder;

class ColumnFilter implements ColumnatorFilter, SearchFilter
{
    /**
     * The columns to be retrieved as a result of the request.
     *
     * @var array
     */
    protected $columns;

    /**
     * Construct a new ColumnFilter with the required columns.
     *
     * @param  array  $columns  Can be passed as an array, or list of arguments.
     */
    public function __construct(...$columns)
    {
        $this->columns = Arr::flatten($columns);
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @param  object  $query
     * @return object
     */
    public function applyToEloquent($query)
    {
        return ColumnsSelectQueryBuilder::forColumns($this->columns)->applyTo($query);
    }

    /**
     * Include fields that may be needed for search, but are not apart of any column configuration, such as a
     * primary key like a users id field.
     */
    public function with(string|Expression ...$fields): self
    {
        $this->columns = array_merge($this->columns, $fields);

        return $this;
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }
}
