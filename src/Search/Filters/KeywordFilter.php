<?php

namespace Platform\Search\Filters;

use Illuminate\Support\Arr;

class KeywordFilter implements ColumnatorFilter, SearchFilter
{
    /**
     * Default field for keyword searches. For most resources, which are quite basic,
     * the field "name" is common, and so is used frequently. It's also the standard
     * field name for "name" like fields, such as title, topic.etc.
     *
     * @var string
     */
    public $defaultField = 'name';

    /**
     * Keywords to search by.
     *
     * @var string
     */
    protected $keywords;

    protected $fields;

    public function __construct($keywords, $fields)
    {
        $this->keywords = $keywords;
        $this->fields = is_null($fields) ? $this->defaultField : $fields;
    }

    /**
     * Creates a new KeywordFilter from the keywords provided.
     *
     * @param  string  $keywords
     * @param  string|array  $fields
     * @return static
     */
    public static function fromKeywords($keywords, $fields = null)
    {
        return new static($keywords, $fields);
    }

    /**
     * Creates a new KeywordFilter from the keywords provided and then split it by space.
     *
     * @param  string  $keywords
     * @param  string|array  $fields
     * @return static
     */
    public static function fromSplitKeywords($keywords, $fields = null): KeywordFilter
    {
        return self::fromKeywords(array_filter(preg_split('/\\s+/', $keywords)), $fields);
    }

    /**
     * Default behaviour when working with request array input.
     *
     * @param  null  $fields
     * @return static
     */
    public static function fromInput(array $input, $fields = null)
    {
        return new static(Arr::get($input, 'keywords'), $fields);
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if ($keywords = $this->keywords) {
            $keywords = is_array($keywords) ? $keywords : [$keywords];
            foreach ($keywords as $keyword) {
                $query = $this->searchConditions($query, $keyword);
            }
        }

        return $query;
    }

    /**
     * Executes the required search conditions with the keyword provided.
     *
     * @return mixed
     */
    private function searchConditions($query, string $keyword)
    {
        $fields = (array) $this->fields;

        return $query->where(function ($query) use ($fields, $keyword) {
            foreach ($fields as $field) {
                $query->orWhere($field, 'LIKE', '%'.$keyword.'%');
            }
        });
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }
}
