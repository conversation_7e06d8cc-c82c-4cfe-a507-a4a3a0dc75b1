<?php
namespace Platform\Search\Filters;


class IncludeAggregateFilter implements Search<PERSON>ilter, ColumnatorFilter, RelationshipFilter
{
    private array $aggregates = [];
    
    public function __construct(string $type, array|string $relationships)
    {
        $this->aggregates['with'.ucfirst($type)] = $relationships;
    }

    public function applies(): bool
    {
        return true;
    }

    public function applyToEloquent($query)
    {
        foreach ($this->aggregates as $type => $relationships) {
            $query->$type($relationships);
        }

        return $query;
    }
    
    protected function withCount(array|string $relationships): self
    {
        $this->aggregates['withCount'] = (array) $relationships;
        
        return $this;
    }
    
    public function withSum(array|string $relationships): self
    {
        $this->aggregates['withSum'] = (array) $relationships;
        
        return $this;
    }
    
    public function withAvg(array|string $relationships): self
    {
        $this->aggregates['withAvg'] = (array) $relationships;
        
        return $this;
    }
    
    public function withMin(array|string $relationships): self
    {
        $this->aggregates['withMin'] = (array) $relationships;
        
        return $this;
    }
    
    public function with<PERSON><PERSON>(array|string $relationships): self
    {
        $this->aggregates['withMax'] = (array) $relationships;
        
        return $this;
    }
}
