<?php

namespace Platform\Search\Filters;

use Platform\Search\ApiVisibility;

interface SearchFilterValidation
{
    public function validateFilterName(string $filterName): bool;

    /**
     * Checks the validity of the passed in filter name and value
     */
    public function validateFilterValue(string $filterName, string $filterValue);

    /**
     * Validates if the scope of the filter is one of the allowed. True by default, it can
     * be implemented in the filter.
     * Eg:
     *    return $scope->matches(new ApiVisibility('view'));
     *
     * @return bool
     */
    public function validateApiScope(ApiVisibility $scope);
}
