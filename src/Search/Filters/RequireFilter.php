<?php

namespace Platform\Search\Filters;

/**
 * @codeCoverageIgnore
 */
class RequireFilter implements ColumnatorFilter, SearchFilter
{
    /**
     * Stores the relationships that should be included as part of the search results.
     *
     * @var array
     */
    private $relationships;

    /**
     * @param  array|string  $relationships
     */
    public function __construct($relationships)
    {
        $this->relationships = is_string($relationships) ? func_get_args() : $relationships;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @param  object  $query
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        foreach ($this->relationships as $relationship) {
            $query->has($relationship);
        }

        return $query;
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }
}
