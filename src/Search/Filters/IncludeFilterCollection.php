<?php
namespace Platform\Search\Filters;


use Assert\Assertion;
use Illuminate\Support\Collection;

class IncludeFilterCollection extends Collection
{
    public function __construct($items = [])
    {
        $this->validateItems($items);

        parent::__construct($items);
    }

    private function validateItems(mixed $items)
    {
        foreach ($items as $item) {
            Assertion::isInstanceOf($item, RelationshipFilter::class);
        }
    }
    
    public function addFilter(RelationshipFilter $filter): self
    {
        $this->add($filter);

        return $this;
    }
    
    public function addIncludeFilter(string|array $with): self
    {
        $this->add(new IncludeFilter($with));

        return $this;
    }
    
    public function addIncludeAggregateFilter(string $type, array|string $relationships): self
    {
        $this->add(new IncludeAggregateFilter($type, $relationships));

        return $this;
    }
    
    public function addIncludeCountFilter(array|string $relationships): self
    {
        $this->add(new IncludeAggregateFilter('count', $relationships));
        
        return $this;
    }

    public function addIncludeSumFilter(array|string $relationships): self
    {
        $this->add(new IncludeAggregateFilter('sum', $relationships));

        return $this;
    }

    public function addIncludeAvgFilter(array|string $relationships): self
    {
        $this->add(new IncludeAggregateFilter('avg', $relationships));

        return $this;
    }

    public function addIncludeMinFilter(array|string $relationships): self
    {
        $this->add(new IncludeAggregateFilter('min', $relationships));

        return $this;
    }

    public function addIncludeMaxFilter(array|string $relationships): self
    {
        $this->add(new IncludeAggregateFilter('max', $relationships));

        return $this;
    }
}
