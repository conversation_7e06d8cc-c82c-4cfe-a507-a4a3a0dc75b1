<?php

namespace Platform\Search\Filters\Dates;

enum DateFilterType: string
{
    case On = 'on';
    case Before = 'before';
    case After = 'after';
    case Between = 'between';

    public function operator(): string
    {
        return match ($this) {
            self::On => '=',
            self::Before => '<=',
            self::After => '>=',
            self::Between => 'between',
        };
    }
}
