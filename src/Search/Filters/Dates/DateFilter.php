<?php

namespace Platform\Search\Filters\Dates;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Platform\Database\Eloquent\Builder;
use Platform\Search\ApiVisibility;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Filters\SearchFilterValidation;
use Platform\Search\Services\SearchFilterValidator;

class DateFilter implements ColumnatorFilter, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;

    public function __construct(private array $input, private string $filterName, private string $columnName)
    {
    }

    public function applies(): bool
    {
        return Arr::has($this->input, $this->filterName);
    }

    public function applyToEloquent($query)
    {
        foreach ($this->input[$this->filterName] as $dateFilter => $date) {
            $this->apply($query, $date, DateFilterType::from($dateFilter));
        }

        return $query;
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, [$this->filterName]);
    }

    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $this->validateFilterValues();
    }

    private function validateFilterValues(): void
    {
        foreach ($this->input[$this->filterName] as $dateFilter => $date) {
            $this->validateValueInArray($this->filterName, $dateFilter, collect(DateFilterType::cases())->pluck('value')->toArray());
            $this->validateDate($date, DateFilterType::from($dateFilter));
        }
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }

    private function validateDate(string $date, DateFilterType $dateFilter): void
    {
        $this->validateValueDatesAreZuluDate($this->filterName, $date, $dateFilter);
    }

    private function apply(Builder $query, string $date, DateFilterType $dateFilter): void
    {
        $dateFilter === DateFilterType::Between ?
            $this->applyBetweenDates($query, $date) :
            $this->applySingleDate($query, $dateFilter->operator(), $date);
    }

    private function applySingleDate(Builder $query, string $operator, string $dateValue): void
    {
        $query->where($this->filterColumn($query), $operator, Carbon::parse($dateValue));
    }

    private function applyBetweenDates(Builder $query, string $datesValue): void
    {
        [$from, $to] = explode(',', trim($datesValue, '[]'));
        $query->where($this->filterColumn($query), DateFilterType::After->operator(), Carbon::parse($from));
        $query->where($this->filterColumn($query), DateFilterType::Before->operator(), Carbon::parse($to));
    }

    private function filterColumn(Builder $query)
    {
        return $query->getModel()->getTable().'.'.$this->columnName;
    }
}
