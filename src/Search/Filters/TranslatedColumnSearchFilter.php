<?php

namespace Platform\Search\Filters;

use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Search\Columns;
use Platform\Search\TranslatedColumn;

class TranslatedColumnSearchFilter implements ColumnatorFilter, SearchFilter
{
    /** @var Columns */
    private $columns;

    /** @var array */
    private $input;

    /** @var string */
    private $resource;

    /** @var string */
    private $joinTable;

    /** @var string */
    private $languageCode;

    /** @var int */
    private $accountId;

    public function __construct(Columns $columns, string $resource, ?int $accountId = null, array $input = [])
    {
        $this->columns = $columns->filter(function ($column) {
            return $column instanceof TranslatedColumn;
        });

        $this->resource = $resource;
        $this->accountId = $accountId;
        $this->input = $input;
    }

    /**
     * Specify the table to join with translations. Useful if the translated resource is not the base query's
     * resource.
     *
     * @return $this
     */
    public function setJoinTable(string $table)
    {
        $this->joinTable = $table;

        return $this;
    }

    /**
     * Limit translations join to a specific language. Needed for queries that count rows (e.g: leaderboard). Grouping
     * will remove duplicate rows but counts are likely to be incorrect.
     *
     * @return $this
     */
    public function restrictLanguage(string $languageCode)
    {
        $this->languageCode = $languageCode;

        return $this;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        $aliases = [];

        foreach ($this->columns as $column) {
            $aliases = array_merge($aliases, $this->addJoin($query, $column));
        }

        if (! empty($this->input['keywords'])) {
            $this->restrictQuery($query, $aliases, $this->input['keywords']);
        }

        return $query;
    }

    /**
     * Adds a join for each column that is a translated field.
     *
     * @param  Builder  $query
     * @return string[]
     */
    private function addJoin($query, TranslatedColumn $column)
    {
        $from = $this->joinTable($query);
        $alias = $column->alias();
        $aliases = [$alias];
        $this->checkDefaultLanguage($column);

        $this->performJoin($query, $from, $column, $alias, $this->languageCode);

        if (($fallbackLanguage = $column->fallbackLanguage()) && $this->languageCode !== $fallbackLanguage) {
            $aliasFallback = $alias.'_fallback';
            $aliases[] = $aliasFallback;
            $this->performJoin($query, $from, $column, $aliasFallback, $fallbackLanguage);
        }

        return $aliases;
    }

    private function joinTable($query)
    {
        return $this->joinTable ?: $query->getQuery()->from;
    }

    /**
     * Restrict the query's results by the field's value, if the keywords have been provided.
     *
     * @param  Builder  $query
     * @param  array  $aliases
     * @param  string  $keywords
     */
    private function restrictQuery($query, $aliases, $keywords)
    {
        $query->where(function ($query) use ($keywords, $aliases) {
            foreach ($aliases as $alias) {
                $query->orWhere(function ($query) use ($alias, $keywords) {
                    $this->searchByKeywords($query, $alias, $keywords);
                });
            }
        });
    }

    /**
     * Return true if this search filter applies to the search in any way.
     */
    public function applies(): bool
    {
        return true;
    }

    /**
     * Return a collection of keywords.
     *
     * @param  string  $keywords
     * @return Collection
     */
    private function getKeywords($keywords)
    {
        $keywords = collect(explode(' ', $keywords));

        return $keywords->reject(function ($keyword) {
            return empty($keyword);
        })->each(function ($keyword) {
            return trim($keyword);
        });
    }

    /**
     * Search by keywords.
     *
     * @param  Builder  $query
     * @param  string  $alias
     * @param  string  $keywords
     */
    private function searchByKeywords($query, $alias, $keywords)
    {
        $query->where(function ($query) use ($alias, $keywords) {
            foreach ($this->getKeywords($keywords) as $keyword) {
                $query->orWhere("{$alias}.value", 'LIKE', "%$keyword%");
            }
        });
    }

    private function checkDefaultLanguage(TranslatedColumn $column)
    {
        if ($column->defaultLanguage()) {
            $this->restrictLanguage($column->defaultLanguage());
        }
    }

    private function performJoin(&$query, $from, $column, $alias, $languageCode = null)
    {
        $query->leftJoin(DB::raw("translations $alias"), function ($join) use ($from, $column, $alias, $languageCode) {
            $join->on("$alias.foreign_id", '=', "$from.{$column->joinColumn()}")
                ->when($this->accountId, function ($query) use ($alias) {
                    return $query->where("$alias.account_id", '=', $this->accountId);
                })
                ->where("$alias.resource", '=', $this->resource)
                ->where("$alias.field", '=', $column->fieldName());

            if ($languageCode) {
                $join->where("{$alias}.language", '=', $languageCode);
            }
        });
    }
}
