<?php

namespace Platform\Search\Filters;

/**
 * @codeCoverageIgnore
 */
class GroupingFilter implements ColumnatorFilter, SearchFilter
{
    private $grouping;

    public function __construct(...$fields)
    {
        $this->grouping = $fields;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        return $query->groupBy($this->grouping);
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }
}
