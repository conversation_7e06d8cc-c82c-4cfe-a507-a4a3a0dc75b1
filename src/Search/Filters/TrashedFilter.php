<?php

namespace Platform\Search\Filters;

use Illuminate\Support\Arr;
use Platform\Search\ApiVisibility;
use Platform\Search\Services\SearchFilterValidator;

/**
 * The following search filter allows for the user to filter by results that have been
 * marked as deleted. They can include deleted results as part of their search query,
 * or filter for ONLY deleted records.
 */
class TrashedFilter implements ColumnatorFilter, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;

    private $input;

    public function __construct(array $input)
    {
        $this->input = $input;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (Arr::has($this->input, 'trashed') || Arr::has($this->input, 'deleted')) {
            $filterValue = Arr::get($this->input, 'trashed') ?? Arr::get($this->input, 'deleted');

            switch ($filterValue) {
                case 'only':
                    return $query->onlyTrashed();
                case 'all':
                case 'included':
                    return $query->withTrashed();
                case 'none':
                    return $query;
            }
        }

        return $query;
    }

    /**
     * Return true if this is a default search filter, meaning it will always be applied to search regardless
     * of column settings and input parameters from the user.
     */
    public function applies(): bool
    {
        return true;
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, ['trashed', 'deleted']);
    }

    /**
     * Checks the validity of the passed in filter name and value
     */
    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $validFilterValues = [
            'trashed' => ['only', 'all', 'none'],
            'deleted' => ['only', 'included', 'none'],
        ];

        $this->validateValueInArray($filterName, $filterValue, $validFilterValues[$filterName]);
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }
}
