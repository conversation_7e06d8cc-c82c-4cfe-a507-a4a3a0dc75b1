<?php

namespace Platform\Search\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Tectonic\Localisation\Contracts\Translatable;

/**
 * The translated search filter provides an easy mechanism for searching for resources that have translated
 * string values in the translations table. It'll create a new join to that table for each "field" and then do a like
 * comparison against each of the translations for that given resource.
 *
 * @note: This class has no associated test. The reason for this is because platform has no database access and the
 * resulting test would just be a stack of mocks re-iterating the code. Aka, useless, and very breakable. The test would
 * provide no value. Also, given that this class is heavily used, it's also very heavily tested.
 */
class TranslatedSearchFilter implements SearchFilter
{
    /**
     * We need to manage the number of times the translated search filter is applied. This is crucial
     * for creating the appropriate table aliases when adding the relevant joins.
     *
     * @var int
     */
    private static $calls = 0;

    /**
     * The translated field names to do the search against.
     *
     * @var string
     */
    private $fields;

    /**
     * The array of input to use when searching the translated fields.
     *
     * @var []
     */
    private $input;

    /**
     * The resource that will be used as part of the join.
     *
     * @var string
     */
    private $resource;

    /**
     * @var bool
     */
    private $restrictQuery;

    /**
     * @var string
     */
    private $delimiter = ' ';

    /**
     * Construct a new filter, with the required field names.
     *
     * @param  string|array  $fields
     * @param  string  $resource
     * @param  bool  $restrictQuery
     */
    public function __construct($fields, $resource, array $input, $restrictQuery = true)
    {
        $this->fields = (array) $fields;
        $this->resource = $resource;
        $this->input = $input;
        $this->restrictQuery = $restrictQuery;
    }

    /**
     * Track call.
     */
    private function trackCall()
    {
        // Each time a new search filter is added, we should be logging how many times it's
        // called, for field and table name aliasing and to avoid clashes.
        static::$calls++;
    }

    /**
     * Setup filter.
     *
     * @param  Builder  $query
     * @param  string  $field
     * @return string
     */
    private function setupFilter($query, $field)
    {
        $this->trackCall();

        $alias = $this->alias();

        $this->addJoin($query, $field, $alias);

        return $alias;
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @param  Builder  $query
     * @return Builder
     */
    public function applyToEloquent($query)
    {
        $aliases = [];

        foreach ($this->fields as $field) {
            $aliases[] = $this->setupFilter($query, $field);
        }

        if (! empty($this->input['keywords']) && $this->restrictQuery) {
            $this->restrictQuery($query, $aliases, $this->input['keywords']);
        }

        return $query;
    }

    /**
     * Alters the query object, adding the new translations join and filtering by the relevant search query.
     *
     * @param  Builder  $query
     * @param  string  $field
     * @param  string  $alias
     * @return Builder
     */
    private function addJoin($query, $field, $alias)
    {
        $from = $query->getQuery()->from;

        $query->addSelect(DB::raw("$alias.value AS t{$field}"));

        $query->join(DB::raw("translations $alias"), function ($join) use ($from, $field, $alias) {
            $join->on("$alias.foreign_id", '=', "$from.id")
                ->where("$alias.resource", '=', $this->resource)
                ->where("$alias.field", '=', $field);
        });

        return $query;
    }

    /**
     * Return a collection of keywords.
     *
     * @param  string  $keywords
     * @return Collection
     */
    private function getKeywords($keywords)
    {
        $keywords = collect(explode($this->delimiter, $keywords));

        return $keywords->reject(function ($keyword) {
            return empty($keyword);
        })->each(function ($keyword) {
            return trim($keyword);
        });
    }

    /**
     * Search by keywords.
     *
     * @param  Builder  $query
     * @param  string  $alias
     * @param  string  $keywords
     */
    private function searchByKeywords($query, $alias, $keywords)
    {
        $query->where(function ($query) use ($alias, $keywords) {
            foreach ($this->getKeywords($keywords) as $keyword) {
                $query->orWhere("{$alias}.value", 'LIKE', "%$keyword%");
            }
        });
    }

    /**
     * Restrict the query's results by the field's value, if the keywords have been provided.
     *
     * @param  Builder  $query
     * @param  array  $aliases
     * @param  string  $keywords
     */
    private function restrictQuery($query, $aliases, $keywords)
    {
        $query->where(function ($query) use ($keywords, $aliases) {
            foreach ($aliases as $alias) {
                $query->orWhere(function ($query) use ($alias, $keywords) {
                    $this->searchByKeywords($query, $alias, $keywords);
                });
            }
        });
    }

    /**
     * Creates the required table alias.
     *
     * @return string
     */
    private function alias()
    {
        return 't'.static::$calls;
    }

    /**
     * Returns a new filter instance based on the model's translation settings.
     *
     * @return static
     */
    public static function fromModel(Translatable $model, array $input)
    {
        return new static($model->getTranslatableFields(), $model->getResourceName(), $input);
    }
}
