<?php

namespace Platform\Search;

use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Enhancers\SearchEnhancerCollection;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Filters\UnionFilter;

class MultiColumnator extends Columnator
{
    /** @var Collection */
    protected $columnators;

    public function __construct()
    {
        $this->columnators = collect();
    }

    public function add(Columnator $columnator)
    {
        $this->columnators->push($columnator);
    }

    /**
     * Apply all filters from the first columnator to the base search, then apply a union filter for every other columnator.
     */
    public function filters(Defaults $view): SearchFilterCollection
    {
        $filters = new SearchFilterCollection;

        $first = $this->columnators->first();
        foreach ($first->filters($view) as $filter) {
            $filters->add($filter);
        }

        $this->applyExtraFilters($view, $filters);

        if ($orderFilter = $this->orderFilter($view)) {
            $filters->add($orderFilter);
        }

        return $filters;
    }

    protected function applyExtraFilters(Defaults $view, SearchFilterCollection $filters)
    {
        $rest = $this->columnators->slice(1);
        foreach ($rest as $columnator) {
            $filters->add(new UnionFilter($columnator->repository()->applyFilters($columnator->filters($view))));
        }
    }

    /**
     * Extract first available order filter.
     *
     * @return null|SearchFilter
     */
    protected function orderFilter(Defaults $view)
    {
        foreach ($this->columnators as $columnator) {
            $orderFilter = $columnator->filters($view)->first(function ($filter) {
                return $filter instanceof OrderFilter;
            });

            if ($orderFilter) {
                return $orderFilter;
            }
        }
    }

    /**
     * Columnators could have different enhancers. All of them must be applied to results.
     */
    public function enhancers(Defaults $view): SearchEnhancerCollection
    {
        return new SearchEnhancerCollection($this->columnators->map(function (Columnator $columnator) use ($view) {
            return $columnator->enhancers($view);
        })->collapse());
    }

    /**
     * Resource must be the same across columnators.
     *
     * @return mixed
     */
    public function resource()
    {
        return $this->columnators->first()->resource();
    }

    /**
     * Columns must be the same across columnators.
     *
     * @return mixed
     */
    protected function baseColumns()
    {
        return $this->columnators->first()->baseColumns();
    }

    /**
     * Columns must be the same across columnators.
     *
     * @return mixed|Columns
     */
    public function columns(Defaults $view): Columns
    {
        return $this->columnators->first()->columns($view);
    }

    /**
     * Field columns must be the same across columnators.
     *
     * @return Collection
     */
    protected function fieldColumns()
    {
        return $this->columnators->first()->fieldColumns();
    }

    /**
     * The repository must be the same across columnators.
     */
    public function repository(): Repository
    {
        return $this->columnators->first()->repository();
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        return new Dependencies;
    }

    public static function key(): string
    {
        return '';
    }

    public static function exportKey(): string
    {
        return '';
    }

    public function input()
    {
        return $this->columnators->first()->input();
    }

    public function columnators(): Collection
    {
        return $this->columnators;
    }
}
