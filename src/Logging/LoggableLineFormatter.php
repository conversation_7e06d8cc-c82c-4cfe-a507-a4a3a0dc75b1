<?php

namespace Platform\Logging;

use Monolog\Formatter\LineFormatter;
use Monolog\LogRecord;

class LoggableLineFormatter extends LineFormatter
{
    public function format(LogRecord $record): string
    {
        $context = array_map(
            fn($object) => $object instanceof Loggable ? $object->toLog() : $object,
            $record->context
        );

        return parent::format(new LogRecord(
            $record->datetime,
            $record->channel,
            $record->level,
            $record->message,
            $context,
            $record->extra,
            $record->formatted
        ));
    }
}
