<?php

namespace Platform\Logging;

class CustomFormatter
{
    public function __invoke($logger): void
    {
        $includeStacktraces = config('logging.general.includeStacktraces', false);
        foreach ($logger->getHandlers() as $handler) {
            $handler->setFormatter(new LoggableLineFormatter(dateFormat: 'Y-m-d H:i:s', ignoreEmptyContextAndExtra: true, includeStacktraces: $includeStacktraces));
        }
    }
}
