<?php

namespace Platform\Authorisation\FeatureRoles;

use Illuminate\Support\Collection;
use Platform\Authorisation\ValueObjects\Permission;

abstract class FeatureRole
{
    /**
     * Permissions required to have this feature role.
     *
     * @return mixed
     */
    abstract public static function permissionSet();

    /**
     * Return an array of Permission objects that contain the permissions
     * necessary for a given consumer to pass any checks.
     *
     * @return Permission[]
     */
    public static function permissions()
    {
        return array_map(function ($permission) {
            return new Permission($permission[0], $permission[1], $permission[2]);
        }, static::permissionSet());
    }

    /**
     * Check if this feature role applies to either a user with the given set of permissions
     * or permissions collection.
     * A feature role applies if a user has all the required permissions defined in permissionSet()
     *
     * @param  Collection  $permissions
     * @return bool
     */
    public static function appliesTo($consumerOrPermissions)
    {
        if ($consumerOrPermissions instanceof Collection) {
            return static::appliesToPermissions($consumerOrPermissions);
        }

        return static::appliesToConsumer($consumerOrPermissions);
    }

    /**
     * Check if this feature role applies to a user with the given set of permissions.
     * A feature role applies if a user has all the required permissions defined in permissionSet()
     *
     * @return bool
     */
    private static function appliesToPermissions(Collection $permissions)
    {
        $requiredPermissions = static::permissionSet();
        $userPermissions = static::format($permissions);

        return count($requiredPermissions) == count(static::intersect($requiredPermissions, $userPermissions));
    }

    /**
     * Figures out whether or not the role applies to the consumer, by checking each
     * of the required permissions for the role permission set.
     *
     * @return bool
     */
    private static function appliesToConsumer($consumer)
    {
        $numRequiredChecks = count(static::permissionSet());
        $numPassedChecks = 0;

        foreach (static::permissionSet() as $permission) {
            $checkType = static::checkType($permission[2]);

            if (! is_null($checkType) && $consumer->$checkType($permission[1], $permission[0])) {
                $numPassedChecks++;
            }
        }

        return $numPassedChecks == $numRequiredChecks;
    }

    /**
     * Convert Permission objects into a simpler ['resource', 'action', 'mode']
     * format than can be used for matching.
     *
     * @return array
     */
    private static function format(Collection $permissions)
    {
        return collect($permissions)->map(function ($permission) {
            return [$permission->resource, $permission->action, (string) $permission->mode];
        })->all();
    }

    /**
     * Returns all required permissions that can be found in the user's permissions.
     *
     * @return array
     */
    private static function intersect(array $requiredPermissions, array $userPermissions)
    {
        return array_filter($requiredPermissions, function (array $required) use ($userPermissions) {
            foreach ($userPermissions as $available) {
                if ($required[0] == $available[0] && $required[1] == $available[1] && $required[2] == $available[2]) {
                    return true;
                }
            }
        });
    }

    /**
     * Determines the type of check to be done based on the permission mode.
     *
     * @param  string  $mode
     * @return string|void
     */
    private static function checkType($mode)
    {
        switch ($mode) {
            case 'allow':
                return 'can';
            case 'deny':
                return 'cannot';
            case 'inherit':
                return;
        }
    }
}
