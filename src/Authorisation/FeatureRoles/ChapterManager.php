<?php

namespace Platform\Authorisation\FeatureRoles;

use Platform\Authorisation\Consumer;

class ChapterManager extends FeatureRole
{
    /**
     * Permissions required to have this feature role.
     *
     * @return mixed
     */
    public static function permissionSet()
    {
        return [
            ['Chapters', 'create', 'deny'],
            ['EntriesAll', 'view', 'allow'],
        ];
    }

    public static function appliesTo($consumerOrPermissions): bool
    {
        if ($consumerOrPermissions instanceof Consumer) {
            return $consumerOrPermissions->isChapterLimited() && parent::appliesTo($consumerOrPermissions);
        }

        return parent::appliesTo($consumerOrPermissions);
    }
}
