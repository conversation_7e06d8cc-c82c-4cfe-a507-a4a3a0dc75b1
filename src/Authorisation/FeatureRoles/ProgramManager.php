<?php

namespace Platform\Authorisation\FeatureRoles;

class ProgramManager extends FeatureRole
{
    /**
     * Permissions required to have this feature role.
     *
     * @return mixed
     */
    public static function permissionSet()
    {
        return [
            ['Chapters', 'update', 'allow'],
            ['EntriesAll', 'update', 'allow'],
            ['Users', 'update', 'allow'],
        ];
    }
}
