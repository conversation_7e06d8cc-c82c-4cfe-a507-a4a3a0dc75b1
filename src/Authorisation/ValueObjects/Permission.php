<?php

namespace Platform\Authorisation\ValueObjects;

class Permission
{
    /**
     * @var string
     */
    public $resource;

    public $action;

    public $mode;

    /**
     * Constructor
     *
     * @param  string  $resource
     * @param  string  $action
     * @param  string  $mode
     */
    public function __construct($resource, $action, $mode = 'allow')
    {
        $this->resource = $resource;
        $this->action = $action;
        $this->mode = $mode;
    }
}
