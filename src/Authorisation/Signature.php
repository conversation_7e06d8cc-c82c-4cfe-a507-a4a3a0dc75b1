<?php

namespace Platform\Authorisation;

use Assert\Assertion;

class Signature
{
    /**
     * @var array
     */
    protected $parts;

    /**
     * @param  string[]  $parts
     *
     * @throws \Assert\AssertionFailedException
     */
    public function __construct(string $app, string ...$parts)
    {
        Assertion::inArray($app, ['af4', 'falcon', 'myaf']);

        $this->parts = array_merge([$app], $parts);
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return implode(':', array_filter($this->parts));
    }
}
