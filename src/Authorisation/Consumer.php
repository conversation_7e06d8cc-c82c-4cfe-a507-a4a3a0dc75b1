<?php

namespace Platform\Authorisation;

use Platform\Language\Language;

interface Consumer
{
    /**
     * Returns an array of accounts that the consumer can manage.
     *
     * @return array Account
     */
    public function accounts();

    /**
     * Returns the language that the consumer prefers.
     */
    public function language(): Language;

    /**
     * Returns the account default language code if the consumer's language is not active.
     */
    public function strictLanguage(): Language;

    /**
     * Return the id for the consumer.
     *
     * @return int
     */
    public function id();

    /**
     * Returns the type of the consumer.
     *
     * @return string
     */
    public function type();

    /**
     * Returns the roles the consumer has been assigned.
     *
     * @return \Illuminate\Support\Collection
     */
    public function roles();

    /**
     * Returns the user model for the consumer.
     *
     * @return object
     */
    public function user();

    /**
     * Return the preferred timezone of the user.
     *
     * @return mixed
     */
    public function timezone();

    /**
     * Returns the name for the consumer.
     */
    public function name(): string;

    /**
     * Returns the global ID of the consumer.
     */
    public function globalId(): ?string;

    /**
     * Returns the current account global ID .
     */
    public function globalAccountId(): ?string;

    /**
     * Check if the consumer has any chapter limited roles.
     */
    public function isChapterLimited(): bool;
}
