<?php

namespace Platform\Authorisation;

use Platform\Database\Eloquent\Model;

/**
 * Platform\Authorisation\Permission
 *
 * @property int $id
 * @property string $resource
 * @property string $action
 * @property string $mode
 */
class Permission extends Model
{
    /**
     * Determines whether or not a permission object matches the required resource/action requirement.
     *
     * @param  string  $resource
     * @param  string|null  $action
     * @return bool
     */
    public function matches($resource, $action = null)
    {
        if ($resource == $this->resource && (is_null($action) or $action == $this->action)) {
            return true;
        }

        return false;
    }

    /**
     * Determines whether or not the permission object has an allowed state.
     *
     * @return bool
     */
    public function allowed()
    {
        return $this->mode == 'allow';
    }

    /**
     * Determines whether the permission object has a denied state.
     *
     * @return bool
     */
    public function denied()
    {
        return $this->mode == 'deny';
    }
}
