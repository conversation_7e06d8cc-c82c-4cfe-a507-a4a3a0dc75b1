<?php

namespace Platform\KVStore;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Psr7\Response;

class Consul implements Store
{
    /** @var string */
    private $hostname;

    /** @var int */
    private $port;

    /** @var bool */
    private $secure;

    /** @var string */
    private $prefix;

    /** @var Client */
    private $client;

    public function __construct(string $hostname, bool $secure = false, int $port = 8500, string $prefix = 'v1/kv/')
    {
        $this->hostname = $hostname;
        $this->secure = $secure;
        $this->port = $port;
        $this->prefix = $prefix;
    }

    /**
     * Create the value at the specified key.
     */
    public function create(string $key)
    {
        $this->request('PUT', $key);
    }

    /**
     * Returns the value of the specified key, if exists.
     * Returns `false` if not exists.
     *
     * @return string|null|bool
     */
    public function get(string $key)
    {
        try {
            $response = $this->request('GET', $key);

            return $response->getBody()->getContents();
        } catch (ClientException $exception) {
            return false; // 4xx error => missing
        }
    }

    /**
     * Remove a specific key from the key-value store.
     */
    public function delete(string $key)
    {
        $this->request('DELETE', $key);
    }

    public function setClient(Client $client): self
    {
        $this->client = $client;

        return $this;
    }

    private function request(string $method, string $path): Response
    {
        $this->client = $this->client ?: new Client;
        $url = ($this->secure ? 'https://' : 'http://')."{$this->hostname}:{$this->port}/{$this->prefix}";

        return $this->client->request($method, $url.$path);
    }
}
