<?php

namespace Platform\KVStore;

interface Store
{
    /**
     * Create the value at the specified key.
     */
    public function create(string $key);

    /**
     * Returns the value of the specified key, if exists.
     * Returns `false` if not exists.
     *
     * @return string|null|bool
     */
    public function get(string $key);

    /**
     * Remove a specific key from the key-value store.
     */
    public function delete(string $key);
}
