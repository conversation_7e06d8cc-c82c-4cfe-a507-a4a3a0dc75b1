<?php

use Carbon\Carbon;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\HtmlString;
use Platform\Database\Eloquent\Model;
use Platform\DateTime\LocalisedDate;
use Platform\DateTime\LocalisedDateTime;
use Platform\Menu\Menu;
use Platform\Menu\Menufy;
use Platform\Search\Filtertron;
use Spatie\Html\Html;

/**
 * Returns the relative time for a given mysql timestamp.
 *
 * @param  string  $timestamp
 * @return \Illuminate\Contracts\Support\Htmlable
 */
HTML::macro('relativeTime', function ($timestamp) {
    return new HtmlString((new Carbon($timestamp))->diffForHumans());
});

/**
 * Returns the relative created & updated time (if different from created) for given mysql timestamps.
 *
 * @param  string  $created
 * @param  string  $updated
 * @return string
 */
HTML::macro('relativeCreatedUpdatedTime', function ($created, $updated) {
    if ($created == $updated) {
        return HTML::relativeTime($created);
    }

    return ucwords(trans('miscellaneous.created')).' '.(new Carbon($created))->diffForHumans().', '.
        trans('miscellaneous.updated').' '.(new Carbon($updated))->diffForHumans();
});

/**
 * Returns the localised date for a given mysql timestamp in the specified format.
 *
 * @param  string  $timestamp
 * @return string
 */
HTML::macro('formattedDate', function ($timestamp, $format = '%d %B %Y') {
    if (empty($timestamp)) {
        return '';
    }

    return (new Carbon($timestamp))->isoFormat($format);
});

/**
 * Returns the localised date for a given mysql timestamp.
 *
 * @param  string  $timestamp
 * @param  string  $locale
 * @return string
 */
HTML::macro('localisedDate', function ($timestamp, $locale = 'international') {
    if (empty($timestamp)) {
        return '';
    }

    return LocalisedDate::create(new Carbon($timestamp), $locale)->format();
});

/**
 * Returns the localised date/time for a given mysql timestamp.
 *
 * @param  string  $timestamp
 * @param  string  $locale
 * @return string
 */
HTML::macro('localisedDateTime', function ($timestamp, $locale = 'international') {
    if (empty($timestamp)) {
        return '';
    }

    return LocalisedDateTime::create(new Carbon($timestamp), $locale)->format();
});

/**
 * Returns the ISO 8601 zulu datetime for a given timestamp and timezone.
 *
 * @param  string  $timestamp
 * @param  string|null  $timezone
 * @return string
 */
HTML::macro('isoZuluDateTime', function ($timestamp, $timezone = null) {
    if (empty($timestamp)) {
        return '';
    }

    return (new Carbon($timestamp, $timezone))->toIso8601ZuluString();
});

/**
 * Returns the atom datetime for a given timestamp and timezone.
 *
 * @param  string  $timestamp
 * @param  string|null  $timezone
 * @return string
 */
HTML::macro('atomDateTime', function ($timestamp, $timezone = null) {
    if (empty($timestamp)) {
        return '';
    }

    return (new Carbon($timestamp, $timezone))->toAtomString();
});

/**
 * Returns the localised time for a given mysql timestamp.
 *
 * @param  string  $timestamp
 * @param  string  $locale
 * @return string
 */
HTML::macro('localisedTime', function ($timestamp, $locale = 'international') {
    if (empty($timestamp)) {
        return '';
    }

    $timeFormat = config('lang.moment-formats.'.$locale.'.time');

    return (new Carbon($timestamp))->isoFormat($timeFormat);
});

/**
 * Generates an html date time component, which includes the date
 * selector, an hour selector, a time selector - and a timezone selector.
 *
 * @return string
 */
HTML::macro('date_time', function ($name) {
    return view('html.form.datetime', ['name' => $name]);
});

/**
 * Returns a very customised view for pagination. Simply provide the paginator instance to the
 * macro as the one and only parameter and the pagination view will handle the rest.
 *
 * @param  Paginator  $paginator
 */
HTML::macro('pagination', function ($paginator) {
    return view('partials.page.pagination', compact('paginator'));
});

/**
 * Renders a menu that has been registered by the provided menuName.
 *
 * @param  string|Menu  $menu
 * @param  string  $partial
 * @return View
 */
HTML::macro('menu', function ($menu, $partial) {
    // If a menu instance was not passed, let's instead try and fetch the menu by name
    if (! ($menu instanceof Menu)) {
        $menu = Menufy::menu($menu);
    }

    // Render the menu
    return view($partial, compact('menu'));
});

/**
 * Manages the sorting component for the front-end in list views. Checks for request data to
 * see if an order and direction has already been provided, and builds required URLs for sorting.
 */
HTML::macro('sorter', function ($field, $name, array $extraQuery = []) {
    return Filtertron::sorter(app(\Illuminate\Http\Request::class), $field, $name, $extraQuery);
});

/**
 * Returns the route + search params to an export url.
 *
 * @return string
 */
HTML::macro('export', function ($route, $type) {
    $queryString = parse_url(Request::fullUrl(), PHP_URL_QUERY);

    if ($queryString) {
        $queryString = '?'.$queryString;
    }

    return route($route, ['type' => $type]).$queryString;
});

/**
 * Renders an HTML pill button for removing filters from a search.
 *
 * @return View
 */
HTML::macro('removeFilter', function ($filter) {
    $url = app(Filtertron::class)
        ->without($filter->key, $filter->value)
        ->with('page', 1);

    return view('html.buttons.remove-filter', compact('filter', 'url'));
});

/**
 * Renders a Marker Action button.
 */
HTML::macro('markerAction', function (
    $action,
    $name,
    $url = null,
    $method = null,
    $fields = [],
    $view = 'html.buttons.marker-action'
) {
    $icon = $name;

    return view($view, compact('action', 'name', 'icon', 'url', 'method', 'fields'));
});

/**
 * Renders a Reveal Action button.
 */
HTML::macro('revealAction', function ($action, $target, $open, $close) {
    return view('html.buttons.reveal-action', compact('action', 'target', 'open', 'close'));
});

/**
 * Creates a new hidden form field called updatedAt with the model's
 * current updatedAt field value.
 *
 * @return string
 */
HTML::macro('updatedAt', function (?Model $model = null) {
    $model = $model ?: $this->model;

    $updatedAt = $model->exists ? $model->fresh()->updatedAt : '';

    return new HtmlString('<input type="hidden" name="updatedAt" value="'.$updatedAt.'">');
});

/**
 * Trashed search filter selector. This is a little tricky as we make the option value the URL
 * that we wish to send the user to. This is derived from all the filtering and pagination options
 * that may already be configured, so we have to do some checks to see which trashed filter option
 * has been selected.
 *
 * @param  string  $view
 */
HTML::macro('trashedSelect', function ($view = null) {
    $filtertron = app(Filtertron::class);

    $none = $filtertron->with('trashed', 'none')->with('page', 1)->url();
    $only = $filtertron->with('trashed', 'only')->with('page', 1)->url();

    $selected = Request::get('trashed') == 'only' ? $only : null;

    $options = [
        $none => trans('miscellaneous.trashed.not_deleted'),
        $only => trans('miscellaneous.trashed.only_deleted'),
    ];

    if ($view) {
        return view($view, compact('options', 'selected'));
    }

    $select = HTML::select('trashSelector', $options, $selected)->attributes(['enhance', 'id' => 'trashSelector']);

    return new HtmlString('<div class="selector">'
        .'<label for="trashSelector"><span class="sr-only">'.trans('interface-text.selectors.trashed-selector.label').'</span></label>'
        .$select
        .'</div>');
});

/**
 * Trashed search filter selector. Like trashedSelect(), but with "Archived" option.
 *
 * @param  string  $view
 */
HTML::macro('trashedWithArchivedSelect', function ($view = null) {
    $filtertron = app(Filtertron::class);

    $none = $filtertron->with('archived', 'none')->with('trashed', 'none')->with('page', 1)->url();
    $onlyArchived = $filtertron->with('archived', 'only')->with('trashed', 'none')->with('page', 1)->url();
    $onlyTrashed = $filtertron->with('archived', 'none')->with('trashed', 'only')->with('page', 1)->url();

    if (Request::get('archived') == 'only') {
        $selected = $onlyArchived;
    } elseif (Request::get('trashed') == 'only') {
        $selected = $onlyTrashed;
    } else {
        $selected = null;
    }

    $options = [
        $none => trans('miscellaneous.state.current'),
        $onlyArchived => trans('miscellaneous.state.archived'),
        $onlyTrashed => trans('miscellaneous.trashed.only_deleted'),
    ];

    if ($view) {
        return view($view, compact('options', 'selected'));
    }

    $select = Html::select('trashSelector', $options, $selected)->attributes(['enhance', 'id' => 'trashSelector']);

    return new HtmlString('<div class="selector">'
        .'<label for="trashSelector"><span class="sr-only">'.trans('interface-text.selectors.trashed-selector.label').'</span></label>'
        .$select
        .'</div>');
});

/**
 * State (current/archived/trashed) search filter selector.
 *
 * @param  string  $view
 */
HTML::macro('stateSelect', function ($view = null) {
    $filtertron = app(Filtertron::class);

    $current = $filtertron->with('archived', 'none')->with('trashed', 'none')->with('page', 1)->url();
    $onlyArchived = $filtertron->with('archived', 'only')->with('trashed', 'none')->with('page', 1)->url();
    $onlyTrashed = $filtertron->with('archived', 'none')->with('trashed', 'only')->with('page', 1)->url();

    if (Request::get('archived') == 'only') {
        $selected = $onlyArchived;
    } elseif (Request::get('trashed') == 'only') {
        $selected = $onlyTrashed;
    } else {
        $selected = null;
    }

    $options = [
        $current => trans('miscellaneous.state.current'),
        $onlyArchived => trans('miscellaneous.state.archived'),
        $onlyTrashed => trans('miscellaneous.state.deleted'),
    ];

    if ($view) {
        return view($view, compact('options', 'selected'));
    }

    $select = Html::select('trashSelector', $options, $selected)->attributes(['enhance', 'id' => 'trashSelector']);

    return new HtmlString('<div class="selector">'
        .'<label for="trashSelector"><span class="sr-only">'.trans('interface-text.trashed-selector.label').'</span></label>'
        .$select
        .'</div>');
});

/**
 * Returns an HTML output icon representing a model's deleted state.
 */
HTML::macro('deletedState', function ($model) {
    if (isset($model->deletedAt) && ! is_null($model->deletedAt)) {
        return new HtmlString('<span class="af-icons af-icons-close deleted-state"></span>');
    }
});

/**
 * Returns the HTML required for a resource link. If a resource is deleted, then it should not link
 * through to the appropriate page and be identified as such. IF, however - the resource is still active
 * then the link should be shown as per normal.
 */
HTML::macro('resourceLink', function ($text, $url, $resource) {
    return view('html.resource-link', compact('text', 'url', 'resource'));
});

/**
 * Renders a help icon that will render the required translated text.
 *
 * @param  string  $text
 * @param  array  $replacements
 * @param  bool  $html
 * @return string
 */
HTML::macro('helpIcon', function ($text, $replacements = [], $html = false) {
    if (empty($text)) {
        return;
    }

    $lang = str_replace("'", '&#039;', trans(e($text), $replacements));
    $html = $html ? 'true' : 'false';

    return new HtmlString("<span class='af-icons af-icons-help trigger-tooltip' data-html='{$html}' data-content='{$lang}'></span>");
});

/**
 * Renders the marker toggle component.
 *
 * @return string
 */
HTML::macro('markerToggle', function () {
    return new HtmlString(
        '<label for="select-all"><span class="sr-only">'.trans('interface-text.selectors.select_all.label').'</span></label>'
        .Html::checkbox()->id('select-all')->class('marker-toggle')
    );
});

/**
 * Similar to markerToggle, but is per-record.
 *
 * @return string
 */
HTML::macro('markerCheckbox', function ($id, $disabled = false) {
    $options = ['id' => $id, 'data-id' => $id, 'class' => 'marker-checkbox'];
    if ($disabled) {
        $options[] = 'disabled';
    }

    return new HtmlString(
        '<label for="'.$id.'"><span class="sr-only">'.trans('fields.types.checkbox').'</span></label>'
        .Html::checkbox(name: '', checked: false, value: null)->attributes($options)
    );
});

/**
 * Deletion is often a two-way street in Award Force, with the ability to undelete items
 * at any time. This helper macro allows us to generate the appropriate button based on
 * the search state of the page.
 */
HTML::macro('markerDelete', function ($resource) {
    if (! Request::filled('trashed') or Request::get('trashed') == 'none') {
        return HTML::markerAction('delete', trans('buttons.delete'), route("$resource.delete"), 'DELETE');
    }

    return HTML::markerAction('undelete', trans('buttons.undelete'), route("$resource.undelete"), 'PUT');
});

/**
 * Archive/unarchive macro.
 */
HTML::macro('markerArchive', function ($resource) {
    if (! Request::filled('archived') or Request::get('archived') == 'none') {
        return HTML::markerAction('archive', trans('buttons.archive'), route("$resource.archive"), 'PUT');
    }

    return HTML::markerAction('unarchive', trans('buttons.unarchive'), route("$resource.unarchive"), 'PUT');
});

/**
 * Helper macro for a consistent output of dates and timezones when coupled together.
 *
 * @param  string  $date
 * @param  string  $timezone
 * @param  string  $locale
 * @return \Illuminate\Contracts\Support\Htmlable
 */
HTML::macro('dateTimezone', function ($date, $timezone, $locale = null) {
    $timezone = $timezone ?: 'UTC';

    $fullTimezone = full_timezone($timezone);

    // Even if dates are nulled, the date conversion on the Round model/entity will show up like this
    // so, we want to check for this value and just create an empty string instead. In addition, timezones
    // have no relevance here, so just return empty string.
    if (is_null($date)) {
        return '';
    }

    $date = convert_date_to_timezone($date, $timezone);

    if ($locale) {
        $dateFormat = config('lang.moment-formats.'.$locale.'.date');
        $timeFormat = config('lang.moment-formats.'.$locale.'.time');

        $date = $date->isoFormat($dateFormat.' '.$timeFormat);
    }

    return new HtmlString("<div class=\"date-output\">{$date}</div>
        <div class=\"timezone-output\">{$fullTimezone}</div>");
});

/**
 * Creates a filterable multiselect form element.
 *
 * Accepted Parameters:
 * - placeholder => placeholder text for the filter box
 *
 * Accepted Subset fields:
 * - label    => Lang String to show for the subset selector header
 * - name     => field name to save subset selected options
 * - selected => currently selected subset options
 */
Html::macro('multi_select', function (
    $name,
    array $options,
    ?array $selected = null,
    array $parameters = [],
    array $subset = [],
    $displayFilterBox = true
) {
    $options = $options ?: [];
    $selected = $selected ?: [];

    return view(
        'html.form.multiselect',
        compact('name', 'options', 'selected', 'parameters', 'subset', 'displayFilterBox')
    );
});

/**
 * Generates a timezone selector that provides a list of all timezones and their gmt offsets/timezone codes.
 *
 * @param  string  $name
 */
Html::macro('timezone', function ($name, $value = null, $attributes = []) {
    $options = timezones();

    if (is_null($value) && empty($this->model?->getAttribute($name))) {
        $value = function_exists('setting') && setting('timezone') ?: 'UTC';
    }

    // Now we create the full, nicely-formatted timezone, with offset
    return Html::select($name, $options, $value)->attributes($attributes);
});

/**
 * Renders HTML when the $optional var is set to true.
 *
 * @return string
 */
HTML::macro('optional', function ($optional) {
    if ($optional) {
        return new HtmlString('<span class="optional-field">'.trans('miscellaneous.optional').'</span>');
    }
});

/**
 * Checks if the provided model is deleted, and renders the provided string accordingly.
 *
 * @param  Model  $model
 * @param  string  $value
 * @param  bool  $escape
 * @return string
 */
HTML::macro('maybeDeleted', function ($model, $value, $escape = true) {
    if ($escape) {
        $value = e($value);
    }

    if (empty($model->deletedAt)) {
        return $value;
    }

    return new HtmlString('<span class="deleted">'.$value.'</span>');
});

/**
 * Renders a formatted span with merge fields for the given trigger.
 *
 * @param  string  $trigger
 * @param  array  $mergeFields
 * @return string
 */
HTML::macro('mergeFields', function ($trigger, $mergeFields) {
    $fields = count($mergeFields) ? '{'.implode('}, {', $mergeFields).'}' : '';

    return new HtmlString("<span class=\"hidden\" id=\"{$trigger}-merge-fields\">{$fields}</span>");
});

/**
 * Parses the url and returns a video id as a data attribute.
 *
 * @return string
 */
HTML::macro('videoId', function ($url) {
    if (get_vimeo_id($url)) {
        return 'data-vimeo-id="'.get_vimeo_id($url).'"';
    } elseif (get_youtube_id($url)) {
        return 'data-youtube-id="'.get_youtube_id($url).'"';
    }
});

/**
 * Generates the caret icon required for any stat widget.
 *
 * @return string
 */
HTML::macro('caretStat', function ($difference) {
    if ($difference != 0) {
        $chevron = ($difference >= 0) ? 'up' : 'down';

        return new HtmlString('<span class="af-icons af-icons-arrow-'.$chevron.' af-icons-sm"></span>');
    }
});

/**
 * State (current/archived/trashed/grants) search filter selector.
 *
 * @param  string  $view
 */
Html::macro('stateSelectGrants', function ($view = null) {
    $filtertron = app(Filtertron::class);

    $current = $filtertron->with('archived', 'none')->with('trashed', 'none')->without('showGrants')->with('page', 1)->url();
    $onlyArchived = $filtertron->with('archived', 'only')->with('trashed', 'none')->without('showGrants')->with('page', 1)->url();
    $onlyTrashed = $filtertron->with('archived', 'none')->with('trashed', 'only')->without('showGrants')->with('page', 1)->url();
    $showGrants = $filtertron->with('showGrants', '1')->without('archived')->without('trashed')->with('page', 1)->url();

    if (Request::get('archived') == 'only') {
        $selected = $onlyArchived;
    } elseif (Request::get('trashed') == 'only') {
        $selected = $onlyTrashed;
    } elseif (Request::get('showGrants') == '1') {
        $selected = $showGrants;
    } else {
        $selected = null;
    }

    $options = [
        $current => trans('miscellaneous.state.current'),
        $onlyArchived => trans('miscellaneous.state.archived'),
        $onlyTrashed => trans('miscellaneous.state.deleted'),
        $showGrants => trans(':grant+'),
    ];

    if ($view) {
        return view($view, compact('options', 'selected'));
    }
    $select = Html::select('trashSelector', $options, $selected)->attributes(['enhance', 'id' => 'trashSelector']);

    return new HtmlString('<div class="selector">'
        .'<label for="trashSelector"><span class="sr-only">'.trans('interface-text.trashed-selector.label').'</span></label>'
        .$select
        .'</div>');
});

HTML::macro('link', function ($url, $name, $attributes = []) {
    $link = html()->a($url)->attributes($attributes);

    return $link->text($name ?? $link->getAttribute('href'));
});

HTML::macro('linkRoute', function (string $route, ?string $name = null, array $parameters = [], array $attributes = []) {
    $link = html()->a(route($route, $parameters))->attributes($attributes);

    return $link->text($name ?? $link->getAttribute('href'));
});

HTML::macro('style', function (string $url, array $attributes = []) {
    $defaults = ['media' => 'all', 'type' => 'text/css', 'rel' => 'stylesheet'];

    $attributes = array_merge($defaults, $attributes);

    $attributes['href'] = asset($url);

    return html()->element('link')->attributes($attributes);
});
