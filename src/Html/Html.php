<?php

namespace Platform\Html;

use Illuminate\Contracts\View\Factory as ViewFactory;
use Illuminate\Support\Str;
use Spatie\Html\BaseElement;
use Spatie\Html\Elements\Select;
use Spatie\Html\Elements\Textarea;
use Spatie\Html\Html as BaseHtml;

class Html extends BaseHtml
{
    public function textarea($name = null, $value = null, $options = []): Textarea|BaseElement
    {
        return parent::textarea($name, $value)->attributes($this->mergeExtraOptions($name, $options));
    }

    public function select($name = null, $options = [], $value = null, array $selectAttributes = [], array $optionsAttributes = [], array $optgroupsAttributes = []): BaseElement|Select
    {
        return parent::select($name, array_map_recursive((array) $options, 'strip_tags'), $value)->attributes($this->mergeExtraOptions($name, $selectAttributes));
    }

    private function mergeExtraOptions($name = '', $options = []): array
    {
        $errors = isset(app(ViewFactory::class)->getShared()['errors'])
            ? app(ViewFactory::class)->getShared()['errors']->getBag('default')
            : null;
        $name = $this->transformKey($name ?? '');

        if ($errors && $errors->has($name)) {
            return array_merge([
                'aria-invalid' => 'true',
                'aria-describedby' => "{$name}Error",
            ], $options);
        }

        return $options;
    }

    private function transformKey($key): string
    {
        return str_replace(['.', '[]', '[', ']'], ['_', '', '.', ''], $key);
    }
}
