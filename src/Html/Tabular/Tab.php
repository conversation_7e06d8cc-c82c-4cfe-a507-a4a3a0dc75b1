<?php

namespace Platform\Html\Tabular;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Str;

class Tab implements Arrayable
{
    const STATUS_ACTIVE = 'active';

    const STATUS_DISABLED = 'disabled';

    private $name;

    private $view;

    private $status;

    /**
     * The id that is used to identify the tab. Automatically generated based on the name.
     *
     * @var string
     */
    private $id;

    /**
     * @var array
     */
    private $classes = [];

    /**
     * Construct a new tab instance.
     *
     * @param  string  $name
     * @param  string  $view
     * @param  string  $status
     */
    public function __construct($name, $view, $status = '')
    {
        $this->name = $name;
        $this->view = $view;
        $this->status = $status;
        $this->id = Str::random(8);
    }

    /**
     * Add a new CSS class for the tab.
     *
     * @return $this
     */
    public function addClass($class)
    {
        $this->classes[] = $class;

        return $this;
    }

    /**
     * Set the status value.
     *
     * @param  string  $status
     * @return Tab
     */
    public function setStatus($status)
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Set the tab to an active status.
     */
    public function setActive()
    {
        return $this->setStatus(self::STATUS_ACTIVE);
    }

    /**
     * Returns true if the tab is currently active.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->status == self::STATUS_ACTIVE;
    }

    /**
     * Set the tab to an disabled status.
     */
    public function setDisabled()
    {
        return $this->setStatus(self::STATUS_DISABLED);
    }

    /**
     * Returns true if the tab is currently disabled.
     *
     * @return bool
     */
    public function isDisabled()
    {
        return $this->status == self::STATUS_DISABLED;
    }

    /**
     * The id that is generated based on the name of the tab.
     *
     * @return string
     */
    public function id()
    {
        return Str::camel($this->id);
    }

    /**
     * @return string
     */
    public function name()
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function view()
    {
        return $this->view;
    }

    /**
     * Returns true if this tab matches the requested tab.
     */
    public function matches(string $tab): bool
    {
        return in_array($tab, [$this->id(), $this->view()]);
    }

    /**
     * @return string
     */
    public function status()
    {
        return $this->status;
    }

    /**
     * Generates the HTML classes necessary for rendering.
     *
     * @return array
     */
    public function classes()
    {
        $classes = $this->classes;

        if (! empty($this->status)) {
            $classes[] = $this->status;
        }

        return $classes;
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return [
            'id' => $this->id(),
            'name' => $this->name(),
            'view' => $this->view(),
            'status' => $this->status(),
            'classes' => $this->classes(),
        ];
    }
}
