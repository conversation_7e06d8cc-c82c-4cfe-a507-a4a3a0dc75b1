<?php

namespace Platform\Html\Tabular;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class Tabular implements Arrayable
{
    /**
     * Tab style - wizard or normal.
     *
     * @var string
     */
    private $style;

    /**
     * The tabs to be registered for the system.
     *
     * @var Tab[]
     */
    private $tabs = [];

    /**
     * @var Request
     */
    private $request;

    /**
     * Tabular constructor.
     */
    public function __construct(Request $request, string $style)
    {
        $this->request = $request;
        $this->style = $style;
    }

    /**
     * Register a new tab with the system. Include the name of the tab, and the view template it should link to.
     */
    public function addTab(Tab $tab)
    {
        $this->tabs[] = $tab;

        if (! $this->request->filled('tab')) {
            return;
        }

        if ($tab->matches($this->request->get('tab'))) {
            $this->setActive($tab->id());
        }
    }

    /**
     * Activate the required tab.
     *
     * @param  string  $tabId
     */
    public function setActive($tabId)
    {
        foreach ($this->tabs as $tab) {
            $tab->setStatus('');
        }

        $this->getTab($tabId)->setActive();
    }

    /**
     * Returns the active tab, if there is one.
     *
     * @return null|Tab
     */
    public function getActive()
    {
        foreach ($this->tabs as $tab) {
            if ($tab->isActive()) {
                return $tab;
            }
        }
    }

    /**
     * Retrieve a tab by its id.
     *
     * @param  string  $tabId
     * @return Tab
     */
    public function getTab($tabId)
    {
        foreach ($this->tabs as $tab) {
            if ($tab->id() == $tabId) {
                return $tab;
            }
        }
    }

    /**
     * Returns the registered tabs.
     *
     * @return array
     */
    public function tabs()
    {
        return $this->tabs;
    }

    /**
     * Render the tabs out for the page. It will render both the navigable tabs up
     * the top, as well as the tab content below this.
     *
     * @return string
     */
    public function render()
    {
        if (! $this->getActive()) {
            $this->setActive($this->tabs[0]->id());
        }

        return view('platform::html.tabular', ['tabular' => $this])->render();
    }

    /**
     * Return the rendering style for this tab setup.
     *
     * @return string
     */
    public function style()
    {
        return $this->style;
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return array_map(function ($tab) {
            return $tab->toArray();
        }, $this->tabs);
    }
}
