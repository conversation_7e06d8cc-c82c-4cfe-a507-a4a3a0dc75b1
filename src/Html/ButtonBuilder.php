<?php

namespace Platform\Html;

use Illuminate\Contracts\View\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\View;

class ButtonBuilder
{
    /**
     * @var PermissionsService
     */
    private $permissions;

    /**
     * @var Factory
     */
    private $view;

    /**
     * ButtonBuilder constructor.
     */
    public function __construct(Permissions $permissions, Factory $view)
    {
        $this->permissions = $permissions;
        $this->view = $view;
    }

    /**
     * @param  string  $url
     * @param  string  $title
     * @return mixed
     */
    public function link($url, $title, array $options = [])
    {
        $icon = '';
        $iconClass = '';
        $size = Arr::get($options, 'size', 'lg');
        $type = Arr::get($options, 'type', '');
        $buttonClass = array_get($options, 'class', '');

        if (isset($options['icon'])) {
            $icon = $options['icon'];
            $iconClass = 'icon';
        }

        if (isset($options['permissions']) && $this->permitted($options['permissions']) === false) {
            return;
        }

        return $this->view->make('html.buttons.link', compact('title', 'url', 'icon', 'iconClass', 'size', 'type', 'buttonClass'))->render();
    }

    /**
     * Determine whether or not the user is permitted to view the element. This is based on the permissions
     * element of the array, the required permissions, and the resource/action we're checking against.
     *
     * @return bool
     */
    protected function permitted(array $permissions)
    {
        return $this->permissions->permits($permissions);
    }
}
