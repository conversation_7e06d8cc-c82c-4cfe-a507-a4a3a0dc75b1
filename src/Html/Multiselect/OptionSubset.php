<?php

namespace Platform\Html\Multiselect;

use Assert\Assertion;

class OptionSubset
{
    /**
     * @var string
     */
    private $type;

    /**
     * @var string
     */
    private $name;

    /**
     * @var string
     */
    private $label;

    /**
     * OptionSubset constructor.
     */
    public function __construct(string $type, string $name, string $label)
    {
        Assertion::inArray($type, ['radio', 'checkbox']);

        $this->type = $type;
        $this->name = $name;
        $this->label = $label;
    }

    public function type(): string
    {
        return $this->type;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function label(): string
    {
        return $this->label;
    }
}
