<?php

namespace Platform\Html\Multiselect;

final class SubsetMultiselect extends Multiselect
{
    /**
     * The subset of data that has been previously selected.
     *
     * @var array
     */
    private $subsetSelected = [];

    /**
     * @var OptionSubset
     */
    private $subset;

    /**
     * SimpleMultiselect constructor.
     *
     * @param  string  $name  Name to be used for the form elements.
     * @param  array  $options  key->value array of options to be displayed.
     * @param  OptionSubset  $subset  The subset options to be displayed for each option of the component.
     */
    public function __construct(string $name, array $options, OptionSubset $subset)
    {
        $this->name = $name;
        $this->options = $options;
        $this->subset = $subset;
    }

    /**
     * Set the selected options (both normal and subset) for the subset multiselect component.
     */
    public function setSelected(array $selected, array $subsetSelected)
    {
        $this->selected = $selected;
        $this->subsetSelected = $subsetSelected;
    }

    /**
     * Returns the option subset if it's been provided.
     */
    public function subset(): OptionSubset
    {
        return $this->subset;
    }

    /**
     * Returns the selected subset options.
     */
    public function subsetSelected(): array
    {
        return $this->subsetSelected;
    }

    /**
     * Return the location of the template to be used for rendering.
     */
    protected function template(): string
    {
        return 'platform::html.form.multiselect.subset';
    }
}
