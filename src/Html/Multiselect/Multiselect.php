<?php

namespace Platform\Html\Multiselect;

use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Request;

abstract class Multiselect implements Htmlable
{
    /**
     * @var array
     */
    protected $options;

    /**
     * @var array
     */
    protected $config = [];

    /**
     * Name of the multiselect component.
     *
     * @var string
     */
    protected $name;

    /**
     * The options that have been previously selected.
     *
     * @var array
     */
    protected $selected = [];

    /**
     * Configure the multiselect component.
     */
    public function configure(array $config)
    {
        $this->config = $config;
    }

    /**
     * Returns the selected options.
     */
    public function selected(): array
    {
        return $this->selected;
    }

    /**
     * Return the options registered for the multiselect.
     */
    public function options(): array
    {
        return $this->options;
    }

    /**
     * Returns true if the multiselect counter should be visible.
     */
    public function counter(): bool
    {
        return $this->get('counter');
    }

    /**
     * Returns true if the filter box for searching multiselect options should be visible.
     */
    public function filter(): bool
    {
        return $this->get('filter');
    }

    /**
     * Return true if the "select all" button should be made visible to the user.
     */
    public function selectAll(): bool
    {
        return $this->get('selectAll');
    }

    /**
     * Returns the name of the component to be used in the form.
     */
    public function name(): string
    {
        return $this->name;
    }

    /**
     * Return a configuration option, or default if not available.
     *
     * @param  bool  $default
     * @return mixed
     */
    protected function get(string $key, $default = false)
    {
        return Arr::get($this->config, $key, $default);
    }

    /**
     * Returns a configuration option.
     *
     * @return mixed
     */
    public function config(string $key)
    {
        return Arr::get($this->config, $key);
    }

    /**
     * Render the multiselect component.
     *
     * @return mixed
     */
    public function render()
    {
        return view($this->template(), ['multiselect' => $this]);
    }

    /**
     * Returns the rendered html for the component.
     *
     * @return mixed
     */
    public function toHtml()
    {
        return $this->render()->render();
    }

    /**
     * Returns true if the value provided has been previously selected.
     *
     * @param  mixed  $value
     * @return bool
     */
    public function optionSelected($value)
    {
        return in_array($value, $this->selected()) || in_array($value, Request::old(form_to_dot_notation($this->name()), []));
    }

    /**
     * Return the location of the template to be used for rendering.
     */
    abstract protected function template(): string;
}
