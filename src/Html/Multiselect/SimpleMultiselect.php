<?php

namespace Platform\Html\Multiselect;

final class SimpleMultiselect extends Multiselect
{
    /**
     * SimpleMultiselect constructor.
     *
     * @param  string  $name  Name to be used for the form elements.
     * @param  array  $options  key->value array of options to be displayed.
     */
    public function __construct(string $name, array $options)
    {
        $this->name = $name;
        $this->options = $options;
    }

    /**
     * Set the selected options for the simple multiselect.
     */
    public function setSelected(array $selected)
    {
        $this->selected = $selected;
    }

    /**
     * Return the location of the template to be used for rendering.
     */
    protected function template(): string
    {
        return 'platform::html.form.multiselect.simple';
    }
}
