<?php

namespace Platform\Html;

use Facades\Platform\Strings\Output;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;

class SelectOptions
{
    /** @var Collection */
    private $items;

    /** @var array */
    private $fields;

    /** @var bool */
    private $allowEmpty = false;

    /** @var bool */
    private $sort = false;

    protected function __construct($items)
    {
        $this->items = $items;
    }

    /**
     * Build array of options for <select> from given list of items (objects).
     *
     * @param  Collection|array  $items
     */
    public static function from($items): SelectOptions
    {
        return new SelectOptions(collect($items));
    }

    /**
     * Generate options for <select> field.
     */
    public function generate(): array
    {
        $options = [];
        [$valueField, $textField] = $this->fields ?: ['id', 'name'];

        foreach ($this->items as $item) {
            $options[$this->optionValue($item, $valueField)] = $this->optionText($item, $textField);
        }

        if ($this->sort) {
            $ascending = $this->sort === 1;
            uasort($options, function ($a, $b) use ($ascending) {
                return ($ascending ? 1 : -1) * (strtolower((string) $a) <=> strtolower((string) $b));
            });
        }

        if (! $this->allowEmpty) {
            return $options;
        }

        // Append empty, preserving keys
        return ['' => ($this->allowEmpty !== true ? $this->allowEmpty : '')] + $options;
    }

    /**
     * Returns value for the select option, based on $valueField attribute.
     *
     * @param  mixed  $item
     */
    public function optionValue($item, string $valueField): string
    {
        return (string) $item->{$valueField};
    }

    /**
     * Returns text for the select option, based on $textField attribute.
     *
     * @param  mixed  $item
     * @return string|HtmlString
     */
    public function optionText($item, string $textField)
    {
        return Output::text($item->{$textField});
    }

    /**
     * Extracts from collection given fields and treats them as option value and text.
     *
     * Defaults are ['id', 'name'].
     */
    public function pluckFields(?array $fields = null): SelectOptions
    {
        if ($fields && count($fields) === 2) {
            $this->fields = $fields;
        }

        return $this;
    }

    /**
     * Add default empty text.
     *
     * @param  mixed|bool  $allow  if non-boolean, it will be used as a text for the empty option
     */
    public function allowEmpty($allow): SelectOptions
    {
        $this->allowEmpty = $allow;

        return $this;
    }

    /**
     * Sort options alphabetically.
     */
    public function sort(bool $ascending = true): SelectOptions
    {
        $this->sort = $ascending ? 1 : -1;

        return $this;
    }
}
