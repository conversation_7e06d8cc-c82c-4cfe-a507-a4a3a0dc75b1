<?php

namespace Platform\Html;

class Markdown
{
    public function __construct(private Parsedown $parsedown) {}

    public function parse(string $markdown): string
    {
        if ($this->lineCount($markdown) === 1) {
            return $this->inline($markdown);
        }

        return $this->parsedown
            ->setBreaksEnabled(true)
            ->text($markdown);
    }

    public function pdf(string $markdown): string
    {
        return $this->parsedown
            ->setBreaksEnabled(true)
            ->embedVideos(false)
            ->text($markdown);
    }

    public function inline(string $markdown): string
    {
        // Remove leading spaces (which cause <pre><code> blocks)
        $markdown = preg_replace('/^[ \t]+/m', '', $markdown);

        $html = $this->parsedown->text($markdown);

        // Remove outer <p> wrappers
        $html = preg_replace('/^<p>(.*?)<\/p>$/s', '$1', $html);

        // Remove remaining <p> tags and replace with <br> tags
        $html = preg_replace('/<p>/', '', $html);
        $html = preg_replace('/<\/p>/', '<br>', $html);
        $html = str_replace(["\r\n", "\n\r"], '<br>', $html);
        // Replace any remaining newlines that are not between HTML tags with <br>
        $html = preg_replace('/(?<!>)\n(?!<)/', '<br>', $html);

        return trim($html);
    }

    /**
     * Line count is somewhat of a misnomer, because what we're really looking for is how many "root" elements there
     * might be, which are generally separated by two line breaks. However, this does give us the outcome we're
     * looking for, as we want to avoid wrapping p tags if there's only one line in the markdown. //- Kirk
     */
    private function lineCount(string $markdown): int
    {
        return count(explode("\n\n", str_replace(["\r\n", "\r"], "\n", $markdown)));
    }
}
