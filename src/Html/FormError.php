<?php

namespace Platform\Html;

use Illuminate\Contracts\Support\MessageBag;
use Illuminate\Contracts\View\Factory as ViewFactory;

class FormError
{
    /**
     * @var MessageBag
     */
    protected $errors;

    /**
     * @var ViewFactory
     */
    private $view;

    public function __construct(ViewFactory $factory)
    {
        // Pull the default MessageBag out of the ViewErrorBag stored in the view 'errors' parameter.
        $this->errors = $factory->getShared()['errors']->getBag('default');
        $this->view = $factory;
    }

    /**
     * Helper function to return the specified css class if there are errors for the named field.
     *
     * @param  string  $name  Name of the field to check for errors for.
     * @param  string  $class  Class name to return, if not 'error'.
     * @return string
     */
    public function classIfError($name, $class = 'error')
    {
        return $this->hasErrors($name) ? $class : '';
    }

    /**
     * Helper function to return the specified css class if there are errors for the translated named field.
     */
    public function classIfErrorTranslated(string $name, string $lang, string $class = 'error'): string
    {
        return $this->classIfError("translated[$name][$lang]", $class);
    }

    /**
     * Returns a nicely rendered error message.
     *
     * @param  string  $name
     * @return string
     */
    public function message($name, $isHtml = false)
    {
        $name = $this->transformKey($name);

        return $this->view->make('partials.errors.inline', [
            'id' => "{$name}Error",
            'type' => 'error',
            'message' => $this->errors->has($name) ? $this->errors->first($name) : null,
            'isHtml' => $isHtml,
            'hasError' => $this->errors->has($name),
        ])->render();
    }

    /**
     * Returns a nicely rendered error message for translated fields.
     */
    public function messageTranslated(string $name, string $lang, bool $isHtml = false): string
    {
        return $this->message("translated[$name][$lang]", $isHtml);
    }

    public function hasErrors($name): bool
    {
        return $this->errors->has($this->transformKey($name));
    }

    /**
     * Converts the raw html name string with potential [] arrays into dot-notated format.
     *
     * @param  string  $name
     * @return string
     */
    protected function transformKey($name)
    {
        return str_replace(['.', '[]', '[', ']'], ['_', '', '.', ''], $name);
    }
}
