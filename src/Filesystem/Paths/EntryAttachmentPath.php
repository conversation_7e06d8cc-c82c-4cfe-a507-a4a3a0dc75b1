<?php

namespace Platform\Filesystem\Paths;

class EntryAttachmentPath extends Path
{
    use HashPath;

    /**
     * Creates a new path based on entry attachment information.
     *
     * @param  int  $entryId
     * @param  int  $attachmentId
     * @param  string  $extension
     * @return Path
     */
    public static function forAttachment($entryId, $attachmentId, $extension)
    {
        $filePath = FileDirPath::fromHash();

        return new static($filePath."/entry-{$entryId}-{$attachmentId}.{$extension}");
    }
}
