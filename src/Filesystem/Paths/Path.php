<?php

namespace Platform\Filesystem\Paths;

use Assert\Assertion;

class Path
{
    protected $path;

    /**
     * Construct a new path instance.
     *
     * @param  string  $path
     */
    public function __construct($path)
    {
        Assertion::string($path);

        $this->path = $path;
    }

    /**
     * Return the full path to the file.
     *
     * @return string
     */
    public function path()
    {
        return $this->path;
    }

    /**
     * Comparison function - check to see if given path is identical to another path object.
     *
     * @return bool
     */
    public function equals(Path $path)
    {
        return $path == $this;
    }

    /**
     * All paths should convert nicely to strings.
     *
     * @return string
     */
    public function __toString()
    {
        return $this->path;
    }
}
