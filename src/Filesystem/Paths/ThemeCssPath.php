<?php

namespace Platform\Filesystem\Paths;

use Eloquence\Behaviours\Slug;
use Illuminate\Support\Str;

class ThemeCssPath extends Path
{
    /**
     * Create a new ThemeCssPath instance and a hash for creating CSS files.
     *
     * @return Path
     */
    public static function fromSlug(Slug $accountSlug)
    {
        $themePath = ThemePath::fromSlug($accountSlug);
        $hash = Str::random(32);

        return new static($themePath."/theme-{$hash}.css");
    }
}
