<?php

namespace Platform\Filesystem;

use Illuminate\Contracts\Filesystem\Factory;
use League\Flysystem\FilesystemException;
use League\Flysystem\FilesystemOperator;

class RemoteFileRetriever
{
    public function __construct(protected Factory $filesystem, protected FilesystemOperator $mountManager)
    {
    }

    /**
     * Retrieve and copy a file from S3 storage to local storage
     *
     * @throws FilesystemException
     */
    public function s3ToLocal(string $s3FilePath, string $localFilePath): void
    {
        $this->mountManager->copy('s3://'.$s3FilePath, 'local://'.$localFilePath);
    }
}
