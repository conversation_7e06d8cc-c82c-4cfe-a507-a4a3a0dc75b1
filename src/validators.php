<?php

use Illuminate\Support\Facades\Validator;

/**
 * domain: any string that is a valid domain name
 */
Validator::extend('domain', function ($attribute, $value): bool {
    return (bool) preg_match('/^[0-9a-z][0-9a-z-]*[0-9a-z]\.[0-9a-z][0-9a-z-.]*[0-9a-z]$/i', $value);
});

/**
 * domain_reserved: the first portion of the domain (i.e. bit before the dot) must not be reserved
 */
Validator::extend('domain_reserved', function ($attribute, $value): bool {
    $parts = explode('.', $value);
    $sub = array_shift($parts);

    return ! in_array($sub, config('domains.reserved'));
});

/**
 * domain_whitelabel: anything after the first dot must be in the whitelist, or the whole string must be in the whitelist
 */
Validator::extend('domain_whitelabel', function ($attribute, $value): bool {
    $parts = explode('.', $value, 2);
    $root = array_pop($parts);

    return in_array($root, $options = config('domains.white_label')) || in_array($value, $options);
});

/**
 * subdomain: any string that is a valid subdomain
 */
Validator::extend('subdomain', function ($attribute, $value): bool {
    return (bool) preg_match('/^([0-9a-z]|[0-9a-z][0-9a-z-]*[0-9a-z])$/i', $value);
});
