<?php

namespace Platform\Encryption;

use Illuminate\Contracts\Encryption\Encrypter as EncrypterContract;

class Encrypter implements EncrypterContract
{
    const PREFIX_STANDARD = 's:';

    const PREFIX_MAXIMUM = 'm:';

    /** @var EncrypterContract */
    private $standard;

    /** @var EncrypterContract */
    private $maximum;

    public function __construct(EncrypterContract $standard, EncrypterContract $maximum)
    {
        $this->standard = $standard;
        $this->maximum = $maximum;
    }

    /**
     * Encrypt payload at the standard security level.
     *
     * @param  string  $payload
     * @param  bool  $serialize
     */
    public function encrypt($payload, $serialize = true): string
    {
        return self::PREFIX_STANDARD.$this->standard->encrypt($payload, $serialize);
    }

    /**
     * Encrypt payload at the maximum security level.
     *
     * @param  bool  $serialize
     */
    public function maximum($payload, $serialize = true): string
    {
        return self::PREFIX_MAXIMUM.$this->maximum->encrypt($payload, $serialize);
    }

    /**
     * @param  string  $payload
     * @param  bool  $unserialize
     * @return string
     *
     * @throws \Platform\Encryption\UnableToDecryptPayload
     */
    public function decrypt($payload, $unserialize = true)
    {
        $prefix = substr($payload, 0, 2);
        $payload = substr($payload, 2);

        switch ($prefix) {
            case self::PREFIX_STANDARD:
                return $this->standard->decrypt($payload, $unserialize);
            case self::PREFIX_MAXIMUM:
                return $this->maximum->decrypt($payload, $unserialize);
            default:
                throw new UnableToDecryptPayload('Unknown security level');
        }
    }

    public function getKey()
    {
        // TODO: Implement getKey() method.
    }

    public function getAllKeys()
    {
        // TODO: Implement getAllKeys() method.
    }

    public function getPreviousKeys()
    {
        // TODO: Implement getPreviousKeys() method.
    }
}
