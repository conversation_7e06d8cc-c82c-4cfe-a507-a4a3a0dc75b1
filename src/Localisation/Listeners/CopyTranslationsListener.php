<?php

namespace Platform\Localisation\Listeners;

use Platform\Events\ModelWasCopied;
use Platform\Localisation\CopyTranslationsService;
use Tectonic\Localisation\Contracts\Translatable;

/**
 * @codeCoverageIgnore
 */
class CopyTranslationsListener
{
    /**
     * @var CopyTranslationsService
     */
    protected $copyTranslations;

    public function __construct(CopyTranslationsService $copyTranslations)
    {
        $this->copyTranslations = $copyTranslations;
    }

    /**
     * Listens for the ModelWasCopied Event.
     */
    public function whenModelWasCopied(ModelWasCopied $event)
    {
        if (! ($event->original instanceof Translatable)) {
            return;
        }

        $this->copyTranslations->copy($event->original, $event->copy, true);
    }
}
