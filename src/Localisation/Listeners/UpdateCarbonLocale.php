<?php

namespace Platform\Localisation\Listeners;

use Carbon\Carbon;
use Illuminate\Foundation\Events\LocaleUpdated;
use Platform\Language\Facades\LanguageConfig;

class UpdateCarbonLocale
{
    public function handle(LocaleUpdated $event)
    {
        if ($systemLocale = LanguageConfig::system($event->locale)) {
            setlocale(LC_TIME, $systemLocale);
        }

        if ($locale = LanguageConfig::carbon($event->locale)) {
            Carbon::setLocale($locale);
        }
    }
}
