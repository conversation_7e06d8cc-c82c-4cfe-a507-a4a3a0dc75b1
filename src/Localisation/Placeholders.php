<?php

namespace Platform\Localisation;

class Placeholders
{
    /**
     * @return array
     */
    public static function allToCurly(array $strings)
    {
        return array_map(function ($string) {
            return self::toCurly($string);
        }, $strings);
    }

    /**
     * Converts our lang placeholders (:string) into the %{...} format.
     *
     * @param  string  $string
     * @return string
     */
    public static function toCurly($string)
    {
        return preg_replace(
            ['#:(\w+)\+#', '#:(\w+)#'],
            ['%{$1_plural}', '%{$1}'],
            $string
        );
    }

    /**
     * Converts placeholders from the %{...} format into our format.
     *
     * @param  string  $string
     * @return string
     */
    public static function fromCurly($string)
    {
        return preg_replace(
            ['#%{([^}]+)_plural}#', '#%{([^}]+)}#'],
            [':$1+', ':$1'],
            $string
        );
    }
}
