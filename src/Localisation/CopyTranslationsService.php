<?php

namespace Platform\Localisation;

use Illuminate\Translation\Translator as Lang;
use Tectonic\LaravelLocalisation\Database\TranslationService;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tectonic\Localisation\Contracts\Translatable;

class CopyTranslationsService
{
    /**
     * @var TranslationService
     */
    protected $translationService;

    /**
     * @var Lang
     */
    protected $lang;

    /**
     * @var Engine
     */
    private $translator;

    public function __construct(TranslationService $translationService, Lang $lang, Engine $translator)
    {
        $this->translationService = $translationService;
        $this->lang = $lang;
        $this->translator = $translator;
    }

    /**
     * Copies translations from one class to another.
     */
    public function copy(Translatable $from, Translatable $to, bool $append = true, ?string $resource = null, array $resourceTranslations = [])
    {
        $translations = $this->swapArray($this->translator->translate($from)->translated);

        if ($append && $resource && $resourceTranslations) {
            $translations = $this->appendCopyStringsFromResource($to, $translations, $resourceTranslations);
        } elseif ($append) {
            $translations = $this->appendCopyStrings($to, $translations);
        }

        $this->translationService->sync($to, $translations);
    }

    /**
     * Swaps the array around to make it usable for injecting into the translation service.
     *
     * @param  array  $rawTranslations
     * @return array
     */
    protected function swapArray($rawTranslations)
    {
        $translations = [];

        foreach ($rawTranslations as $locale => $translation) {
            foreach ($translation as $field => $value) {
                if (! isset($translations[$field])) {
                    $translations[$field] = [];
                }

                $translations[$field][$locale] = $value;
            }
        }

        return $translations;
    }

    /**
     * @return array
     */
    protected function appendCopyStrings(Translatable $model, array $translations)
    {
        if (! method_exists($model, 'getTranslatableCopyAppendFields')) {
            return $translations;
        }

        foreach ($model->getTranslatableCopyAppendFields() as $field => $langKey) {
            if (isset($translations[$field]) && is_array($translations[$field])) {
                foreach ($translations[$field] as $lang => $string) {
                    $translations[$field][$lang] = $string.$this->lang->get($langKey, [], $lang);
                }
            }
        }

        return $translations;
    }

    protected function appendCopyStringsFromResource(Translatable $model, array $translations, array $resourceTranslations): array
    {
        if (! method_exists($model, 'getTranslatableCopyAppendFieldsFromResource')) {
            return $translations;
        }

        foreach ($model->getTranslatableCopyAppendFieldsFromResource() as $field => $values) {
            $key = $values['key'];
            $resource = $values['resource'];
            $resourceKey = $values['resourceKey'];
            $modify = $values['modify'] ?? null;

            if (isset($translations[$field]) && is_array($translations[$field])) {
                foreach ($translations[$field] as $lang => $string) {
                    $resourceValue = $resourceTranslations[$lang][$resourceKey] ?? null;
                    $copyString = $resourceValue ? $this->lang->get($key, [$resource => $resourceValue], $lang) : '';
                    $string = $modify instanceof \Closure ? $modify($string) : $string;
                    $translations[$field][$lang] = $string.$copyString;
                }
            }
        }

        return $translations;
    }
}
