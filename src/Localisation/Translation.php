<?php

namespace Platform\Localisation;

use Platform\Database\Eloquent\Model;

class Translation extends Model
{
    /**
     * The fillable elements of the record.
     *
     * @var array
     */
    public $fillable = ['language', 'foreignId', 'resource', 'field', 'value'];

    /**
     * Trim leading and trailing whitespace from values
     */
    public function setValueAttribute($value)
    {
        $this->attributes['value'] = is_string($value) ? trim($value) : json_encode($value);
    }
}
