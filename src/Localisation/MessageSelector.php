<?php

namespace Platform\Localisation;

use Illuminate\Translation\MessageSelector as IlluminatedMessageSelector;

class MessageSelector extends IlluminatedMessageSelector
{
    /**
     * Get the index to use for pluralization.
     *
     * @param  string  $locale
     * @param  int  $number
     * @return int
     */
    public function getPluralIndex($locale, $number)
    {
        if (config('lang.'.$locale)) {
            $locale = config('lang.'.$locale);
        }

        return parent::getPluralIndex($locale, $number);
    }
}
