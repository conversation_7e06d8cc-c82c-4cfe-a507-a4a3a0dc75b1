<?php

namespace Platform\Tokens;

use Illuminate\Contracts\Cache\Repository as Cache;

class TokenManager
{
    const PREFIX = 'token:';

    /**
     * @var Cache
     */
    protected $cache;

    public function __construct(Cache $cache)
    {
        $this->cache = $cache;
    }

    public function create(Token $type): string
    {
        $token = $type->generate();

        $this->cache->put(self::PREFIX.$token, $type, now()->addMinutes($type->expires()));

        return $token;
    }

    public function forever(Token $type): string
    {
        $token = $type->generate();

        $this->cache->forever(self::PREFIX.$token, $type);

        return $token;
    }

    public function has(string $key, string $type, ?int $accountId = null): bool
    {
        $token = $this->cache->get(self::PREFIX.$key);

        return $token && ! $this->invalidType($token, $type) && ! $this->invalidAccount($token, $accountId);
    }

    /**
     * @throws TokenNotFoundException
     */
    public function pull(string $key, string $type, ?int $accountId = null): Token
    {
        /** @var Token $token */
        $token = $this->cache->pull(self::PREFIX.$key);

        if (! $token || $this->invalidType($token, $type) || $this->invalidAccount($token, $accountId)) {
            throw new TokenNotFoundException("Unknown token '{$key}' requested from Token Repository.");
        }

        return $token;
    }

    /**
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws TokenNotFoundException
     */
    public function get(string $key, string $type, ?int $accountId = null): Token
    {
        /** @var Token $token */
        $token = $this->cache->get(self::PREFIX.$key);

        if (! $token || $this->invalidType($token, $type) || $this->invalidAccount($token, $accountId)) {
            throw new TokenNotFoundException("Unknown token '{$key}' requested from Token Repository.");
        }

        return $token;
    }

    protected function invalidType(Token $token, string $type): bool
    {
        return $type !== get_class($token);
    }

    protected function invalidAccount(Token $token, ?int $accountId = null): bool
    {
        return $token->accountId() !== $accountId;
    }
}
