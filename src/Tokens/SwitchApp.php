<?php

namespace Platform\Tokens;

class SwitchApp implements Token
{
    use StringToken;

    /** @var string */
    public $globalUserId;

    /** @var bool */
    public $secureSession;

    public function __construct(string $globalUserId, bool $secureSession = false)
    {
        $this->globalUserId = $globalUserId;
        $this->secureSession = $secureSession;
    }

    /**
     * The number of minutes until the token expires.
     */
    public function expires(): int
    {
        return 1;
    }

    /**
     * The accountId the token is limited to, or null for no account limit.
     *
     * @return int|null
     */
    public function accountId()
    {
        return null;
    }
}
