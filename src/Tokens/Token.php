<?php

namespace Platform\Tokens;

interface Token
{
    /**
     * The number of minutes until the token expires.
     */
    public function expires(): int;

    /**
     * The accountId the token is limited to, or null for no account limit.
     *
     * @return int|null
     */
    public function accountId();

    /**
     * Generate random token string.
     *
     * @return string|int
     */
    public function generate();
}
