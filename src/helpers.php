<?php

use Facades\Platform\Strings\Output;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Platform\Html\SelectOptions;
use Psr\Http\Message\ResponseInterface;
use Spatie\Html\BaseElement;
use Spatie\Html\Elements\A;

if (! function_exists('current_url')) {
    /**
     * Returns the full current relative URL for the current request.
     *
     * @return string
     */
    function current_url(array $append = [])
    {
        if ($append) {
            $parameters = array_merge(Request::except('_pjax'), $append);
            $query = http_build_query($parameters);
        } else {
            $query = Request::getQueryString();
        }

        return start_with(Request::path().($query ? '?'.$query : ''), '/');
    }
}

if (! function_exists('for_select')) {
    /**
     * Returns a select-friendly array based on the collection and fields required, provided.
     *
     * @param  Collection|array  $collection
     * @param  bool  $empty  Specify true if you would like an empty value created for selection.
     * @param  bool  $sort
     * @return array
     */
    function for_select($collection, ?array $fields = null, $empty = false, $sort = false)
    {
        $options = SelectOptions::from($collection)
            ->pluckFields($fields)
            ->allowEmpty($empty);

        if ($sort) {
            $options->sort();
        }

        return $options->generate();
    }
}

if (! function_exists('for_select_sorted')) {
    /**
     * Returns a sorted <select> friendly array based on the provided collection, with the options of for_select().
     *
     * @param  Collection|array  $collection
     * @param  bool  $empty
     * @return array
     */
    function for_select_sorted($collection, ?array $fields = null, $empty = false)
    {
        return for_select($collection, $fields, $empty, true);
    }
}

if (! function_exists('external_url')) {
    /**
     * Saves the provided URL in the session and returns an external redirect URL for the user.
     *
     * Uses md5 on the URL to generate a repeatable token - to save spamming the session on each page load.
     * We don't need to worry about security as it's being stored in a whitelist.
     * Collisions shouldn't be an issue, due to URL length... and I'm not sure why an attacker would bother...
     */
    function external_url(string $url, ?int $expiry = null, $route = 'redirect.token'): string
    {
        $token = md5($url);
        $key = 'redirect-external:'.$token;

        if ($expiry) {
            // Bump expiry time on cache entry (otherwise it could expire within a second!)
            Cache::forget($key);
            Cache::put($key, $url, now()->addMinutes($expiry));
        } else {
            Session::put($key, $url);
        }

        if (Route::has($route)) {
            return route($route, $token);
        }

        return $url;
    }
}

if (! function_exists('obfuscate')) {
    /**
     * Obfuscates the value into a string that can be passed to the browser, and passed through `clarify()`.
     *
     * @param  mixed  $raw
     * @return string
     */
    function obfuscate($raw)
    {
        // Wrap in HtmlString to prevent sanitisation
        return new HtmlString(
            base64_encode(json_encode($raw, JSON_NUMERIC_CHECK))
        );
    }
}

if (! function_exists('prefix')) {
    /**
     * Provides a function that will prefix a string with the provided value, if it's not already there.
     */
    function prefix(string $string, string $prefix): string
    {
        if (stripos($string, $prefix) === 0) {
            return $string;
        }

        return $prefix.$string;
    }
}

if (! function_exists('query_parameters')) {
    /**
     * Returns true if any of the provided query parameters are set (and not empty) in the request.
     * Intended for use toggling on/off the filtertron by default.
     *
     * @return bool
     */
    function query_parameters(array $parameters)
    {
        return count(array_filter(Request::only($parameters), 'strlen')) > 0;
    }
}

if (! function_exists('sort_config_list')) {
    /**
     * Retrieves a sorted list of translated options, from the authoritative config list.
     *
     * The config array (first parameter) is retrieved and each value is used to
     * retrieve the translation using the lang prefix provided (second parameter).
     * The list is then sorted alphabetically before being returned.
     *
     * @param  string  $config  Configuration key for option values.
     * @param  string  $lang  Lang/translation prefix for the translated labels.
     * @return array
     */
    function sort_config_list($config, $lang)
    {
        return collect(config($config))->keyBy(function ($option) {
            return $option;
        })->map(function ($option) use ($lang) {
            $option = strtolower($option);
            $translation = trans("{$lang}.{$option}");

            if (app('translator')->has("{$lang}.{$option}")) {
                return $translation;
            }

            return trans("{$lang}.".str_replace('-', '_', $option));
        })->sort()->toArray();
    }
}

if (! function_exists('config_list')) {
    /**
     * Retrieves a sorted list of translated options, from the authoritative config list.
     *
     * @param  string  $config  Configuration key for option values.
     * @param  string  $lang  Lang/translation prefix for the translated labels.
     * @return array
     */
    function config_list($config, $lang)
    {
        return collect(config($config))->keyBy(function ($option) {
            return $option;
        })->map(function ($option) use ($lang) {
            return trans("{$lang}.{$option}");
        })->toArray();
    }
}

if (! function_exists('sentence_case')) {
    /**
     * Apply sentence case to the string.
     * When $force is set to `true`, lowercase will be applied to the rest of the sentence
     *
     * @param  string  $string
     * @param  bool  $force
     * @return string
     */
    function sentence_case($string, $force = false)
    {
        $regex = "(.*?)(\.\s+|\s*$)";

        return mb_ereg_replace_callback(
            $regex,
            function ($matches) use ($force) {
                $sentence = ! $force ? $matches[1] : mb_strtolower($matches[1]);

                return Str::ucfirst($sentence).$matches[2];
            },
            $string
        );
    }
}

if (! function_exists('start_with')) {
    /**
     * Start a string with the provided prefix, if it doesn't already have it.
     */
    function start_with(string $string, string $prefix): string
    {
        if (stripos($string, $prefix) === 0) {
            return $string;
        }

        return $prefix.$string;
    }
}

if (! function_exists('to_kb')) {
    /**
     * Convert megabytes (Mb) to kilobytes (Kb)
     *
     * @param  float  $megabytes
     * @return float
     */
    function to_kb($megabytes)
    {
        return intval(round($megabytes * 1024));
    }
}

if (! function_exists('to_bytes')) {
    /**
     * Convert kilobytes (Kb) to bytes
     *
     * @param  float  $kilobytes
     * @return float
     */
    function to_bytes($kilobytes)
    {
        return intval(round($kilobytes * 1024));
    }
}

if (! function_exists('form_to_dot_notation')) {
    /**
     * Converts a string representing a form array to dot notation.
     *
     * Input: values[ypEwboGD]
     * Output: values.ypEwboGD
     *
     * @param  string  $string
     * @return string
     */
    function form_to_dot_notation($string)
    {
        return preg_replace('/\[([^\[\]]+)\]/', '.$1', $string);
    }
}

if (! function_exists('full_timezone')) {
    /**
     * Returns the full timezone, including the time offset.
     *
     * @param  string  $timezone
     * @return string
     */
    function full_timezone($timezone)
    {
        $offset = timezone_offset($timezone);

        return "(GMT {$offset}) {$timezone}";
    }
}

if (! function_exists('timezone_offset')) {
    /**
     * Get the offset for a given timezone.
     *
     * @param  string  $timezone
     * @return string
     */
    function timezone_offset($timezone)
    {
        $now = new \DateTime(timezone: new \DateTimeZone($timezone));
        $offset = format_timezone_offset($now->getOffset() / 3600);

        return $offset;
    }
}

if (! function_exists('format_timezone_offset')) {
    /**
     * Converts a timezone offset to a nicer format.
     *
     * @param  string  $offset
     * @return string
     */
    function format_timezone_offset($offset)
    {
        $string = str_pad(number_format(str_replace('-', '', $offset), 2, ':', ''), 5, '0', STR_PAD_LEFT);
        $timeOffset = str_replace(':50', ':30', $string);
        $timeOffset = $offset >= 0 ? "+{$timeOffset}" : "-{$timeOffset}";

        return $timeOffset;
    }
}

if (! function_exists('convert_date_to_utc')) {
    /**
     * Converts the provided date/datetime from the set timezone to UTC.
     *
     * @param  string  $date
     * @param  string  $timezone
     * @return Carbon
     */
    function convert_date_to_utc($date, $timezone)
    {
        $date = \Carbon\Carbon::createFromFormat('Y-m-d H:i', $date, $timezone);

        return $date->setTimezone('UTC');
    }
}

if (! function_exists('convert_date_to_timezone')) {
    /**
     * Converts the provided date/datetime from UTC to the required timezone.
     *
     * @param  string  $date
     * @param  string  $timezone
     * @return Carbon
     */
    function convert_date_to_timezone($date, $timezone)
    {
        $timezone = $timezone ?? 'UTC';
        if (is_null($date)) {
            return;
        }

        $date = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $date, 'UTC');

        return $date->setTimezone($timezone);
    }
}

if (! function_exists('date_value')) {
    /**
     * If a given date is set to an empty string, nullify that value. Otherwise convert to UTC.
     *
     * @param  string  $date
     * @param  string  $timezone
     * @return null|string
     */
    function date_value($date, $timezone)
    {
        if (empty($date)) {
            $date = null;
        } else {
            $date = convert_date_to_utc($date, $timezone);
        }

        return $date;
    }
}

if (! function_exists('uses_trait')) {
    /**
     * Returns true if the class uses the trait
     *
     * @param  string|object  $class
     */
    function uses_trait($class, string $trait): bool
    {
        $class = is_object($class) ? get_class($class) : $class;

        return in_array($trait, class_uses_recursive($class));
    }
}

if (! function_exists('format_size')) {
    /**
     * Formats the file size, in bytes, into a friendly string.
     *
     * Inspired by / stolen from https://gist.github.com/grena/5977137
     *
     * @return string
     */
    function format_size(int $size)
    {
        $units = ['B', 'KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
        $power = floor(log($size, 1024));
        $precision = $size >= 1048576 ? 1 : 0;
        $number = $size > 0 ? $size / (1024 ** $power) : 0;

        return number_format($number, $precision).' '.$units[(int) $power];
    }
}

if (! function_exists('format_time')) {
    /**
     * Formats the time, in seconds, into a friendly string.
     *
     * @return string
     */
    function format_time(int $time)
    {
        $hours = str_pad(floor($time / 3600), 2, '0', STR_PAD_LEFT);
        $minutes = str_pad(floor((int) ($time / 60) % 60), 2, '0', STR_PAD_LEFT);
        $seconds = str_pad(floor($time % 60), 2, '0', STR_PAD_LEFT);

        return "{$hours}:{$minutes}:{$seconds}";
    }
}

if (! function_exists('parse_response')) {
    /**
     * Parse \Psr\Http\Message\ResponseInterface into an array by decoding the json body content.
     *
     * @return mixed
     */
    function parse_response(ResponseInterface $response)
    {
        return json_decode($response->getBody(), true);
    }
}

if (! function_exists('remembrance')) {
    /**
     * Allows to retrieve saved Filtertron parameters for any given URL.
     *
     * @param  string  $url
     * @return string|null
     */
    function remembrance($url)
    {
        return \Platform\Http\Middleware\Remembrance::forUrl($url);
    }
}

if (! function_exists('key_value_pairs')) {
    /**
     * Transforms an array into an array of arrays (key/value pairs).
     *
     * @param  string  $key
     * @param  string  $value
     * @return array
     */
    function key_value_pairs(array $array, $key = 'key', $value = 'value')
    {
        return array_map(function ($k, $v) use ($key, $value) {
            return [$key => $k, $value => $v];
        }, array_keys($array), $array);
    }
}

if (! function_exists('timezones')) {
    /**
     * Generates and caches an array of timezones`
     */
    function timezones()
    {
        /**
         * Use cache to only load locations once every day.
         * It's an expensive operation to generate, but isn't likely to change, so we can push it out
         * to a 1 day refresh safely.
         */
        return Cache::remember('Form.macro.timezone.list', now()->addDays(1), function () {
            $timezones = DateTimeZone::listIdentifiers();
            $locations = [];

            // First we create the list of timezones/identifiers so that we can sort them
            foreach ($timezones as $identifier) {
                $locations[$identifier] = timezone_offset($identifier);
            }

            uksort($locations, function ($a, $b) {
                $aOffset = (new \DateTime(timezone: new \DateTimeZone($a)))->getOffset();
                $bOffset = (new \DateTime(timezone: new \DateTimeZone($b)))->getOffset();

                return ($aOffset < $bOffset) ? -1 : 1;
            });

            $options = ['' => ''];
            foreach ($locations as $identifier => $offset) {
                $options[$identifier] = full_timezone($identifier);
            }

            return $options;
        });
    }
}

if (! function_exists('product_brand')) {
    /**
     * Returns the product brand
     *
     * @return string
     */
    function product_brand($product)
    {
        return config('products.available.'.$product.'.brand');
    }
}

if (! function_exists('validate_slug')) {
    /**
     * Validates slug values in requests
     *
     * @param  string  $slug
     * @return bool
     */
    function validate_slug($slug, bool $allowNumbers = false)
    {
        $regex = $allowNumbers ? '/^([A-Za-z0-9]{8})$/' : '/^([A-Za-z]{8})$/';

        return (bool) preg_match_all($regex, (string) $slug, $matches) > 0;
    }
}

if (! function_exists('validate_token')) {
    /**
     * Validates token values in requests
     *
     * @param  string  $slug
     * @return bool
     */
    function validate_token($token)
    {
        return (bool) preg_match_all('/^([A-Za-z0-9]{16}|[A-Za-z0-9]{32})$/', $token ?? '', $matches) > 0;
    }
}

if (! function_exists('identifier_type')) {
    /**
     * Determines if the identifier is a slug or token
     *
     * @param  string  $identifier
     * @return bool
     *
     * @throws Exception
     */
    function identifier_type($identifier, bool $allowNumbersInSlugs = false)
    {
        if (validate_slug($identifier, $allowNumbersInSlugs)) {
            return 'slug';
        }

        if (validate_token($identifier)) {
            return 'token';
        }

        throw new Exception('Invalid identifier ['.$identifier.']');
    }
}

if (! function_exists('lowercase_and_hash')) {
    /**
     * Converts to lowercase and hashes a string email field
     *
     * @return string
     */
    function lowercase_and_hash($email)
    {
        return hash('sha256', strtolower($email));
    }
}

if (! function_exists('cluster_region_to_s3_region')) {
    /**
     * Converts cluster region code to its equivalent s3 region name
     *
     * @return string
     */
    function cluster_region_to_s3_region($region)
    {
        switch ($region) {
            case 'au':
                return 'sydney';
            case 'eu':
                return 'ireland';
            case 'us':
                return 'california';
            default:
                return $region;
        }
    }
}

if (! function_exists('array_map_recursive')) {
    /**
     * Apply callback recursively to each element of array, preserving full key notation.
     *
     * @return array
     */
    function array_map_recursive(array|Collection $array, $callback, ?string $keyPrefix = null)
    {
        $result = [];
        foreach ($array as $key => $value) {
            $fullKeyNotation = ($keyPrefix ? $keyPrefix.'.' : '').$key;
            if (is_array($value)) {
                $result[$key] = array_map_recursive($value, $callback, $fullKeyNotation);
            } else {
                $result[$key] = $callback($value, $fullKeyNotation);
            }
        }

        return $result;
    }
}

if (! function_exists('is_json')) {
    function is_json($value): bool
    {
        return is_string($value) && is_array(json_decode($value, true));
    }
}

if (! function_exists('link_to_route')) {
    function link_to_route(string $route, ?string $name = null, array $parameters = [], array $attributes = []): BaseElement|A
    {
        return html()->linkRoute($route, $name, $parameters)->attributes($attributes);
    }
}

if (! function_exists('link_to')) {
    function link_to(?string $url = null, ?string $name = null, $attributes = []): BaseElement|A
    {
        return html()->link($url, $name)->attributes($attributes);
    }
}
