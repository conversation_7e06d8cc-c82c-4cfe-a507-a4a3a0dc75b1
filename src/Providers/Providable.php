<?php

namespace Platform\Providers;

use Illuminate\Foundation\AliasLoader;

trait Providable
{
    /**
     * If there are any listeners defined on the service provider, here we'll loop through
     * them and register them as subscribers with Laravel's events system.
     *
     * The format of the array on the service provider should be:
     *
     *     'some.event' => SomeEventListener::class
     */
    protected function registerListeners(): void
    {
        if (! isset($this->listeners)) {
            return;
        }

        foreach ($this->listeners as $event => $listeners) {
            foreach ((array) $listeners as $listener) {
                $this->app['events']->listen($event, $listener);
            }
        }
    }

    /**
     * Register aliases. Format of aliases array on the service provider should be:
     *
     *     'SomeClass' => 'Path\To\SomeClass'
     *
     * @returns void
     */
    protected function registerAliases(): void
    {
        if (! isset($this->aliases)) {
            return;
        }

        foreach ($this->aliases as $alias => $abstract) {
            AliasLoader::getInstance()->alias($alias, $abstract);
        }
    }

    /**
     * Registers the defined repository interfaces and binds them to an implementation.
     *
     * Format of the array should be:
     *
     *     'Repository' => 'RepositoryImplementation'
     */
    protected function registerRepositories(): void
    {
        if (! isset($this->repositories)) {
            return;
        }

        foreach ($this->repositories as $interface => $repository) {
            $this->app->scoped($interface, $repository);
        }
    }

    /**
     * Register all files for the module.
     */
    protected function bootFiles(): void
    {
        foreach ($this->files as $file) {
            require $file;
        }
    }
}
