<?php

namespace Platform\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Str;
use ReflectionClass;

class ModuleServiceProvider extends RouteServiceProvider
{
    use Providable;

    const ROUTE_PATH = '/Http/Routes';

    const MIGRATIONS_PATH = '/Database/Migrations';

    const EVENT_SUBSCRIBERS_PATH = '/Events/Subscribers';

    protected array $listeners = [];

    protected array $repositories = [];

    protected array $files = [];

    private string $moduleDirectory;

    private string $moduleNamespace;

    public function register(): void
    {
        $this->registerRepositories();
        $this->registerAliases();
        $this->registerListeners();
        parent::register();
    }

    public function boot(): void
    {
        $this->loadMigrationsFrom($this->moduleDirectory().self::MIGRATIONS_PATH);
        $this->registerRoutes();
        $this->bootFiles();
        $this->bootEventSubscribers();
    }

    /**
     * Get the files for the module path if they exist.
     */
    protected function getFiles(string $path): array
    {
        if (! is_dir($this->moduleDirectory().'/'.$path)) {
            return [];
        }

        return array_filter(
            glob($this->moduleDirectory().'/'.$path.'/*.php'),
            fn($file) => ! Str::endsWith($file, ['Test.php', 'test.php'])
        );
    }

    /**
     * Uses reflection to look up the module's directory, which helps to automate the loading of database migrations,
     * routes, and other module-specific files.
     */
    protected function moduleDirectory(): string
    {
        return $this->moduleDirectory ??= dirname((new ReflectionClass($this))->getFileName());
    }

    /**
     * Register any routes discovered in the module.
     * This method will look for a method in the service provider that matches the pattern 'register{ucfirst(route file name)}Route'
     * and call that method if it exists. If it does not exist, it will load the routes from the file.
     */
    protected function registerRoutes(): void
    {
        if (! ($routeFiles = $this->getFiles(self::ROUTE_PATH))) {
            return;
        }

        $this->routes(function () use ($routeFiles) {
            foreach ($routeFiles as $routeFile) {
                $file = pathinfo($routeFile, PATHINFO_FILENAME);
                $method = 'register'.ucfirst(rtrim($file, '.php')).'Routes';
                if (method_exists($this, $method)) {
                    $this->$method($routeFile);

                    continue;
                }

                $this->loadRoutesFrom($routeFile);
            }
        });
    }

    protected function bootEventSubscribers(): void
    {
        if (! ($subscriberFiles = $this->getFiles(self::EVENT_SUBSCRIBERS_PATH))) {
            return;
        }

        foreach ($subscriberFiles as $subscriberFile) {
            Event::subscribe($this->namespaced(self::EVENT_SUBSCRIBERS_PATH, $subscriberFile));
        }
    }

    protected function moduleNamespace(): string
    {
        return $this->moduleNamespace ??= (new ReflectionClass($this))->getNamespaceName();
    }

    /**
     * Generate a fully qualified namespace for a given path and optionally a file.
     */
    protected function namespaced(string $path, string $file = ''): string
    {
        $file = $file ? '\\'.basename($file, '.php') : '';

        $path = Str::startsWith($path, '/') ? $path : '/'.$path;

        return $this->moduleNamespace().str_replace('/', '\\', $path).$file;
    }
}
