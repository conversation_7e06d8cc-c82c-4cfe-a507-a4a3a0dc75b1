<?php

namespace Platform\Menu;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

class Menu extends MenuItem implements Arrayable, Jsonable
{
    /**
     * Stores the name for the menu. This used as a reference point for menu items (where
     * necessary) as well as when rendering specific required menus.
     *
     * @var string
     */
    public $name;

    /**
     * Construct a new menu with the following text.
     *
     * @param  string  $name
     * @param  string  $text
     * @param  null  $icon
     */
    public function __construct($name, $text = null, $icon = null)
    {
        $this->name = strtolower($name);
        $this->text = strip_tags($text ?? '');
        $this->icon = $icon;
    }

    /**
     * Add a new child menu item, and set its parent.
     *
     * @param  Item  $item
     */
    public function addChild(MenuItem $item)
    {
        $this->children[] = $item;

        // Menus are only renderable if they have children
        $this->renderable = true;

        $item->setParent($this);
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return [
            'name' => $this->name,
            'text' => $this->text,
            'icon' => $this->icon,
            'children' => $this->children ?
                array_map(function ($child) {
                    return $child->toArray();
                }, $this->children()) : [],
        ];
    }

    /**
     * Convert the object to its JSON representation.
     *
     * @param  int  $options
     * @return string
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray());
    }
}
