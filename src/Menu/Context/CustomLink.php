<?php

namespace Platform\Menu\Context;

class CustomLink extends Link
{
    public function __construct(
        private string $name,
        private string $text,
        private string $link,
        private string $target,
        private string $icon,
        private bool $applies,
        private array $contexts,
        private array $extras,
        private int $position
    ) {
    }

    public function name(): string
    {
        return $this->name;
    }

    public function text(): string
    {
        return $this->text;
    }

    public function link(): string
    {
        return $this->link;
    }

    public function target(): string
    {
        return $this->target;
    }

    public function icon(): string
    {
        return $this->icon;
    }

    public function applies(): bool
    {
        return $this->applies;
    }

    public function contexts(): array
    {
        return $this->contexts;
    }

    public function extras(): array
    {
        return $this->extras;
    }

    public function position(): int
    {
        return $this->position;
    }
}
