<?php

namespace Platform\Menu\Context;

use Illuminate\Contracts\Support\Arrayable;

abstract class Link extends MenuItem implements Arrayable
{
    /**
     * Route of the link.
     */
    abstract public function link(): string;

    /**
     *  HTML target attribute specifies where to open the linked document.
     */
    abstract public function target(): ?string;

    /**
     * Conditions if the link will be included in the menu.
     */
    abstract public function applies(): bool;

    /**
     * List of contexts menu where the link should only belong and accessible.
     */
    abstract public function contexts(): array;

    public function type(): string
    {
        return MenuItem::TYPE_LINK;
    }

    public function toArray()
    {
        return [
            'name' => strtolower($this->name()),
            'text' => $this->text(),
            'link' => $this->link(),
            'target' => $this->target(),
            'icon' => $this->icon(),
            'contexts' => collect($this->contexts())
                ->map(fn($i) => $i->name())
                ->values()
                ->toArray(),
            'type' => $this->type(),
            'extras' => $this->extras(),
        ];
    }
}
