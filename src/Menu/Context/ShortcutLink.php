<?php

namespace Platform\Menu\Context;

use Illuminate\Contracts\Support\Arrayable;

/**
 * A shortcut link used to open other menu
 */
abstract class ShortcutLink extends Link implements Arrayable
{
    public function type(): string
    {
        return MenuItem::TYPE_SHORTCUT_LINK;
    }

    public function link(): string
    {
        return '';
    }

    public function toArray()
    {
        return array_merge(parent::toArray(), ['shortcut_menu_name' => $this->shortcutMenuName()]);
    }

    /**
     * The name of the menu to open from this shortcut link
     */
    abstract public function shortcutMenuName(): string;
}
