<?php

namespace Platform\Menu\Context;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

abstract class Context implements Arrayable, Jsonable
{
    private string $route = '';

    private bool $selected = false;

    /**
     * Unique name of the context item
     */
    abstract public static function name(): string;

    abstract public function text(): string;

    /**
     * List of FeatureRoles associated of this context
     */
    abstract public function featureRoles(): array;

    /**
     * Unique route alias for each context
     */
    abstract public function routeAlias(): string;

    /**
     * Default route to open
     */
    public function route(): string
    {
        return $this->route;
    }

    public function setRoute(string $route)
    {
        $this->route = $route;
    }

    public function selected()
    {
        return $this->selected;
    }

    public function setSelected(bool $selected)
    {
        $this->selected = $selected;
    }

    public function __toString()
    {
        return $this->name();
    }

    public function toArray()
    {
        return [
            'name' => strtolower($this->name()),
            'text' => strip_tags($this->text()),
            'route' => $this->route(),
            'selected' => $this->selected(),
        ];
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray());
    }
}
