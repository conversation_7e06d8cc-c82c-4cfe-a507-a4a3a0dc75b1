<?php

namespace Platform\Menu\Context\Items;

use Platform\Authorisation\FeatureRoles\ChapterManager;
use Platform\Authorisation\FeatureRoles\ProgramManager;
use Platform\Menu\Context\Context;

class Manage extends Context
{
    public static function name(): string
    {
        return 'manage';
    }

    public function text(): string
    {
        return trans('menu.context.manage');
    }

    public function featureRoles(): array
    {
        return [new ProgramManager, new ChapterManager];
    }

    public function routeAlias(): string
    {
        return 'manager';
    }
}
