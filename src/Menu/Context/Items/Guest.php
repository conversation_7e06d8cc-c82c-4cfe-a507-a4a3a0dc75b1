<?php

namespace Platform\Menu\Context\Items;

use Platform\Menu\Context\Context;

class Guest extends Context
{
    public static function name(): string
    {
        return 'guest';
    }

    public function text(): string
    {
        return trans('menu.context.guest');
    }

    public function featureRoles(): array
    {
        return [];
    }

    public function routeAlias(): string
    {
        return 'home';
    }
}
