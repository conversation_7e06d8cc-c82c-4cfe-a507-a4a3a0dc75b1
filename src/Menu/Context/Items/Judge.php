<?php

namespace Platform\Menu\Context\Items;

use Platform\Authorisation\FeatureRoles\Judge as JudgeFeatureRole;
use Platform\Menu\Context\Context;

class Judge extends Context
{
    public static function name(): string
    {
        return 'judge';
    }

    public function text(): string
    {
        return trans('menu.context.judge');
    }

    public function featureRoles(): array
    {
        return [new JudgeFeatureRole];
    }

    public function routeAlias(): string
    {
        return 'judge';
    }
}
