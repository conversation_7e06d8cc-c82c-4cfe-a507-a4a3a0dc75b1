<?php

namespace Platform\Menu\Context\Items;

use Platform\Authorisation\FeatureRoles\Entrant;
use Platform\Menu\Context\Context;

class Enter extends Context
{
    public static function name(): string
    {
        return 'enter';
    }

    public function text(): string
    {
        return trans('menu.context.enter');
    }

    public function featureRoles(): array
    {
        return [new Entrant];
    }

    public function routeAlias(): string
    {
        return 'entrant';
    }
}
