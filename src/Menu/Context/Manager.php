<?php

namespace Platform\Menu\Context;

class Manager
{
    /**
     * Hold the available contexts menu and their links.
     */
    private array $contextLinks;

    private Menu $generatedMainMenu;

    /**
     * Array of context names
     */
    private array $contextNames;

    public function __construct(private Menu $menu, private array $contexts)
    {
        $this->contextLinks = [];
        $this->contextNames = $this->getContextNames($this->contexts);
    }

    /**
     * Generate the menu based on the given context menu and link visibility
     */
    public function generate(): Menu
    {
        return $this->generatedMainMenu = $this->generateMenu($this->menu);
    }

    /**
     * Generate the menu recursively.
     */
    public function generateMenu(Menu $menu): Menu
    {
        foreach ($menu->menuItems() as $menuItem) {
            if ($menuItem instanceof Link && $this->applies($menuItem)) {
                $menu->addMenuItemsToGenerate($menuItem);
                $this->storeLinkByContexts($menuItem);
            }
            if ($menuItem instanceof Menu) {
                $menu->addMenuItemsToGenerate($this->generateMenu($menuItem));
            }
        }

        return $menu;
    }

    /**
     * Add menu item to the generated menu
     */
    public function addMenuItem(MenuItem $menuItem): void
    {
        if ($menuItem instanceof Link && $this->applies($menuItem)) {
            $this->generatedMainMenu->addMenuItemsToGenerate($menuItem);
            $this->storeLinkByContexts($menuItem);
        }

        if ($menuItem instanceof Menu) {
            $this->generatedMainMenu->addMenuItemsToGenerate($this->generateMenu($menuItem));
        }
    }

    /**
     * Get the generated menu
     */
    public function generatedMainMenu(): Menu
    {
        return $this->generatedMainMenu;
    }

    /**
     * Get the list of links segregated by context names
     */
    public function contextLinks(): array
    {
        return $this->contextLinks;
    }

    /**
     * Get the context menu items that has links.
     *
     * @return array
     */
    public function getRenderableContexts(?callable $mapper = null)
    {
        return array_map(
            fn($context) => ($mapper) ? $mapper($context, $this->contextLinks) : $context,
            array_values(array_filter(
                $this->contexts,
                fn($context) => isset($this->contextLinks[$context->name()])
            ))
        );
    }

    /**
     * Store links to respective context menu item
     */
    private function storeLinkByContexts(Link $link): void
    {
        foreach ($this->getContextNames($link->contexts()) as $context) {
            $this->contextLinks[$context][] = $link;
        }
    }

    /**
     * Check the link visibility and if there's a matched context menu item.
     */
    private function applies(Link $link): bool
    {
        return $link->applies() && count(array_intersect($this->getContextNames($link->contexts()), $this->contextNames)) > 0;
    }

    /**
     * Convert context objects to array of their unique names
     */
    private function getContextNames(array $contexts): array
    {
        return array_map(fn($context) => $context->name(), $contexts);
    }
}
