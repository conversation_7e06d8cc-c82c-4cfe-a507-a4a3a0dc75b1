<?php

namespace Platform\Menu\Context;

use Illuminate\Support\Collection;
use Platform\Menu\Context\Items\Enter;
use Platform\Menu\Context\Items\Guest;
use Platform\Menu\Context\Items\Judge;
use Platform\Menu\Context\Items\Manage;

class ContextMenuService
{
    /**
     * Available context menu items.
     *
     * @return array
     */
    public function contexts()
    {
        return [new Man<PERSON>, new En<PERSON>, new Judge, new Guest];
    }

    /**
     * Determine the list of contexts items from the given set of roles
     */
    public function getContextMenuItems(Collection $roles)
    {
        return $roles->reduce(function ($contexts, $role) {
            $contexts[] = match (true) {
                $role->guest => new Guest,
                $role->chapterLimited => new Manage,
                default => $this->getContextItemsByPermissions($role->permissions),
            };

            return array_unique(array_flatten($contexts));
        }, []);
    }

    /**
     * Determine what context menu a set of permissions belong.
     */
    public function getContextItemsByPermissions(Collection $permissions): array
    {
        $contextMenuItems = [];

        foreach ($this->contexts() as $context) {
            foreach ($context->featureRoles() as $featureRole) {
                if ($featureRole::appliesTo($permissions) && ! in_array($context->name(), $contextMenuItems)) {
                    $contextMenuItems[] = $context;
                }

                if (in_array(Manage::name(), $contextMenuItems)) {
                    break 2;
                }
            }
        }

        return $contextMenuItems;
    }

    /**
     * Return all the context names in array
     */
    public function contextNames(): array
    {
        return array_map(fn($context) => $context::name(), $this->contexts());
    }

    /**
     * Get context by name
     *
     * @return array
     */
    public function getContextByName($name)
    {
        $context = array_filter($this->contexts(), fn($context) => $context::name() === $name);

        return reset($context);
    }

    /**
     * Get context from the list of names
     *
     * @param  $name
     * @return array
     */
    public function getContextByNames(array $names)
    {
        return array_values(array_filter($this->contexts(), fn($context) => in_array($context::name(), $names)));
    }

    /**
     * Get the context route alias name
     */
    public function getContextRouteAliasByName($name): string
    {
        if ($context = $this->getContextByName($name)) {
            return $context->routeAlias();
        }

        return '';
    }

    /**
     * Return all context route aliases
     */
    public function routeAliases(): array
    {
        return array_map(fn($context) => $context->routeAlias(), $this->contexts());
    }

    /**
     * Sorting algo for menu items
     *
     * @return array
     */
    public function sort(array $menuItems)
    {
        if (! empty($menuItems)) {
            usort($menuItems, function ($a, $b) {
                if ($a->position() == 0) {
                    return 0;
                }

                return $a->position() <=> $b->position();
            });
        }

        return $menuItems;
    }
}
