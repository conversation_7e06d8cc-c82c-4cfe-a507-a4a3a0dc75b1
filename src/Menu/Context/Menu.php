<?php

namespace Platform\Menu\Context;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

abstract class Menu extends MenuItem implements Arrayable, Jsonable
{
    /*
     * List of menu items that will be generated for this menu based on their visibility conditions.
     */
    private array $menuItemsToGenerate;

    public function __construct()
    {
        $this->menuItemsToGenerate = [];
    }

    abstract public function menuItems(): array;

    /**
     * Add menu items that will be generated.
     *
     *
     * @return void
     */
    public function addMenuItemsToGenerate(MenuItem $menuItem)
    {
        if ($menuItem instanceof Menu && empty($menuItem->menuItemsToGenerate())) {
            return;
        }
        $this->menuItemsToGenerate[] = $menuItem;
    }

    /**
     * Get the menu items that will be generated.
     */
    public function menuItemsToGenerate(): array
    {
        return $this->menuItemsToGenerate;
    }

    public function type(): string
    {
        return MenuItem::TYPE_MENU;
    }

    public function toArray()
    {
        return [
            'name' => strtolower($this->name()),
            'text' => strip_tags($this->text()),
            'icon' => $this->icon(),
            'type' => $this->type(),
            'extras' => $this->extras(),
            'children' => ! empty($this->menuItemsToGenerate) ?
                array_map(function ($item) {
                    return $item->toArray();
                }, app(ContextMenuService::class)->sort($this->menuItemsToGenerate)) : [],
        ];
    }

    public function toJson($options = 0)
    {
        return json_encode($this->toArray());
    }
}
