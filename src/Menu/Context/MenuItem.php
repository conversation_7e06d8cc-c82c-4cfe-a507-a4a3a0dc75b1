<?php

namespace Platform\Menu\Context;

abstract class MenuItem
{
    const TYPE_MENU = 'menu';

    const TYPE_LINK = 'link';

    const TYPE_SHORTCUT_LINK = 'shortcut_link';

    /**
     * Name of the menu item. This should be unique
     */
    abstract public function name(): string;

    /**
     * Text or label of the menu item for display.
     */
    abstract public function text(): string;

    /**
     * af-icon for the menu item.
     */
    abstract public function icon(): string;

    /**
     * Type of menu item. I.e. Menu or a Link.
     */
    abstract public function type(): string;

    /**
     * Extra parameters that can be use in the frontend menu.
     */
    abstract public function extras(): array;

    /**
     * Use to sort the order position within siblings
     */
    abstract public function position(): int;
}
