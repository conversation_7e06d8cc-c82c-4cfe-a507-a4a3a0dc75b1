<?php

namespace Platform\Menu;

use Illuminate\Contracts\Support\Arrayable;

class Item extends MenuItem implements Arrayable
{
    /**
     * The link that when a user clicks, will be directed to.
     *
     */
    public string $link;

    public ?string $target;

    /**
     * Construct a new item to be appended to a menu.
     *
     * @param  string  $name  will be used as id attribute
     * @param  string  $text
     * @param  string  $link
     * @param  null|string  $icon
     * @param  null|string  $target
     */
    public function __construct(string $name, $text, $link, $icon = null, $target = null, array $attributes = [])
    {
        $this->name = strip_tags(strtolower($name));
        $this->text = strip_tags($text);
        $this->link = $link;
        $this->attributes = $attributes;

        $this->renderable = true;
        $this->icon = $icon;
        $this->target = $target;
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray()
    {
        return [
            'text' => $this->text,
            'name' => $this->name,
            'link' => $this->link,
            'icon' => $this->icon(),
            'target' => $this->target,
            'attributes' => $this->attributes,
        ];
    }
}
