<?php

namespace Platform\Menu;

abstract class MenuItem
{
    /**
     * The name item that will be used as id attribute.
     *
     * @var string|null It is possible for top-level menus to not need any text.
     */
    public $name;

    /**
     * The text of the menu item.
     *
     * @var string|null It is possible for top-level menus to not need any text.
     */
    public $text;

    /**
     * Stores the parent Menu.
     *
     * @var Menu
     */
    protected $parent;

    /**
     * Defines whether or not the menu item is renderable.
     *
     * @var bool
     */
    protected $renderable = false;

    /**
     * Stores the children this menu item has.
     *
     * @var array
     */
    protected $children = [];

    /**
     * Determines whether the menu or item is active.
     *
     * @var bool
     */
    private $active = false;

    /**
     * The icon class to be used for the menu item. If an icon is specified,
     * then the appropriate icon will be displayed to the left o the text
     * on the menu.
     *
     * @var null|string
     */
    protected $icon;

    /** @var array */
    protected $attributes = [];

    /**
     * Determines whether or not the menu item has a parent.
     *
     * @return bool
     */
    public function hasParent()
    {
        return (bool) $this->parent;
    }

    /**
     * Determines whether or not the item has children.
     *
     * @return bool
     */
    public function hasChildren()
    {
        return (bool) count($this->children);
    }

    /**
     * Returns the array of children this item has.
     *
     * @return array
     */
    public function children()
    {
        return $this->children;
    }

    /**
     * Inverse check of hasChildren. Helper method.
     *
     * @return bool
     */
    public function isParent()
    {
        return $this->hasChildren();
    }

    /**
     * @return bool
     */
    public function isRenderable()
    {
        return $this->renderable;
    }

    /**
     * Set the parent of the menu item.
     */
    public function setParent(Menu $parent)
    {
        $this->parent = $parent;
    }

    /**
     * Return the icon string.
     *
     * @return null|string
     */
    public function icon()
    {
        return $this->icon;
    }

    /**
     * Returns the active state of the item.
     *
     * @return bool
     */
    public function isActive()
    {
        return $this->active;
    }

    /**
     * Sets the active property on the item. This is useful for highlighting/activating menu items visually.
     */
    public function setActive()
    {
        $this->active = true;

        if ($this->hasParent()) {
            $this->parent->setActive();
        }
    }

    public function attributes(array $merge = []): string
    {
        $attributes = array_merge($this->attributes, $merge);

        return collect($attributes)->map(function ($value, $key) {
            return "{$key}=\"{$value}\"";
        })->implode(' ');
    }
}
