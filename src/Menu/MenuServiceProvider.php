<?php

namespace Platform\Menu;

use Illuminate\Support\ServiceProvider;

/**
 * @codeCoverageIgnore
 */
class MenuServiceProvider extends ServiceProvider
{
    protected $aliases = [
        'Menufy' => Menufy::class,
    ];

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('menufy', Manager::class);
    }

    public function provides()
    {
        return ['menufy', 'Menufy'];
    }
}
