<?php

namespace Platform\Support;

class StrongPasswordGenerator
{
    public static function generate(int $length = 12): string
    {
        if ($length < 12) {
            $length = 12;
        }

        $digits = array_flip(range('0', '9'));
        $lowercase = array_flip(range('a', 'z'));
        $uppercase = array_flip(range('A', 'Z'));
        $special = array_flip(str_split('!@#$%^&*()_+=-}{[}]\|;:<>?/'));
        $combined = array_merge($digits, $lowercase, $uppercase, $special);
        $remainingLength = $length - 4;

        return str_shuffle(array_rand($digits).
            array_rand($lowercase).
            array_rand($uppercase).
            array_rand($special).
            implode(array_rand($combined, $remainingLength)));
    }
}
