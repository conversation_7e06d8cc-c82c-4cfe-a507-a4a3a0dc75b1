<?php

namespace Platform\Support;

use Illuminate\Contracts\Cookie\QueueingFactory as <PERSON><PERSON>;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class UserTracker
{
    const USER_IP = 'HTTP_X_FORWARDED_FOR';

    const USER_IP_LOCAL = 'REMOTE_ADDR';

    const COOKIE = 'af4-10283439';            // random string, we don't want to advertise it's for voting.

    private $request;

    /**
     * @var Cookie
     */
    private $cookie;

    public function __construct(Request $request, Cookie $cookie)
    {
        $this->request = $request;
        $this->cookie = $cookie;
    }

    /**
     * Returns the users IP address.
     *
     * @return string
     */
    public function userIP()
    {
        return $this->request->server(self::USER_IP) ?: $this->request->server(self::USER_IP_LOCAL);
    }

    /**
     * Returns the current requesting browser fingerprint.
     *
     * This is a very rough indicator of the uniqueness of the browser making the request,
     * based off the information the browser advertises to the server.
     *
     * It currently uses:
     *  - HTTP_ACCEPT
     *  - HTTP_ACCEPT_ENCODING
     *  - HTTP_ACCEPT_LANGUAGE
     *  - HTTP_USER_AGENT
     *
     * @return string
     */
    public function browserFingerprint()
    {
        return json_encode([
            'accept' => $this->request->server('HTTP_ACCEPT'),
            'encoding' => $this->request->server('HTTP_ACCEPT_ENCODING'),
            'lang' => $this->request->server('HTTP_ACCEPT_LANGUAGE'),
            'useragent' => $this->request->server('HTTP_USER_AGENT'),
        ]);
    }

    /**
     * Returns the user tracker cookie value for the current user.
     * If no cookie is found, it will push a new cookie to the user and return the cookie value.
     *
     * @return string
     */
    public function cookie()
    {
        $cookie = $this->request->cookie(self::COOKIE);

        if ($cookie) {
            return $cookie;
        }

        $cookie = Str::random(32);

        $this->cookie->queue(self::COOKIE, $cookie, 2628000);

        return $cookie;
    }
}
