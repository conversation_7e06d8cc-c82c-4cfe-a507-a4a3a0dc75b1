<?php

namespace Platform\Support\Values;

use Platform\Features\Plan;

class Product
{
    /**
     * @var string
     */
    private $product;

    /**
     * @var array
     */
    private $parts;

    public function __construct(string $product)
    {
        $this->product = $product;
        $this->parts = explode('-', $this->product);
    }

    public function currency()
    {
        return $this->parts[0];
    }

    /**
     * Returns the plan associated with the product.
     */
    public function plan(): Plan
    {
        return new Plan($this->parts[2]);
    }

    /**
     * Returns the product version number.
     */
    public function version(): int
    {
        return $this->parts[3];
    }

    /**
     * Return the billing period (annual or monthly).
     */
    public function period(): string
    {
        return $this->parts[1];
    }

    /**
     * Returns the full product string.
     */
    public function __toString(): string
    {
        return $this->product;
    }
}
