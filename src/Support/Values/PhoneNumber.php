<?php

namespace Platform\Support\Values;

use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use Propaganistas\LaravelPhone\PhoneNumber as LaravelPhoneNumber;

class PhoneNumber
{
    use Checkable;

    /**
     * @var string
     */
    private $number;

    /**
     * Instantiate a new phone number object. Number provided must follow international phone number standards.
     *
     * @param  string  $number
     */
    public function __construct($number)
    {
        if (! preg_match('/^\+[0-9]+$/', $number)) {
            throw new InvalidArgumentException('The phone number provided was invalid. Please follow international phone number formats.');
        }
        $this->number = self::removeLeadingZeroes($number);
    }

    /**
     * Convert the phone number back to string format.
     *
     * @return string
     */
    public function __toString()
    {
        return $this->number;
    }

    /**
     * Returns a phone number in E.164 format.
     */
    public static function removeLeadingZeroes(?string $number): ?string
    {
        if (! $number) {
            return $number;
        }

        try {
            return (new LaravelPhoneNumber($number, 'INTERNATIONAL'))->formatE164();
        } catch (\Throwable $e) {
            Log::info('Skip problematic number: '.$number);

            return $number;
        }
    }
}
