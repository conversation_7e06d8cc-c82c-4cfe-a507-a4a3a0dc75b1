<?php

namespace Platform\Support\Values;

use Assert\InvalidArgumentException as InvalidAssertion;
use InvalidArgumentException;

trait Checkable
{
    /**
     * Test a string to see if it's a suitable match as an Email.
     *
     * @param  string  $string
     * @return bool
     */
    public static function check($string)
    {
        try {
            new static($string);

            return true;
        } catch (InvalidAssertion $e) {
        } catch (InvalidArgumentException $e) {
        }

        return false;
    }
}
