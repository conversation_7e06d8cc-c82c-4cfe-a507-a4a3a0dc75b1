<?php

namespace Platform\Events;

class Dispatcher extends \Illuminate\Events\Dispatcher
{
    /**
     * Simple method to return all listeners registered in the system.
     *
     * @return array
     */
    public function getAllListeners()
    {
        return $this->listeners;
    }

    /**
     * Same as dispatch. Kept for backwards compatibility.
     *
     * @param  array  $payload
     * @param  bool  $halt
     * @return array|null
     */
    public function fire($event, $payload = [], $halt = false)
    {
        return $this->dispatch($event, $payload, $halt);
    }
}
