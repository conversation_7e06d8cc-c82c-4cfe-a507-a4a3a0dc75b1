<?php

namespace Platform\Events;

use Platform\Authorisation\Consumer;
use Platform\Events\Facades\Broadcast;

/**
 * @codeCoverageIgnore
 */
trait UserBroadcaster
{
    /**
     * Get the channels the event should broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [
            Broadcast::consumerChannel($this->consumer()) ?: 'dev-null',
        ];
    }

    /**
     * @return string
     */
    public function broadcastAs()
    {
        return Broadcast::simpleMessage();
    }

    /**
     * Returns the consumer to be used for the broadcast.
     *
     * @return Consumer
     */
    abstract protected function consumer();
}
