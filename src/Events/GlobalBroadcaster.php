<?php

namespace Platform\Events;

use Platform\Events\Facades\Broadcast;

/**
 * @codeCoverageIgnore
 */
trait GlobalBroadcaster
{
    /**
     * Get the channels the event should broadcast on.
     *
     * @return array
     */
    public function broadcastOn()
    {
        return [
            Broadcast::globalChannel($this->globalId()),
        ];
    }

    /**
     * @return string
     */
    public function broadcastAs()
    {
        return Broadcast::simpleMessage();
    }

    /**
     * Returns the globalId to be used for the broadcast.
     *
     * @return string
     */
    abstract protected function globalId();
}
