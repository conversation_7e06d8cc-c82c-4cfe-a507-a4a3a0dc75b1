<?php

namespace Platform\Events;

use Platform\Authorisation\Consumer;

class BroadcastService
{
    /**
     * @return string
     */
    public function consumerChannel(Consumer $consumer)
    {
        if (empty($consumer->globalId()) || empty($consumer->globalAccountId())) {
            return null;
        }

        return "private-consumer-{$consumer->globalId()}-{$consumer->globalAccountId()}";
    }

    /**
     * @return string
     */
    public function globalChannel($globalId)
    {
        return "private-global-{$globalId}";
    }

    /**
     * @return string
     */
    public function simpleMessage()
    {
        return 'message.simple';
    }
}
