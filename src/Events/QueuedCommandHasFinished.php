<?php

namespace Platform\Events;

use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;
use Platform\Authorisation\Consumer;
use Platform\Bus\Pipeline\Consumable;
use Platform\Bus\QueuedJob;
use Platform\Bus\Status\Messenger;
use Tectonic\Localisation\Contracts\Translatable;

/**
 * @codeCoverageIgnore
 */
class QueuedCommandHasFinished implements ShouldBroadcastNow
{
    use UserBroadcaster;

    /**
     * @var mixed
     */
    public $resource;

    /**
     * @var Consumable
     */
    public $command;

    /**
     * @var Consumer
     */
    public $consumer;

    /**
     * @param  mixed  $resource
     */
    public function __construct(QueuedJob $command, $resource, Consumer $consumer)
    {
        $this->resource = $resource;
        $this->command = $command;
        $this->consumer = $consumer;
    }

    /**
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'message' => $this->message(),
            'type' => 'info',
            'refreshIfExists' => $this->resource->getBroadcastRefreshToken(),
        ];
    }

    /**
     * Returns the consumer to be used for the broadcast.
     *
     * @return Consumer
     */
    protected function consumer()
    {
        return $this->consumer;
    }

    private function message(): string
    {
        if ($this->resource instanceof Messenger && $message = $this->resource->finishMessage()) {
            return $message;
        }

        if ($this->resource instanceof Translatable) {
            $this->resource = translate($this->resource);
        }

        $resourceKey = $this->resource->getBroadcastKey();
        $name = method_exists($this->resource, 'getBroadcastName') ? $this->resource->getBroadcastName() : '';
        $languageCode = $this->consumer->language()->code;

        return trans('broadcast.message.finished.'.$resourceKey, ['name' => $name], $languageCode);
    }
}
