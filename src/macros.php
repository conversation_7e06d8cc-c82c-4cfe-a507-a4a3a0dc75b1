<?php

/**
 * Finds the first record that has a matching slug.
 *
 * @param  \Eloquence\Behaviours\Slug  $slug
 * @param  string  $slug
 * @return mixed
 */
\Illuminate\Support\Collection::macro('firstWithSlug', function ($slug) {
    return $this->first(function ($record) use ($slug) {
        return (string) $record->slug == (string) $slug;
    });
});

/**
 * When all you need is the key-value pair. This is the reimplementation of < 5.1
 * pluck() method signature, which was both extremely useful and terse in its usage.
 *
 * @param  string  $value
 * @param  string  $key
 * @return array
 */
\Illuminate\Support\Collection::macro('just', function ($value, $key = null) {
    return $this->pluck($value, $key)->toArray();
});

/**
 * Moved the release events method to a specific macro.
 *
 * @return array
 */
\Illuminate\Database\Eloquent\Collection::macro('releaseEvents', function () {
    return array_filter($this->reduce(function (array $events, $model): array {
        return method_exists($model, 'releaseEvents')
                ? array_merge($events, $model->releaseEvents())
                : $events;
    }, []));
});

/**
 * Syntactic sugar for filtering a collection given the value type.
 * Currently supports int, integer, string, and class name.
 *
 * @param  string  $type
 *
 * @retun bool
 */
\Illuminate\Support\Collection::macro('whereType', function (string $type) {
    return $this->filter(function ($value) use ($type) {
        switch ($type) {
            case 'int':
            case 'integer':
                return is_numeric($value) && (int) $value == $value;
            case 'float':
                return is_numeric($value);
            case 'string':
                return is_string($value);
            default:
                return $value instanceof $type;
        }
    });
});
