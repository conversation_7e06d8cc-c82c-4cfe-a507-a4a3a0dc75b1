<?php

namespace Platform\Features;

use Assert\Assertion;

class Plan
{
    private array $features;

    private array $limits;

    public function __construct(private string $plan)
    {
        Assertion::inArray($plan, array_keys(config('features.plans')));

        $this->features = config('features.plans.'.$plan.'.features');
        $this->limits = config('features.plans.'.$plan.'.limits', []);
    }

    /**
     * Returns an array of Feature value objects specific to the plan.
     */
    public function features(): Features
    {
        return Features::fromConfig($this->features, 'enabled');
    }

    /**
     * Returns the limits that a plan has imposed on it.
     */
    public function limits(): array
    {
        return $this->limits;
    }

    /**
     * Converts the object to a string.
     */
    public function __toString(): string
    {
        return $this->plan;
    }
}
