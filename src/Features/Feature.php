<?php

namespace Platform\Features;

class Feature
{
    /**
     * @var string
     */
    protected $feature;

    /**
     * @var string
     */
    protected $status;

    /**
     * Feature constructor.
     */
    public function __construct(string $feature, string $status)
    {
        if (! in_array($status, ['enabled', 'disabled'])) {
            throw new InvalidFeatureStatus($feature, $status);
        }

        if (! in_array($feature, config('features.features'))) {
            throw new InvalidFeature($feature);
        }

        $this->feature = $feature;
        $this->status = $status;
    }

    public static function new(string $feature, string $status)
    {
        try {
            return new self($feature, $status);
        } catch (InvalidFeature $exception) {
            return new NullFeature($feature, $status);
        }
    }

    public function valid(): bool
    {
        return get_class($this) !== NullFeature::class;
    }

    /**
     * @return string
     */
    public function value()
    {
        return $this->feature;
    }

    /**
     * Returns true if the feature is enabled.
     */
    public function enabled(): bool
    {
        return $this->status == 'enabled';
    }

    /**
     * Returns true if the feature is disabled.
     */
    public function disabled(): bool
    {
        return ! $this->enabled();
    }

    /**
     * Returns the status of the feature.
     */
    public function status(): string
    {
        return $this->status;
    }

    /**
     * @return string
     */
    public function __toString()
    {
        return $this->value();
    }

    /**
     * Will return true if the the provided feature is the same feature as this one, regardless of status.
     *
     * @return bool
     */
    public function is(Feature $that)
    {
        return $this->value() === $that->value();
    }
}
