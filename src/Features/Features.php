<?php

namespace Platform\Features;

use Illuminate\Support\Collection;

class Features extends Collection
{
    /**
     * Merging two collections of Feature value objects is a little tricky, so we're overloading the merge
     * metho so that we can more easily facilitate that requirement, as and when we need it.
     *
     * @return static
     */
    public function merge($items)
    {
        $thoseItems = $this->getArrayableItems($items);
        $mergedItems = $this->mergeFeatures($thoseItems);

        return new static($mergedItems);
    }

    /**
     * Will craft a new features collection based on the configuration array passed.
     *
     * @return static
     */
    public static function fromConfig(array $config, string $status)
    {
        return new static(array_map(function ($item) use ($status) {
            return new Feature($item, $status);
        }, $config));
    }

    /**
     * Finds and merges features as found.
     */
    private function mergeFeatures(array $thoseItems): array
    {
        $items = $this->keyBy(function ($feature) {
            return (string) $feature;
        })->toArray();

        foreach ($thoseItems as $thatItem) {
            $items[(string) $thatItem] = $thatItem;
        }

        return $items;
    }

    public function enabled(): self
    {
        return $this->filter->enabled();
    }

    public function commonEnabledFeatures(self $features): self
    {
        return $this->enabled()
            ->intersect($features->enabled())
            ->values();
    }
}
