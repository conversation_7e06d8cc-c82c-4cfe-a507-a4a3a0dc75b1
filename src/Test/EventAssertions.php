<?php

namespace Platform\Test;

/**
 * @codeCoverageIgnore
 */
trait EventAssertions
{
    /**
     * Asserts that an object raised a specific event.
     * If $callback is non-null also assert its truth-test for every matched event.
     */
    public function assertRaised($object, string $eventClass, ?callable $callback = null)
    {
        $eventObjects = $object->pendingEvents();

        $events = array_map(function ($event) {
            return get_class($event);
        }, $object->pendingEvents());

        $this->assertContains($eventClass, $events);

        if ($callback !== null) {
            $callback = $callback ?: function () {
                return true;
            };

            return collect($eventObjects)->filter(function ($event) use ($eventClass) {
                return get_class($event) === $eventClass;
            })->each(function (...$event) use ($callback) {
                return $this->assertTrue($callback(...$event));
            });
        }
    }

    /**
     * Inverse logic of assertRaised. Asserts that an event was NOT raised on a raiseable object.
     *
     * @param  object  $object
     */
    public function assertNotRaised($object, string $event)
    {
        $events = array_map(function ($event) {
            return get_class($event);
        }, $object->pendingEvents());

        $this->assertNotContains($event, $events);
    }
}
