#!/bin/bash

# Allows us to read user input below, assigns stdin to keyboard
exec < /dev/tty

POSITIONAL_ARGS=()

while [[ $# -gt 0 ]]; do
  case $1 in
    -n|--no-verify)
      exit 1
      ;;
    *)
      POSITIONAL_ARGS+=("$1") # save positional arg
      shift # past argument
      ;;
  esac
done

# Restore positional parameters
set -- "${POSITIONAL_ARGS[@]}"

# Get staged files
ALL_STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACMR)

# Disable if it's a merge
if ! git merge HEAD &> /dev/null; then
  exit $?
fi

# Don't do anything if no staged files
if [[ "$ALL_STAGED_FILES``" = "" ]]; then
  exit 0
fi

# Run Pint
STAGED_PHP_FILES=$(echo "$ALL_STAGED_FILES" | grep -E "\.php$")
if [[ -n "$STAGED_PHP_FILES" ]]; then
  printf "\033[0;32mRunning Pint...\033[0m\n"
  if ! ./vendor/bin/pint --dirty; then
    printf "\033[41mFix Pint errors before commit.\033[0m\n"
    exit 1
  fi
fi

# Re-stage fixed files
echo "$ALL_STAGED_FILES" | xargs -n 50 git add
NEW_STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACMR)
if [[ -z "$NEW_STAGED_FILES" ]]; then
  printf "\033[0;33mNothing to commit.\033[0m\n"
  exit 1
else
  printf "\033[0;32mCommitting fixed files...\033[0m\n"
fi
exit 0
