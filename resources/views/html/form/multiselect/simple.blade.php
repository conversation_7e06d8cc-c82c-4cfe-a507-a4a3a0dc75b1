<multiselect inline-template :selected="@js($multiselect->selected())">
    <div class="multiselect">
        @include('platform::html.form.multiselect.filter')
        @include('platform::html.form.multiselect.counter')

        <div class="multiselect-toggles">
            @include('platform::html.form.multiselect.select-all')
        </div>

        <div class="multiselect-options">
            @include('platform::html.form.multiselect.options', ['template' => 'simple-option'])
        </div>
    </div>
</multiselect>
